2025-07-19 18:20:32,803 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-19 18:20:32,804 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-19 18:20:32,805 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-19 18:20:32,805 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-19 18:20:32,806 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-19 18:20:32,806 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-19 18:20:32,806 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-19 18:20:32,807 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-19 18:20:32,807 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-19 18:20:32,807 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-19 18:20:32,808 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-19 18:20:32,808 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-19 18:20:32,808 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-19 18:20:34,882 - __main__ - INFO - Existing processes terminated
2025-07-19 18:20:37,267 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-19 18:20:37,268 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-19 18:20:37,268 - utils.global_values_db - INFO - Using global values from config.py
2025-07-19 18:20:37,268 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-19 18:20:37,270 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-19 18:20:37,271 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-19 18:20:37,317 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-19 18:20:37,839 - utils.enhanced_screenshot_manager - WARNING - Could not initialize default screenshots directory: cannot import name 'app' from partially initialized module 'app' (most likely due to a circular import) (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py)
2025-07-19 18:20:37,843 - utils.unified_health_monitor - INFO - Registered recovery callback for platform: ios
2025-07-19 18:20:37,843 - utils.unified_health_monitor - INFO - Registered recovery callback for platform: android
2025-07-19 18:20:37,843 - utils.unified_health_monitor - INFO - Health monitor worker started
2025-07-19 18:20:37,843 - utils.unified_health_monitor - INFO - Unified health monitoring started
2025-07-19 18:20:37,843 - utils.unified_session_manager - INFO - Unified session manager initialized
2025-07-19 18:20:37,851 - utils.session_pool - INFO - Session pool cleanup worker started
2025-07-19 18:20:37,883 - utils.session_pool - INFO - Resource monitoring started
2025-07-19 18:20:37,991 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-19 18:20:37,992 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-19 18:20:37,992 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-19 18:20:37,992 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-19 18:20:37,993 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-19 18:20:37,994 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-19 18:20:37,994 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-19 18:20:37,994 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-19 18:20:37,994 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-19 18:20:37,994 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-19 18:20:37,994 - utils.database - INFO - Database initialized successfully
2025-07-19 18:20:37,994 - utils.database - INFO - Checking initial database state...
2025-07-19 18:20:38,023 - utils.database - INFO - Database state: 0 suites, 0 cases, 11385 steps, 1 screenshots, 1532 tracking entries
2025-07-19 18:20:38,043 - utils.unified_health_monitor - WARNING - Health monitoring is already active
Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 141, in <module>
    logger.info("Enhanced session management initialized successfully")
    ^^^^^^
NameError: name 'logger' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/run.py", line 257, in <module>
    import app as flask_app
  File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 144, in <module>
    logger.error(f"Failed to initialize enhanced session management: {e}")
    ^^^^^^
NameError: name 'logger' is not defined
