"""
Enhanced session router with improved stability and recovery mechanisms for Android
"""
import logging
import requests
import time
import threading
from unified_device_discovery import get_device_platform
from session_stability_fix import apply_session_management

# Import the original session router
from session_router import SessionRouter as OriginalSessionRouter

logger = logging.getLogger(__name__)

class EnhancedSessionRouter(OriginalSessionRouter):
    """Enhanced session router with improved stability and recovery mechanisms for Android devices"""
    
    def __init__(self):
        """Initialize the enhanced session router"""
        super().__init__()
        self.active_sessions = {}
        self.session_lock = threading.RLock()
        self.session_health_monitor = SessionHealthMonitor(self)
        self.session_health_monitor.start()
    
    def start_session(self, device_id, session_config=None):
        """
        Start a session with enhanced stability for Android devices
        
        Args:
            device_id (str): Device ID
            session_config (dict): Session configuration
            
        Returns:
            dict: Session start response with enhanced stability info
        """
        with self.session_lock:
            # Verify this is an Android device
            platform = get_device_platform(device_id)
            if platform != 'Android':
                logger.info(f"Device {device_id} is not Android, using standard session management")
                return super().start_session(device_id, session_config)
            
            # Check if session already exists
            if device_id in self.active_sessions:
                logger.info(f"Android session already exists for device {device_id}, reusing")
                return {
                    'status_code': 200,
                    'data': {
                        'success': True,
                        'message': 'Android session already active',
                        'device_id': device_id,
                        'reused': True
                    }
                }
            
            # Prepare enhanced Android session configuration
            if session_config is None:
                session_config = {}
            
            # Add Android-specific stability enhancements
            android_enhancements = {
                'uiautomator2ServerLaunchTimeout': 120000,  # 2 minutes
                'uiautomator2ServerInstallTimeout': 120000,  # 2 minutes
                'adbExecTimeout': 120000,                   # 2 minutes
                'newCommandTimeout': 900,                   # 15 minutes
                'skipServerInstallation': False,            # Always install fresh server
                'skipDeviceInitialization': False,          # Don't skip initialization
                'ignoreHiddenApiPolicyError': True,         # Ignore policy errors
                'disableWindowAnimation': True,             # Disable animations for stability
                'autoGrantPermissions': True,               # Auto grant permissions
                'dontStopAppOnReset': True,                 # Don't stop app on reset
                'unicodeKeyboard': True,                    # Use Unicode keyboard
                'resetKeyboard': True,                      # Reset keyboard after tests
                'skipLogcatCapture': True,                  # Skip logcat for performance
                'enforceXPath1': True,                      # Use XPath 1.0 for compatibility
                'eventTimings': True,                       # Enable event timings
                'printPageSourceOnFindFailure': False,      # Don't print page source on failure
                'shouldTerminateApp': False,                # Don't terminate app
                'forceAppLaunch': False,                    # Don't force app launch
                'clearSystemFiles': True,                   # Clear system files
                'skipUnlock': True                          # Skip unlock
            }
            
            # Merge with user-provided config, preserving user settings
            for key, value in android_enhancements.items():
                if key not in session_config:
                    session_config[key] = value
            
            # Add stability marker
            session_config['enhanced_stability'] = True
            
            # Start new session using parent method with enhanced config
            logger.info(f"Starting enhanced Android session for device {device_id}")
            response = super().start_session(device_id, session_config)
            
            # If successful, track the session
            if response.get('status_code') == 200 and response.get('data', {}).get('success'):
                self.active_sessions[device_id] = {
                    'platform': 'Android',
                    'start_time': time.time(),
                    'last_activity': time.time(),
                    'health_checks': 0,
                    'recovery_attempts': 0,
                    'backend': self.backends.get('Android', {}).get('name', 'Android Backend'),
                    'enhanced': True
                }
                logger.info(f"Started new enhanced session for Android device {device_id}")
            
            return response
    
    def stop_session(self, device_id):
        """
        Stop a session with proper cleanup
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Session stop response
        """
        with self.session_lock:
            # Remove from active sessions regardless of stop result
            if device_id in self.active_sessions:
                logger.info(f"Removing session tracking for device {device_id}")
                del self.active_sessions[device_id]
            
            # Stop the session using parent method
            return super().stop_session(device_id)
    
    def execute_action(self, device_id, action_data):
        """
        Execute an action with session recovery
        
        Args:
            device_id (str): Device ID
            action_data (dict): Action configuration
            
        Returns:
            dict: Action execution response
        """
        with self.session_lock:
            # Update last activity time
            if device_id in self.active_sessions:
                self.active_sessions[device_id]['last_activity'] = time.time()
        
        # Execute action using parent method
        response = super().execute_action(device_id, action_data)
        
        # Check for session errors
        if self._is_session_error(response):
            logger.warning(f"Session error detected for device {device_id}, attempting recovery")
            recovery_result = self._recover_session(device_id)
            
            if recovery_result.get('success'):
                logger.info(f"Session recovery successful for device {device_id}, retrying action")
                # Retry the action after recovery
                return super().execute_action(device_id, action_data)
            else:
                logger.error(f"Session recovery failed for device {device_id}")
        
        return response
    
    def _is_session_error(self, response):
        """
        Check if a response indicates a session error
        
        Args:
            response (dict): Response from backend
            
        Returns:
            bool: True if session error, False otherwise
        """
        # Check for common session error indicators
        if response.get('status_code') in [500, 404]:
            error_msg = str(response.get('error', '')).lower()
            data_error = str(response.get('data', {}).get('error', '')).lower()
            
            session_error_indicators = [
                'session not found', 'invalid session id', 'no such session',
                'session is either terminated', 'session has been terminated',
                'session does not exist', 'nosuchdriver'
            ]
            
            for indicator in session_error_indicators:
                if indicator in error_msg or indicator in data_error:
                    return True
        
        return False
    
    def _recover_session(self, device_id):
        """
        Attempt to recover a failed session
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Recovery result
        """
        with self.session_lock:
            if device_id not in self.active_sessions:
                return {'success': False, 'error': 'No active session to recover'}
            
            session_info = self.active_sessions[device_id]
            platform = session_info['platform']
            
            # Increment recovery attempts
            session_info['recovery_attempts'] += 1
            
            # If too many recovery attempts, give up
            if session_info['recovery_attempts'] > 3:
                logger.error(f"Too many recovery attempts for device {device_id}")
                return {'success': False, 'error': 'Too many recovery attempts'}
            
            logger.info(f"Attempting session recovery for {platform} device {device_id} (attempt {session_info['recovery_attempts']})")
            
            try:
                # Force stop any existing session
                self.route_request(
                    device_id=device_id,
                    endpoint='/api/disconnect',
                    method='POST',
                    data={'device_id': device_id, 'force': True}
                )
                
                # Wait a moment for cleanup
                time.sleep(2)
                
                # Start a new session
                response = self.route_request(
                    device_id=device_id,
                    endpoint='/api/connect',
                    method='POST',
                    data={
                        'device_id': device_id,
                        'device_type': platform,
                        'recovery': True
                    }
                )
                
                if response.get('status_code') == 200:
                    logger.info(f"Session recovery successful for device {device_id}")
                    # Reset recovery attempts on success
                    session_info['recovery_attempts'] = 0
                    session_info['last_activity'] = time.time()
                    return {'success': True}
                else:
                    logger.error(f"Session recovery failed for device {device_id}: {response}")
                    return {'success': False, 'error': str(response)}
                    
            except Exception as e:
                logger.error(f"Error during session recovery for device {device_id}: {e}")
                return {'success': False, 'error': str(e)}
    
    def check_session_health(self, device_id):
        """
        Check health of a session
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Health status
        """
        try:
            with self.session_lock:
                if device_id not in self.active_sessions:
                    return {'healthy': False, 'error': 'No active session'}
                
                session_info = self.active_sessions[device_id]
                platform = session_info['platform']
                
                # Increment health check count
                session_info['health_checks'] += 1
            
            # Simple health check via screenshot endpoint
            response = self.route_request(
                device_id=device_id,
                endpoint='/api/screenshot',
                method='GET'
            )
            
            healthy = response.get('status_code') == 200
            
            with self.session_lock:
                if device_id in self.active_sessions:
                    if healthy:
                        # Update last activity time on successful health check
                        self.active_sessions[device_id]['last_activity'] = time.time()
                    else:
                        logger.warning(f"Health check failed for device {device_id}")
            
            return {
                'healthy': healthy,
                'device_id': device_id,
                'platform': platform,
                'last_activity': session_info['last_activity'],
                'health_checks': session_info['health_checks'],
                'recovery_attempts': session_info['recovery_attempts']
            }
            
        except Exception as e:
            logger.error(f"Error checking session health for device {device_id}: {e}")
            return {'healthy': False, 'error': str(e)}
    
    def get_all_session_health(self):
        """
        Check health of all active sessions
        
        Returns:
            dict: Health status for all sessions
        """
        health_status = {}
        
        with self.session_lock:
            for device_id in self.active_sessions:
                health_status[device_id] = self.check_session_health(device_id)
        
        return health_status
    
    def get_session_stats(self):
        """
        Get statistics for all active sessions
        
        Returns:
            dict: Session statistics
        """
        with self.session_lock:
            stats = {
                'active_sessions': len(self.active_sessions),
                'by_platform': {
                    'iOS': 0,
                    'Android': 0
                },
                'sessions': {}
            }
            
            for device_id, session_info in self.active_sessions.items():
                platform = session_info['platform']
                stats['by_platform'][platform] = stats['by_platform'].get(platform, 0) + 1
                
                # Calculate session duration
                duration = time.time() - session_info['start_time']
                idle_time = time.time() - session_info['last_activity']
                
                stats['sessions'][device_id] = {
                    'platform': platform,
                    'backend': session_info['backend'],
                    'duration': f"{duration:.1f}s",
                    'idle_time': f"{idle_time:.1f}s",
                    'health_checks': session_info['health_checks'],
                    'recovery_attempts': session_info['recovery_attempts']
                }
            
            return stats


class SessionHealthMonitor:
    """Background monitor for session health"""
    
    def __init__(self, router, check_interval=60):
        """
        Initialize the session health monitor
        
        Args:
            router (EnhancedSessionRouter): The session router to monitor
            check_interval (int): Interval between health checks in seconds
        """
        self.router = router
        self.check_interval = check_interval
        self.monitor_thread = None
        self.monitoring_active = False
    
    def start(self):
        """Start the health monitoring thread"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            logger.info("Session health monitoring started")
    
    def stop(self):
        """Stop the health monitoring thread"""
        self.monitoring_active = False
        if self.monitor_thread:
            # Let the thread terminate naturally
            logger.info("Session health monitoring stopping")
            self.monitor_thread = None
    
    def _monitor_worker(self):
        """Background worker that monitors session health"""
        logger.info("Session health monitor worker started")
        
        while self.monitoring_active:
            try:
                # Sleep first to avoid immediate health check after start
                time.sleep(self.check_interval)
                
                if not self.monitoring_active:
                    break
                
                # Check all sessions and recover if needed
                self._check_all_sessions()
                
            except Exception as e:
                logger.error(f"Error in session health monitor: {e}")
        
        logger.info("Session health monitor worker stopped")
    
    def _check_all_sessions(self):
        """Check health of all active sessions and recover if needed"""
        try:
            with self.router.session_lock:
                # Make a copy of device IDs to avoid modification during iteration
                device_ids = list(self.router.active_sessions.keys())
            
            for device_id in device_ids:
                try:
                    # Check session health
                    health_status = self.router.check_session_health(device_id)
                    
                    # If unhealthy, attempt recovery
                    if not health_status.get('healthy', False):
                        logger.warning(f"Unhealthy session detected for device {device_id}, attempting recovery")
                        self.router._recover_session(device_id)
                        
                except Exception as e:
                    logger.error(f"Error checking/recovering session for device {device_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error checking all sessions: {e}")


# Replace the original SessionRouter with our enhanced version
def apply_session_router_enhancement():
    """Apply session router enhancement by patching the original module"""
    import sys
    import session_router
    
    # Create enhanced router instance
    enhanced_router = EnhancedSessionRouter()
    
    # Replace the global instance in the original module
    session_router.session_router = enhanced_router
    
    # Also replace the class for any future instantiations
    session_router.SessionRouter = EnhancedSessionRouter
    
    logger.info("Session router enhanced with improved stability features")
    
    return enhanced_router
"""