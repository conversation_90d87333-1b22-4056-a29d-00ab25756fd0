"""
Enhanced Android Session Stability Module for Mobile Automation Tool

This module provides robust session management capabilities to prevent crashes
and ensure stable Appium connections specifically for Android devices using UiAutomator2.
"""

import os
import time
import logging
import threading
import traceback
import requests
from functools import wraps

# Configure logging
logger = logging.getLogger("session_stability")
logger.setLevel(logging.INFO)

class SessionManager:
    """
    Manages Android Appium sessions with robust error handling and recovery mechanisms.
    Works as a wrapper around existing Android AppiumDeviceController implementation.
    """
    
    def __init__(self, controller=None, platform="Android"):
        """
        Initialize the Android session manager
        
        Args:
            controller: The Android AppiumDeviceController instance to manage
            platform (str): Should be 'Android' for this implementation
        """
        self.controller = controller
        self.platform = "Android"  # Force Android platform
        self.device_id = None
        self.session_lock = threading.RLock()  # Reentrant lock for thread safety
        self.last_activity_time = time.time()
        self.health_check_interval = 30  # Seconds between health checks
        self.recovery_attempts = 0
        self.max_recovery_attempts = 3
        self.monitoring_thread = None
        self.monitoring_active = False
        self.session_id = None
        self.connection_options = None
        
        # Session health metrics
        self.health_check_count = 0
        self.health_check_failures = 0
        self.recovery_success_count = 0
        self.recovery_failure_count = 0
        
        # Android-specific settings
        self.uiautomator2_server_check = True  # Check UiAutomator2 server status
        self.adb_connection_check = True       # Check ADB connection status
    
    def start_monitoring(self):
        """Start session monitoring in a background thread"""
        if not self.monitoring_active and self.controller:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(
                target=self._session_monitor_worker,
                daemon=True
            )
            self.monitoring_thread.start()
            logger.info(f"Session monitoring started for {self.platform} device: {self.device_id}")
    
    def stop_monitoring(self):
        """Stop session monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            # Let the thread terminate naturally
            logger.info(f"Session monitoring stopping for {self.platform} device: {self.device_id}")
            self.monitoring_thread = None
    
    def _session_monitor_worker(self):
        """Background worker that monitors session health"""
        logger.info(f"Session monitor worker started for {self.platform}")
        
        while self.monitoring_active and self.controller:
            try:
                # Sleep first to avoid immediate health check after connection
                time.sleep(self.health_check_interval)
                
                if not self.monitoring_active:
                    break
                
                # Check if session is healthy
                if not self._check_session_health():
                    logger.warning(f"Session health check failed for {self.platform} device: {self.device_id}")
                    self._attempt_recovery()
                else:
                    # Reset recovery attempts counter on successful health check
                    self.recovery_attempts = 0
                    
            except Exception as e:
                logger.error(f"Error in session monitor: {e}")
                # Don't attempt recovery here, wait for next cycle
        
        logger.info(f"Session monitor worker stopped for {self.platform}")
    
    def _check_session_health(self):
        """
        Check if the current Android session is healthy
        
        Returns:
            bool: True if session is healthy, False otherwise
        """
        if not self.controller or not self.controller.driver:
            return False
        
        try:
            with self.session_lock:
                self.health_check_count += 1
                
                # Store current session ID for comparison
                current_session_id = getattr(self.controller.driver, 'session_id', None)
                
                # Basic check: session ID exists and matches stored ID
                if not current_session_id:
                    logger.debug("Health check failed: No session ID")
                    self.health_check_failures += 1
                    return False
                
                # If we have a stored session ID, compare it
                if self.session_id and self.session_id != current_session_id:
                    logger.warning(f"Session ID changed: {self.session_id} -> {current_session_id}")
                    self.session_id = current_session_id
                
                # Store session ID if not already stored
                if not self.session_id:
                    self.session_id = current_session_id
                
                # Perform Android-specific health checks
                try:
                    # Test 1: Get window size (very lightweight)
                    size = self.controller.driver.get_window_size()
                    if not size or 'width' not in size or 'height' not in size:
                        logger.debug("Health check failed: Invalid window size")
                        self.health_check_failures += 1
                        return False
                    
                    # Test 2: Check current activity (Android-specific)
                    try:
                        current_activity = self.controller.driver.current_activity
                        if not current_activity:
                            logger.debug("Health check warning: No current activity")
                            # Don't fail just on this, as some apps might not report activity correctly
                    except Exception as act_err:
                        logger.debug(f"Health check warning: Activity check failed - {act_err}")
                        # Don't fail just on this
                    
                    # Test 3: Check UiAutomator2 server status if enabled
                    if self.uiautomator2_server_check and hasattr(self.controller, 'uiautomator2_helper'):
                        try:
                            if self.controller.uiautomator2_helper and not self._check_uiautomator2_server():
                                logger.warning("Health check warning: UiAutomator2 server not responsive")
                                # This is a more serious issue but still don't fail immediately
                        except Exception as ui2_err:
                            logger.debug(f"Health check warning: UiAutomator2 check failed - {ui2_err}")
                    
                    # Test 4: Simple page source access (more thorough but still lightweight)
                    try:
                        source = self.controller.driver.page_source
                        if not source or not isinstance(source, str):
                            logger.debug("Health check warning: Invalid page source type")
                            # Don't fail just on this
                    except Exception as ps_err:
                        # Page source errors are common but not always fatal for Android
                        logger.debug(f"Health check warning: Page source error - {ps_err}")
                        # Don't fail just on this
                    
                    # Update last activity time on successful health check
                    self.last_activity_time = time.time()
                    logger.debug(f"Health check passed for Android device: {self.device_id}")
                    return True
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    
                    # Check for Android-specific session termination indicators
                    if any(err in error_msg for err in [
                        "nosuchdriver", "no such session", "session is either terminated",
                        "session not found", "invalid session id", "uiautomator not responding",
                        "uiautomatoraborted", "uiautomator died", "connection refused"
                    ]):
                        logger.warning(f"Android session terminated: {e}")
                        self.health_check_failures += 1
                        return False
                    else:
                        logger.warning(f"Health check operation failed: {e}")
                        self.health_check_failures += 1
                        return False
                        
        except Exception as e:
            logger.error(f"Health check error: {e}")
            self.health_check_failures += 1
            return False
    
    def _check_uiautomator2_server(self):
        """
        Check if UiAutomator2 server is responsive
        
        Returns:
            bool: True if server is responsive, False otherwise
        """
        try:
            # Check if we have a UIAutomator2 helper
            if not hasattr(self.controller, 'uiautomator2_helper') or not self.controller.uiautomator2_helper:
                return True  # Skip check if helper not available
            
            # Try to execute a simple shell command via ADB
            if hasattr(self.controller.uiautomator2_helper, 'shell'):
                result = self.controller.uiautomator2_helper.shell('echo "UiAutomator2 test"')
                return result and "UiAutomator2 test" in result
            
            return True  # Default to true if we can't check
        except Exception as e:
            logger.debug(f"UiAutomator2 server check failed: {e}")
            return False
    
    def _attempt_recovery(self):
        """
        Attempt to recover a failed Android session with enhanced recovery strategies
        
        Returns:
            bool: True if recovery successful, False otherwise
        """
        if not self.controller:
            return False
            
        # Check if we've exceeded max recovery attempts
        if self.recovery_attempts >= self.max_recovery_attempts:
            logger.error(f"Max recovery attempts ({self.max_recovery_attempts}) reached for Android device: {self.device_id}")
            self.recovery_failure_count += 1
            return False
            
        self.recovery_attempts += 1
        logger.info(f"Attempting Android session recovery ({self.recovery_attempts}/{self.max_recovery_attempts}) for device: {self.device_id}")
        
        try:
            with self.session_lock:
                # Strategy 1: Clean up existing driver
                if self.controller.driver:
                    try:
                        logger.info("Recovery step 1: Quitting existing driver")
                        self.controller.driver.quit()
                    except Exception as e:
                        logger.debug(f"Error quitting driver during recovery: {e}")
                    finally:
                        self.controller.driver = None
                
                # Strategy 2: Check UiAutomator2 server status and reset if needed
                if self._should_reset_uiautomator2():
                    logger.info("Recovery step 2: Resetting UiAutomator2 server")
                    self._reset_uiautomator2_server()
                    # Wait for UiAutomator2 server to fully stop
                    time.sleep(3)
                
                # Strategy 3: Attempt reconnection with enhanced capabilities
                if self.device_id:
                    logger.info("Recovery step 3: Reconnecting with enhanced capabilities")
                    
                    # Create enhanced connection options
                    enhanced_options = self.connection_options.copy() if self.connection_options else {}
                    
                    # Add Android-specific stability enhancements
                    android_enhancements = {
                        'uiautomator2ServerLaunchTimeout': 120000,  # 2 minutes
                        'uiautomator2ServerInstallTimeout': 120000,  # 2 minutes
                        'adbExecTimeout': 120000,                   # 2 minutes
                        'newCommandTimeout': 900,                   # 15 minutes
                        'skipServerInstallation': False,            # Always install fresh server
                        'skipDeviceInitialization': False,          # Don't skip initialization
                        'ignoreHiddenApiPolicyError': True,         # Ignore policy errors
                        'disableWindowAnimation': True,             # Disable animations for stability
                        'autoGrantPermissions': True,               # Auto grant permissions
                        'dontStopAppOnReset': True,                 # Don't stop app on reset
                        'unicodeKeyboard': True,                    # Use Unicode keyboard
                        'resetKeyboard': True,                      # Reset keyboard after tests
                        'skipLogcatCapture': True,                  # Skip logcat for performance
                        'enforceXPath1': True,                      # Use XPath 1.0 for compatibility
                        'eventTimings': True,                       # Enable event timings
                        'printPageSourceOnFindFailure': False,      # Don't print page source on failure
                        'shouldTerminateApp': False,                # Don't terminate app
                        'forceAppLaunch': False,                    # Don't force app launch
                        'clearSystemFiles': True,                   # Clear system files
                        'skipUnlock': True,                         # Skip unlock
                        'recovery_attempt': self.recovery_attempts  # Track recovery attempts
                    }
                    
                    # Merge with existing options, preserving user settings
                    for key, value in android_enhancements.items():
                        if key not in enhanced_options:
                            enhanced_options[key] = value
                    
                    # Attempt reconnection with enhanced options
                    if self.controller.connect_to_device(
                        self.device_id, 
                        enhanced_options,
                        self.platform
                    ):
                        logger.info(f"Session recovery successful for Android device: {self.device_id}")
                        self.last_activity_time = time.time()
                        self.session_id = getattr(self.controller.driver, 'session_id', None)
                        self.recovery_success_count += 1
                        return True
                
                logger.error(f"Session recovery failed for Android device: {self.device_id}")
                self.recovery_failure_count += 1
                return False
                
        except Exception as e:
            logger.error(f"Error during Android session recovery: {e}")
            self.recovery_failure_count += 1
            return False
    
    def _should_reset_uiautomator2(self):
        """
        Determine if UiAutomator2 server should be reset
        
        Returns:
            bool: True if UiAutomator2 server should be reset
        """
        # Reset on second recovery attempt or if explicitly requested
        return self.recovery_attempts >= 2
    
    def _reset_uiautomator2_server(self):
        """
        Reset UiAutomator2 server using ADB commands
        
        Returns:
            bool: True if reset successful, False otherwise
        """
        try:
            if not self.device_id:
                return False
                
            # Check if we have a UIAutomator2 helper
            if hasattr(self.controller, 'uiautomator2_helper') and self.controller.uiautomator2_helper:
                logger.info(f"Resetting UiAutomator2 server for device {self.device_id}")
                
                try:
                    # Stop UiAutomator2 server
                    self.controller.uiautomator2_helper.shell('am force-stop io.appium.uiautomator2.server')
                    self.controller.uiautomator2_helper.shell('am force-stop io.appium.uiautomator2.server.test')
                    
                    # Clear app data
                    self.controller.uiautomator2_helper.shell('pm clear io.appium.uiautomator2.server')
                    self.controller.uiautomator2_helper.shell('pm clear io.appium.uiautomator2.server.test')
                    
                    # Kill any zombie UiAutomator processes
                    self.controller.uiautomator2_helper.shell("ps | grep uiautomator | awk '{print $2}' | xargs kill -9")
                    
                    logger.info("UiAutomator2 server reset successfully")
                    return True
                except Exception as e:
                    logger.error(f"Error resetting UiAutomator2 server: {e}")
            
            # Fallback: Try using the restart_wda.sh script (which handles Android UiAutomator2 despite the name)
            try:
                import subprocess
                logger.info(f"Attempting to reset UiAutomator2 server using restart_wda.sh script")
                result = subprocess.run(
                    ['bash', 'restart_wda.sh', self.device_id],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                logger.info(f"Script output: {result.stdout}")
                if result.returncode == 0:
                    logger.info("UiAutomator2 server reset successfully using script")
                    return True
                else:
                    logger.warning(f"Script failed with return code {result.returncode}: {result.stderr}")
            except Exception as script_err:
                logger.error(f"Error running reset script: {script_err}")
            
            return False
        except Exception as e:
            logger.error(f"Error in _reset_uiautomator2_server: {e}")
            return False
    
    def wrap_session_operation(self, func):
        """
        Decorator to wrap session operations with error handling and recovery
        
        Args:
            func: The function to wrap
            
        Returns:
            function: Wrapped function with session error handling
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            max_retries = 2  # Maximum number of retries for operations
            
            for attempt in range(max_retries + 1):
                try:
                    # Update last activity time before operation
                    self.last_activity_time = time.time()
                    
                    # Execute the operation
                    result = func(*args, **kwargs)
                    
                    # Update last activity time after successful operation
                    self.last_activity_time = time.time()
                    return result
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    
                    # Check for session termination indicators
                    if any(err in error_msg for err in [
                        "nosuchdriver", "no such session", "session is either terminated",
                        "session not found", "invalid session id"
                    ]):
                        logger.warning(f"Session error during operation: {e}")
                        
                        # Last attempt, don't try recovery
                        if attempt == max_retries:
                            logger.error(f"Operation failed after {max_retries} attempts")
                            raise
                        
                        # Attempt recovery before retry
                        if self._attempt_recovery():
                            logger.info(f"Recovery successful, retrying operation (attempt {attempt+1}/{max_retries})")
                        else:
                            logger.error("Recovery failed, operation will likely fail")
                    else:
                        # For non-session errors, don't retry
                        logger.error(f"Non-session error during operation: {e}")
                        raise
            
            # This should not be reached due to the raise in the loop
            raise Exception("Operation failed after maximum retries")
        
        return wrapper
    
    def connect(self, device_id, connection_options=None, platform=None):
        """
        Connect to a device with session management
        
        Args:
            device_id (str): Device ID to connect to
            connection_options (dict): Connection options
            platform (str): Platform name ('iOS' or 'Android')
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        if not self.controller:
            return False
            
        try:
            with self.session_lock:
                # Store connection parameters for recovery
                self.device_id = device_id
                self.connection_options = connection_options
                if platform:
                    self.platform = platform
                
                # Connect to device
                result = self.controller.connect_to_device(device_id, connection_options, platform)
                
                if result:
                    # Store session ID for monitoring
                    self.session_id = getattr(self.controller.driver, 'session_id', None)
                    self.last_activity_time = time.time()
                    self.recovery_attempts = 0
                    
                    # Start monitoring
                    self.start_monitoring()
                
                return result
                
        except Exception as e:
            logger.error(f"Error connecting to device: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the device and clean up resources
        
        Returns:
            bool: True if disconnection successful, False otherwise
        """
        try:
            # Stop monitoring first
            self.stop_monitoring()
            
            with self.session_lock:
                if self.controller and self.controller.driver:
                    try:
                        self.controller.driver.quit()
                    except Exception as e:
                        logger.debug(f"Error quitting driver during disconnect: {e}")
                    finally:
                        self.controller.driver = None
                
                # Clear session data
                self.session_id = None
                self.last_activity_time = time.time()
                self.recovery_attempts = 0
                
                return True
                
        except Exception as e:
            logger.error(f"Error disconnecting from device: {e}")
            return False
    
    def get_session_metrics(self):
        """
        Get session health metrics
        
        Returns:
            dict: Session health metrics
        """
        return {
            "platform": self.platform,
            "device_id": self.device_id,
            "session_id": self.session_id,
            "monitoring_active": self.monitoring_active,
            "health_checks": {
                "total": self.health_check_count,
                "failures": self.health_check_failures,
                "success_rate": f"{(1 - self.health_check_failures / max(1, self.health_check_count)) * 100:.1f}%"
            },
            "recovery": {
                "attempts": self.recovery_attempts,
                "successes": self.recovery_success_count,
                "failures": self.recovery_failure_count,
                "success_rate": f"{(self.recovery_success_count / max(1, self.recovery_success_count + self.recovery_failure_count)) * 100:.1f}%"
            },
            "last_activity": self.last_activity_time
        }


class SessionManagerFactory:
    """Factory for creating and managing SessionManager instances"""
    
    def __init__(self):
        self.session_managers = {}
    
    def get_session_manager(self, controller, platform="unknown"):
        """
        Get or create a SessionManager for a controller
        
        Args:
            controller: The AppiumDeviceController instance
            platform (str): Platform name ('iOS' or 'Android')
            
        Returns:
            SessionManager: The session manager instance
        """
        # Use controller object ID as key
        controller_id = id(controller)
        
        if controller_id not in self.session_managers:
            self.session_managers[controller_id] = SessionManager(controller, platform)
            
        return self.session_managers[controller_id]
    
    def remove_session_manager(self, controller):
        """
        Remove a SessionManager for a controller
        
        Args:
            controller: The AppiumDeviceController instance
        """
        controller_id = id(controller)
        
        if controller_id in self.session_managers:
            # Stop monitoring before removing
            self.session_managers[controller_id].stop_monitoring()
            del self.session_managers[controller_id]


# Global factory instance
session_manager_factory = SessionManagerFactory()


def with_session_management(func):
    """
    Decorator to add session management to AppiumDeviceController methods
    
    Args:
        func: The function to wrap
        
    Returns:
        function: Wrapped function with session management
    """
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        # Get session manager for this controller
        platform = getattr(self, 'platform_name', 'unknown')
        session_manager = session_manager_factory.get_session_manager(self, platform)
        
        # Wrap the function with session error handling
        wrapped_func = session_manager.wrap_session_operation(func)
        
        # Call the wrapped function
        return wrapped_func(self, *args, **kwargs)
    
    return wrapper


def apply_session_management(controller_class):
    """
    Apply session management to an AppiumDeviceController class
    
    Args:
        controller_class: The AppiumDeviceController class to enhance
        
    Returns:
        class: Enhanced controller class with session management
    """
    # List of methods to wrap with session management
    methods_to_wrap = [
        'tap', 'swipe', 'get_screenshot', 'find_element', 'find_elements',
        'get_page_source', 'execute_script', 'get_element_rect',
        'tap_by_coordinates', 'long_press', 'send_keys', 'clear_text',
        'is_element_present', 'wait_for_element', 'wait_for_element_to_disappear'
    ]
    
    # Wrap each method with session management
    for method_name in methods_to_wrap:
        if hasattr(controller_class, method_name):
            original_method = getattr(controller_class, method_name)
            wrapped_method = with_session_management(original_method)
            setattr(controller_class, method_name, wrapped_method)
    
    # Enhance connect_to_device method
    if hasattr(controller_class, 'connect_to_device'):
        original_connect = getattr(controller_class, 'connect_to_device')
        
        @wraps(original_connect)
        def enhanced_connect(self, device_id, options=None, platform_hint=None):
            # Get session manager
            platform = platform_hint or getattr(self, 'platform_name', 'unknown')
            session_manager = session_manager_factory.get_session_manager(self, platform)
            
            # Use session manager's connect method
            return session_manager.connect(device_id, options, platform)
        
        setattr(controller_class, 'connect_to_device', enhanced_connect)
    
    # Enhance disconnect method
    if hasattr(controller_class, 'disconnect'):
        original_disconnect = getattr(controller_class, 'disconnect')
        
        @wraps(original_disconnect)
        def enhanced_disconnect(self):
            # Get session manager
            platform = getattr(self, 'platform_name', 'unknown')
            session_manager = session_manager_factory.get_session_manager(self, platform)
            
            # Use session manager's disconnect method
            result = session_manager.disconnect()
            
            # Remove session manager
            session_manager_factory.remove_session_manager(self)
            
            return result
        
        setattr(controller_class, 'disconnect', enhanced_disconnect)
    
    return controller_class
"""