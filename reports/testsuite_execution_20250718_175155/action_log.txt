Action Log - 2025-07-18 19:04:31
================================================================================

[[19:04:31]] [INFO] Generating execution report...
[[19:04:31]] [WARNING] 4 tests failed.
[[19:04:31]] [SUCCESS] Screenshot refreshed
[[19:04:31]] [INFO] Refreshing screenshot...
[[19:04:30]] [SUCCESS] Screenshot refreshed
[[19:04:30]] [INFO] Refreshing screenshot...
[[19:04:27]] [SUCCESS] Screenshot refreshed successfully
[[19:04:27]] [SUCCESS] Screenshot refreshed successfully
[[19:04:27]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[19:04:26]] [SUCCESS] Screenshot refreshed
[[19:04:26]] [INFO] Refreshing screenshot...
[[19:04:15]] [SUCCESS] Screenshot refreshed successfully
[[19:04:15]] [SUCCESS] Screenshot refreshed successfully
[[19:04:15]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[19:04:14]] [SUCCESS] Screenshot refreshed
[[19:04:14]] [INFO] Refreshing screenshot...
[[19:04:11]] [SUCCESS] Screenshot refreshed successfully
[[19:04:11]] [SUCCESS] Screenshot refreshed successfully
[[19:04:10]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[19:04:10]] [SUCCESS] Screenshot refreshed
[[19:04:10]] [INFO] Refreshing screenshot...
[[19:04:06]] [SUCCESS] Screenshot refreshed successfully
[[19:04:06]] [SUCCESS] Screenshot refreshed successfully
[[19:04:06]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:04:05]] [SUCCESS] Screenshot refreshed
[[19:04:05]] [INFO] Refreshing screenshot...
[[19:03:59]] [SUCCESS] Screenshot refreshed successfully
[[19:03:59]] [SUCCESS] Screenshot refreshed successfully
[[19:03:58]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[19:03:58]] [SUCCESS] Screenshot refreshed
[[19:03:58]] [INFO] Refreshing screenshot...
[[19:03:52]] [SUCCESS] Screenshot refreshed successfully
[[19:03:52]] [SUCCESS] Screenshot refreshed successfully
[[19:03:51]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[19:03:51]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[19:03:51]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[19:03:51]] [INFO] Ll4UlkE3L9=running
[[19:03:51]] [INFO] Executing action 590/590: cleanupSteps action
[[19:03:51]] [SUCCESS] Screenshot refreshed
[[19:03:51]] [INFO] Refreshing screenshot...
[[19:03:51]] [INFO] 25UEKPIknm=pass
[[19:03:48]] [SUCCESS] Screenshot refreshed successfully
[[19:03:48]] [SUCCESS] Screenshot refreshed successfully
[[19:03:47]] [INFO] 25UEKPIknm=running
[[19:03:47]] [INFO] Executing action 589/590: Terminate app: env[appid]
[[19:03:47]] [SUCCESS] Screenshot refreshed
[[19:03:47]] [INFO] Refreshing screenshot...
[[19:03:47]] [INFO] UqgDn5CuPY=pass
[[19:03:44]] [SUCCESS] Screenshot refreshed successfully
[[19:03:44]] [SUCCESS] Screenshot refreshed successfully
[[19:03:43]] [INFO] UqgDn5CuPY=running
[[19:03:43]] [INFO] Executing action 588/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[19:03:43]] [SUCCESS] Screenshot refreshed
[[19:03:43]] [INFO] Refreshing screenshot...
[[19:03:43]] [INFO] VfTTTtrliQ=pass
[[19:03:40]] [SUCCESS] Screenshot refreshed successfully
[[19:03:40]] [SUCCESS] Screenshot refreshed successfully
[[19:03:40]] [INFO] VfTTTtrliQ=running
[[19:03:40]] [INFO] Executing action 587/590: iOS Function: alert_accept
[[19:03:39]] [SUCCESS] Screenshot refreshed
[[19:03:39]] [INFO] Refreshing screenshot...
[[19:03:39]] [INFO] ipT2XD9io6=pass
[[19:03:35]] [SUCCESS] Screenshot refreshed successfully
[[19:03:35]] [SUCCESS] Screenshot refreshed successfully
[[19:03:35]] [INFO] ipT2XD9io6=running
[[19:03:35]] [INFO] Executing action 586/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[19:03:35]] [SUCCESS] Screenshot refreshed
[[19:03:35]] [INFO] Refreshing screenshot...
[[19:03:35]] [INFO] OKCHAK6HCJ=pass
[[19:03:31]] [SUCCESS] Screenshot refreshed successfully
[[19:03:31]] [SUCCESS] Screenshot refreshed successfully
[[19:03:30]] [INFO] OKCHAK6HCJ=running
[[19:03:30]] [INFO] Executing action 585/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:03:30]] [SUCCESS] Screenshot refreshed
[[19:03:30]] [INFO] Refreshing screenshot...
[[19:03:30]] [INFO] AEnFqnkOa1=pass
[[19:03:26]] [SUCCESS] Screenshot refreshed successfully
[[19:03:26]] [SUCCESS] Screenshot refreshed successfully
[[19:03:26]] [INFO] AEnFqnkOa1=running
[[19:03:26]] [INFO] Executing action 584/590: Tap on image: banner-close-updated.png
[[19:03:25]] [SUCCESS] Screenshot refreshed
[[19:03:25]] [INFO] Refreshing screenshot...
[[19:03:25]] [INFO] z1CfcW4xYT=pass
[[19:03:22]] [SUCCESS] Screenshot refreshed successfully
[[19:03:22]] [SUCCESS] Screenshot refreshed successfully
[[19:03:21]] [INFO] z1CfcW4xYT=running
[[19:03:21]] [INFO] Executing action 583/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:03:20]] [SUCCESS] Screenshot refreshed
[[19:03:20]] [INFO] Refreshing screenshot...
[[19:03:20]] [INFO] dJNRgTXoqs=pass
[[19:03:16]] [SUCCESS] Screenshot refreshed successfully
[[19:03:16]] [SUCCESS] Screenshot refreshed successfully
[[19:03:16]] [INFO] dJNRgTXoqs=running
[[19:03:16]] [INFO] Executing action 582/590: Swipe from (50%, 30%) to (50%, 70%)
[[19:03:15]] [SUCCESS] Screenshot refreshed
[[19:03:15]] [INFO] Refreshing screenshot...
[[19:03:15]] [INFO] ceF4VRTJlO=pass
[[19:03:11]] [SUCCESS] Screenshot refreshed successfully
[[19:03:11]] [SUCCESS] Screenshot refreshed successfully
[[19:03:11]] [INFO] ceF4VRTJlO=running
[[19:03:11]] [INFO] Executing action 581/590: Tap on image: banner-close-updated.png
[[19:03:11]] [SUCCESS] Screenshot refreshed
[[19:03:11]] [INFO] Refreshing screenshot...
[[19:03:11]] [INFO] 8hCPyY2zPt=pass
[[19:03:06]] [SUCCESS] Screenshot refreshed successfully
[[19:03:06]] [SUCCESS] Screenshot refreshed successfully
[[19:03:06]] [INFO] 8hCPyY2zPt=running
[[19:03:06]] [INFO] Executing action 580/590: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[19:03:06]] [SUCCESS] Screenshot refreshed
[[19:03:06]] [INFO] Refreshing screenshot...
[[19:03:06]] [INFO] r0FfJ85LFM=pass
[[19:03:02]] [SUCCESS] Screenshot refreshed successfully
[[19:03:02]] [SUCCESS] Screenshot refreshed successfully
[[19:03:02]] [INFO] r0FfJ85LFM=running
[[19:03:02]] [INFO] Executing action 579/590: Tap on image: banner-close-updated.png
[[19:03:01]] [SUCCESS] Screenshot refreshed
[[19:03:01]] [INFO] Refreshing screenshot...
[[19:03:01]] [INFO] 2QEdm5WM18=pass
[[19:02:57]] [SUCCESS] Screenshot refreshed successfully
[[19:02:57]] [SUCCESS] Screenshot refreshed successfully
[[19:02:57]] [INFO] 2QEdm5WM18=running
[[19:02:57]] [INFO] Executing action 578/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[19:02:56]] [SUCCESS] Screenshot refreshed
[[19:02:56]] [INFO] Refreshing screenshot...
[[19:02:56]] [INFO] NW6M15JbAy=pass
[[19:02:42]] [SUCCESS] Screenshot refreshed successfully
[[19:02:42]] [SUCCESS] Screenshot refreshed successfully
[[19:02:42]] [INFO] NW6M15JbAy=running
[[19:02:42]] [INFO] Executing action 577/590: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[19:02:42]] [SUCCESS] Screenshot refreshed
[[19:02:42]] [INFO] Refreshing screenshot...
[[19:02:42]] [INFO] njiHWyVooT=pass
[[19:02:38]] [SUCCESS] Screenshot refreshed successfully
[[19:02:38]] [SUCCESS] Screenshot refreshed successfully
[[19:02:38]] [INFO] njiHWyVooT=running
[[19:02:38]] [INFO] Executing action 576/590: Tap on image: banner-close-updated.png
[[19:02:37]] [SUCCESS] Screenshot refreshed
[[19:02:37]] [INFO] Refreshing screenshot...
[[19:02:37]] [INFO] 93bAew9Y4Y=pass
[[19:02:33]] [SUCCESS] Screenshot refreshed successfully
[[19:02:33]] [SUCCESS] Screenshot refreshed successfully
[[19:02:32]] [INFO] 93bAew9Y4Y=running
[[19:02:32]] [INFO] Executing action 575/590: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[19:02:32]] [SUCCESS] Screenshot refreshed
[[19:02:32]] [INFO] Refreshing screenshot...
[[19:02:32]] [INFO] rPQ5EkTza1=pass
[[19:02:28]] [SUCCESS] Screenshot refreshed successfully
[[19:02:28]] [SUCCESS] Screenshot refreshed successfully
[[19:02:27]] [INFO] rPQ5EkTza1=running
[[19:02:27]] [INFO] Executing action 574/590: Tap on Text: "Click"
[[19:02:27]] [SUCCESS] Screenshot refreshed
[[19:02:27]] [INFO] Refreshing screenshot...
[[19:02:26]] [SUCCESS] Screenshot refreshed
[[19:02:26]] [INFO] Refreshing screenshot...
[[19:02:22]] [SUCCESS] Screenshot refreshed successfully
[[19:02:22]] [SUCCESS] Screenshot refreshed successfully
[[19:02:22]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[19:02:22]] [SUCCESS] Screenshot refreshed
[[19:02:22]] [INFO] Refreshing screenshot...
[[19:02:10]] [SUCCESS] Screenshot refreshed successfully
[[19:02:10]] [SUCCESS] Screenshot refreshed successfully
[[19:02:10]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[19:02:09]] [SUCCESS] Screenshot refreshed
[[19:02:09]] [INFO] Refreshing screenshot...
[[19:02:02]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[19:02:02]] [SUCCESS] Screenshot refreshed successfully
[[19:02:02]] [SUCCESS] Screenshot refreshed successfully
[[19:02:02]] [SUCCESS] Screenshot refreshed
[[19:02:02]] [INFO] Refreshing screenshot...
[[19:01:58]] [SUCCESS] Screenshot refreshed successfully
[[19:01:58]] [SUCCESS] Screenshot refreshed successfully
[[19:01:58]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[19:01:57]] [SUCCESS] Screenshot refreshed
[[19:01:57]] [INFO] Refreshing screenshot...
[[19:01:53]] [SUCCESS] Screenshot refreshed successfully
[[19:01:53]] [SUCCESS] Screenshot refreshed successfully
[[19:01:53]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[19:01:52]] [SUCCESS] Screenshot refreshed
[[19:01:52]] [INFO] Refreshing screenshot...
[[19:01:48]] [SUCCESS] Screenshot refreshed successfully
[[19:01:48]] [SUCCESS] Screenshot refreshed successfully
[[19:01:48]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[19:01:48]] [SUCCESS] Screenshot refreshed
[[19:01:48]] [INFO] Refreshing screenshot...
[[19:01:43]] [SUCCESS] Screenshot refreshed successfully
[[19:01:43]] [SUCCESS] Screenshot refreshed successfully
[[19:01:43]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[19:01:43]] [SUCCESS] Screenshot refreshed
[[19:01:43]] [INFO] Refreshing screenshot...
[[19:01:35]] [SUCCESS] Screenshot refreshed successfully
[[19:01:35]] [SUCCESS] Screenshot refreshed successfully
[[19:01:35]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[19:01:35]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[19:01:35]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[19:01:35]] [INFO] 0YgZZfWdYY=running
[[19:01:35]] [INFO] Executing action 573/590: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[19:01:34]] [SUCCESS] Screenshot refreshed
[[19:01:34]] [INFO] Refreshing screenshot...
[[19:01:34]] [INFO] arH1CZCPXh=pass
[[19:01:29]] [SUCCESS] Screenshot refreshed successfully
[[19:01:29]] [SUCCESS] Screenshot refreshed successfully
[[19:01:29]] [INFO] arH1CZCPXh=running
[[19:01:29]] [INFO] Executing action 572/590: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[19:01:28]] [SUCCESS] Screenshot refreshed
[[19:01:28]] [INFO] Refreshing screenshot...
[[19:01:28]] [INFO] JLAJhxPdsl=pass
[[19:01:22]] [SUCCESS] Screenshot refreshed successfully
[[19:01:22]] [SUCCESS] Screenshot refreshed successfully
[[19:01:22]] [INFO] JLAJhxPdsl=running
[[19:01:22]] [INFO] Executing action 571/590: Tap on Text: "Cancel"
[[19:01:22]] [SUCCESS] Screenshot refreshed
[[19:01:22]] [INFO] Refreshing screenshot...
[[19:01:22]] [INFO] UqgDn5CuPY=pass
[[19:01:18]] [SUCCESS] Screenshot refreshed successfully
[[19:01:18]] [SUCCESS] Screenshot refreshed successfully
[[19:01:18]] [INFO] UqgDn5CuPY=running
[[19:01:18]] [INFO] Executing action 570/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[19:01:17]] [SUCCESS] Screenshot refreshed
[[19:01:17]] [INFO] Refreshing screenshot...
[[19:01:17]] [INFO] VfTTTtrliQ=pass
[[19:01:15]] [SUCCESS] Screenshot refreshed successfully
[[19:01:15]] [SUCCESS] Screenshot refreshed successfully
[[19:01:14]] [INFO] VfTTTtrliQ=running
[[19:01:14]] [INFO] Executing action 569/590: iOS Function: alert_accept
[[19:01:14]] [SUCCESS] Screenshot refreshed
[[19:01:14]] [INFO] Refreshing screenshot...
[[19:01:14]] [INFO] ipT2XD9io6=pass
[[19:01:10]] [SUCCESS] Screenshot refreshed successfully
[[19:01:10]] [SUCCESS] Screenshot refreshed successfully
[[19:01:09]] [INFO] ipT2XD9io6=running
[[19:01:09]] [INFO] Executing action 568/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[19:01:09]] [SUCCESS] Screenshot refreshed
[[19:01:09]] [INFO] Refreshing screenshot...
[[19:01:09]] [INFO] OKCHAK6HCJ=pass
[[19:01:04]] [SUCCESS] Screenshot refreshed successfully
[[19:01:04]] [SUCCESS] Screenshot refreshed successfully
[[19:01:04]] [INFO] OKCHAK6HCJ=running
[[19:01:04]] [INFO] Executing action 567/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[19:01:03]] [SUCCESS] Screenshot refreshed
[[19:01:03]] [INFO] Refreshing screenshot...
[[19:01:03]] [INFO] RbD937Xbte=pass
[[19:00:58]] [SUCCESS] Screenshot refreshed successfully
[[19:00:58]] [SUCCESS] Screenshot refreshed successfully
[[19:00:58]] [INFO] RbD937Xbte=running
[[19:00:58]] [INFO] Executing action 566/590: Tap on Text: "out"
[[19:00:58]] [SUCCESS] Screenshot refreshed
[[19:00:58]] [INFO] Refreshing screenshot...
[[19:00:58]] [INFO] ylslyLAYKb=pass
[[19:00:54]] [SUCCESS] Screenshot refreshed successfully
[[19:00:54]] [SUCCESS] Screenshot refreshed successfully
[[19:00:54]] [INFO] ylslyLAYKb=running
[[19:00:54]] [INFO] Executing action 565/590: Swipe from (50%, 70%) to (50%, 30%)
[[19:00:53]] [SUCCESS] Screenshot refreshed
[[19:00:53]] [INFO] Refreshing screenshot...
[[19:00:53]] [INFO] wguGCt7OoB=pass
[[19:00:49]] [SUCCESS] Screenshot refreshed successfully
[[19:00:49]] [SUCCESS] Screenshot refreshed successfully
[[19:00:49]] [INFO] wguGCt7OoB=running
[[19:00:49]] [INFO] Executing action 564/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:00:48]] [SUCCESS] Screenshot refreshed
[[19:00:48]] [INFO] Refreshing screenshot...
[[19:00:48]] [INFO] RDQCFIxjA0=pass
[[19:00:44]] [SUCCESS] Screenshot refreshed successfully
[[19:00:44]] [SUCCESS] Screenshot refreshed successfully
[[19:00:44]] [INFO] RDQCFIxjA0=running
[[19:00:44]] [INFO] Executing action 563/590: Swipe from (90%, 20%) to (30%, 20%)
[[19:00:44]] [SUCCESS] Screenshot refreshed
[[19:00:44]] [INFO] Refreshing screenshot...
[[19:00:44]] [INFO] x4Mid4HQ0Z=pass
[[19:00:40]] [SUCCESS] Screenshot refreshed successfully
[[19:00:40]] [SUCCESS] Screenshot refreshed successfully
[[19:00:40]] [INFO] x4Mid4HQ0Z=running
[[19:00:40]] [INFO] Executing action 562/590: Swipe from (90%, 20%) to (30%, 20%)
[[19:00:39]] [SUCCESS] Screenshot refreshed
[[19:00:39]] [INFO] Refreshing screenshot...
[[19:00:39]] [INFO] OKCHAK6HCJ=pass
[[19:00:35]] [SUCCESS] Screenshot refreshed successfully
[[19:00:35]] [SUCCESS] Screenshot refreshed successfully
[[19:00:35]] [INFO] OKCHAK6HCJ=running
[[19:00:35]] [INFO] Executing action 561/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[19:00:34]] [SUCCESS] Screenshot refreshed
[[19:00:34]] [INFO] Refreshing screenshot...
[[19:00:34]] [INFO] Ef6OumM2eS=pass
[[19:00:17]] [SUCCESS] Screenshot refreshed successfully
[[19:00:17]] [SUCCESS] Screenshot refreshed successfully
[[19:00:17]] [INFO] Ef6OumM2eS=running
[[19:00:17]] [INFO] Executing action 560/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[19:00:17]] [SUCCESS] Screenshot refreshed
[[19:00:17]] [INFO] Refreshing screenshot...
[[19:00:17]] [INFO] QkaF93zxUg=pass
[[19:00:13]] [SUCCESS] Screenshot refreshed successfully
[[19:00:13]] [SUCCESS] Screenshot refreshed successfully
[[19:00:13]] [INFO] QkaF93zxUg=running
[[19:00:13]] [INFO] Executing action 559/590: Check if element with xpath="(//XCUIElementTypeStaticText[@name="Value"])[1]" exists
[[19:00:12]] [SUCCESS] Screenshot refreshed
[[19:00:12]] [INFO] Refreshing screenshot...
[[19:00:12]] [INFO] HZT2s0AzX7=pass
[[19:00:08]] [SUCCESS] Screenshot refreshed successfully
[[19:00:08]] [SUCCESS] Screenshot refreshed successfully
[[19:00:08]] [INFO] HZT2s0AzX7=running
[[19:00:08]] [INFO] Executing action 558/590: Tap on element with xpath: //XCUIElementTypeStaticText[@name="("]/following-sibling::*[1]
[[19:00:07]] [SUCCESS] Screenshot refreshed
[[19:00:07]] [INFO] Refreshing screenshot...
[[19:00:07]] [INFO] 0bnBNoqPt8=pass
[[19:00:01]] [SUCCESS] Screenshot refreshed successfully
[[19:00:01]] [SUCCESS] Screenshot refreshed successfully
[[19:00:01]] [INFO] 0bnBNoqPt8=running
[[19:00:01]] [INFO] Executing action 557/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[19:00:00]] [SUCCESS] Screenshot refreshed
[[19:00:00]] [INFO] Refreshing screenshot...
[[19:00:00]] [INFO] xmelRkcdVx=pass
[[18:59:57]] [SUCCESS] Screenshot refreshed successfully
[[18:59:57]] [SUCCESS] Screenshot refreshed successfully
[[18:59:55]] [INFO] xmelRkcdVx=running
[[18:59:55]] [INFO] Executing action 556/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:59:55]] [SUCCESS] Screenshot refreshed
[[18:59:55]] [INFO] Refreshing screenshot...
[[18:59:55]] [INFO] ksCBjJiwHZ=pass
[[18:59:50]] [SUCCESS] Screenshot refreshed successfully
[[18:59:50]] [SUCCESS] Screenshot refreshed successfully
[[18:59:50]] [INFO] ksCBjJiwHZ=running
[[18:59:50]] [INFO] Executing action 555/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:59:49]] [SUCCESS] Screenshot refreshed
[[18:59:49]] [INFO] Refreshing screenshot...
[[18:59:49]] [INFO] RuPGkdCdah=pass
[[18:59:45]] [SUCCESS] Screenshot refreshed successfully
[[18:59:45]] [SUCCESS] Screenshot refreshed successfully
[[18:59:45]] [INFO] RuPGkdCdah=running
[[18:59:45]] [INFO] Executing action 554/590: iOS Function: text - Text: "enn[cooker-id]"
[[18:59:44]] [SUCCESS] Screenshot refreshed
[[18:59:44]] [INFO] Refreshing screenshot...
[[18:59:44]] [INFO] ewuLtuqVuo=pass
[[18:59:39]] [SUCCESS] Screenshot refreshed successfully
[[18:59:39]] [SUCCESS] Screenshot refreshed successfully
[[18:59:38]] [INFO] ewuLtuqVuo=running
[[18:59:38]] [INFO] Executing action 553/590: Tap on Text: "Find"
[[18:59:38]] [SUCCESS] Screenshot refreshed
[[18:59:38]] [INFO] Refreshing screenshot...
[[18:59:38]] [INFO] GTXmST3hEA=pass
[[18:59:33]] [SUCCESS] Screenshot refreshed successfully
[[18:59:33]] [SUCCESS] Screenshot refreshed successfully
[[18:59:33]] [INFO] GTXmST3hEA=running
[[18:59:33]] [INFO] Executing action 552/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:59:32]] [SUCCESS] Screenshot refreshed
[[18:59:32]] [INFO] Refreshing screenshot...
[[18:59:32]] [INFO] qkZ5KShdEU=pass
[[18:59:28]] [SUCCESS] Screenshot refreshed successfully
[[18:59:28]] [SUCCESS] Screenshot refreshed successfully
[[18:59:27]] [INFO] qkZ5KShdEU=running
[[18:59:27]] [INFO] Executing action 551/590: iOS Function: text - Text: "env[pwd]"
[[18:59:27]] [SUCCESS] Screenshot refreshed
[[18:59:27]] [INFO] Refreshing screenshot...
[[18:59:27]] [INFO] 7g2LmvjtEZ=pass
[[18:59:23]] [SUCCESS] Screenshot refreshed successfully
[[18:59:23]] [SUCCESS] Screenshot refreshed successfully
[[18:59:23]] [INFO] 7g2LmvjtEZ=running
[[18:59:23]] [INFO] Executing action 550/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:59:22]] [SUCCESS] Screenshot refreshed
[[18:59:22]] [INFO] Refreshing screenshot...
[[18:59:22]] [INFO] OUT2ASweb6=pass
[[18:59:17]] [SUCCESS] Screenshot refreshed successfully
[[18:59:17]] [SUCCESS] Screenshot refreshed successfully
[[18:59:17]] [INFO] OUT2ASweb6=running
[[18:59:17]] [INFO] Executing action 549/590: iOS Function: text - Text: "env[uname]"
[[18:59:17]] [SUCCESS] Screenshot refreshed
[[18:59:17]] [INFO] Refreshing screenshot...
[[18:59:17]] [INFO] TV4kJIIV9v=pass
[[18:59:12]] [SUCCESS] Screenshot refreshed successfully
[[18:59:12]] [SUCCESS] Screenshot refreshed successfully
[[18:59:12]] [INFO] TV4kJIIV9v=running
[[18:59:12]] [INFO] Executing action 548/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:59:12]] [SUCCESS] Screenshot refreshed
[[18:59:12]] [INFO] Refreshing screenshot...
[[18:59:12]] [INFO] kQJbqm7uCi=pass
[[18:59:09]] [SUCCESS] Screenshot refreshed successfully
[[18:59:09]] [SUCCESS] Screenshot refreshed successfully
[[18:59:09]] [INFO] kQJbqm7uCi=running
[[18:59:09]] [INFO] Executing action 547/590: iOS Function: alert_accept
[[18:59:08]] [SUCCESS] Screenshot refreshed
[[18:59:08]] [INFO] Refreshing screenshot...
[[18:59:08]] [INFO] SPE01N6pyp=pass
[[18:59:02]] [SUCCESS] Screenshot refreshed successfully
[[18:59:02]] [SUCCESS] Screenshot refreshed successfully
[[18:59:02]] [INFO] SPE01N6pyp=running
[[18:59:02]] [INFO] Executing action 546/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:59:01]] [SUCCESS] Screenshot refreshed
[[18:59:01]] [INFO] Refreshing screenshot...
[[18:59:01]] [INFO] WEB5St2Mb7=pass
[[18:58:57]] [SUCCESS] Screenshot refreshed successfully
[[18:58:57]] [SUCCESS] Screenshot refreshed successfully
[[18:58:57]] [INFO] WEB5St2Mb7=running
[[18:58:57]] [INFO] Executing action 545/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:58:56]] [SUCCESS] Screenshot refreshed
[[18:58:56]] [INFO] Refreshing screenshot...
[[18:58:56]] [INFO] To7bij5MnF=pass
[[18:58:50]] [INFO] To7bij5MnF=running
[[18:58:50]] [INFO] Executing action 544/590: Swipe from (5%, 50%) to (90%, 50%)
[[18:58:50]] [SUCCESS] Screenshot refreshed successfully
[[18:58:50]] [SUCCESS] Screenshot refreshed successfully
[[18:58:50]] [SUCCESS] Screenshot refreshed
[[18:58:50]] [INFO] Refreshing screenshot...
[[18:58:50]] [INFO] NkybTKfs2U=pass
[[18:58:45]] [SUCCESS] Screenshot refreshed successfully
[[18:58:45]] [SUCCESS] Screenshot refreshed successfully
[[18:58:44]] [INFO] NkybTKfs2U=running
[[18:58:44]] [INFO] Executing action 543/590: Swipe from (5%, 50%) to (90%, 50%)
[[18:58:44]] [SUCCESS] Screenshot refreshed
[[18:58:44]] [INFO] Refreshing screenshot...
[[18:58:44]] [INFO] dYEtjrv6lz=pass
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [INFO] dYEtjrv6lz=running
[[18:58:39]] [INFO] Executing action 542/590: Tap on Text: "Months"
[[18:58:39]] [SUCCESS] Screenshot refreshed
[[18:58:39]] [INFO] Refreshing screenshot...
[[18:58:39]] [INFO] eGQ7VrKUSo=pass
[[18:58:34]] [SUCCESS] Screenshot refreshed successfully
[[18:58:34]] [SUCCESS] Screenshot refreshed successfully
[[18:58:34]] [INFO] eGQ7VrKUSo=running
[[18:58:34]] [INFO] Executing action 541/590: Tap on Text: "Age"
[[18:58:33]] [SUCCESS] Screenshot refreshed
[[18:58:33]] [INFO] Refreshing screenshot...
[[18:58:33]] [INFO] zNRPvs2cC4=pass
[[18:58:29]] [SUCCESS] Screenshot refreshed successfully
[[18:58:29]] [SUCCESS] Screenshot refreshed successfully
[[18:58:29]] [INFO] zNRPvs2cC4=running
[[18:58:29]] [INFO] Executing action 540/590: Tap on Text: "Toys"
[[18:58:28]] [SUCCESS] Screenshot refreshed
[[18:58:28]] [INFO] Refreshing screenshot...
[[18:58:28]] [INFO] KyyS139agr=pass
[[18:58:24]] [SUCCESS] Screenshot refreshed successfully
[[18:58:24]] [SUCCESS] Screenshot refreshed successfully
[[18:58:23]] [INFO] KyyS139agr=running
[[18:58:23]] [INFO] Executing action 539/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:58:23]] [SUCCESS] Screenshot refreshed
[[18:58:23]] [INFO] Refreshing screenshot...
[[18:58:23]] [INFO] 5e4LeoW1YU=pass
[[18:58:19]] [SUCCESS] Screenshot refreshed successfully
[[18:58:19]] [SUCCESS] Screenshot refreshed successfully
[[18:58:17]] [INFO] 5e4LeoW1YU=running
[[18:58:17]] [INFO] Executing action 538/590: Restart app: env[appid]
[[18:58:17]] [SUCCESS] Screenshot refreshed
[[18:58:17]] [INFO] Refreshing screenshot...
[[18:58:17]] [SUCCESS] Screenshot refreshed
[[18:58:17]] [INFO] Refreshing screenshot...
[[18:58:00]] [SUCCESS] Screenshot refreshed successfully
[[18:58:00]] [SUCCESS] Screenshot refreshed successfully
[[18:57:59]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:57:59]] [SUCCESS] Screenshot refreshed
[[18:57:59]] [INFO] Refreshing screenshot...
[[18:57:11]] [SUCCESS] Screenshot refreshed successfully
[[18:57:11]] [SUCCESS] Screenshot refreshed successfully
[[18:57:10]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:57:10]] [SUCCESS] Screenshot refreshed
[[18:57:10]] [INFO] Refreshing screenshot...
[[18:56:55]] [SUCCESS] Screenshot refreshed successfully
[[18:56:55]] [SUCCESS] Screenshot refreshed successfully
[[18:56:54]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:56:53]] [SUCCESS] Screenshot refreshed
[[18:56:53]] [INFO] Refreshing screenshot...
[[18:56:07]] [SUCCESS] Screenshot refreshed successfully
[[18:56:07]] [SUCCESS] Screenshot refreshed successfully
[[18:56:06]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:56:06]] [SUCCESS] Screenshot refreshed
[[18:56:06]] [INFO] Refreshing screenshot...
[[18:55:49]] [SUCCESS] Screenshot refreshed successfully
[[18:55:49]] [SUCCESS] Screenshot refreshed successfully
[[18:55:49]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:55:48]] [SUCCESS] Screenshot refreshed
[[18:55:48]] [INFO] Refreshing screenshot...
[[18:55:02]] [SUCCESS] Screenshot refreshed successfully
[[18:55:02]] [SUCCESS] Screenshot refreshed successfully
[[18:55:01]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:55:01]] [SUCCESS] Screenshot refreshed
[[18:55:01]] [INFO] Refreshing screenshot...
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [SUCCESS] Screenshot refreshed successfully
[[18:54:40]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:54:39]] [SUCCESS] Screenshot refreshed
[[18:54:39]] [INFO] Refreshing screenshot...
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:51]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:53:51]] [SUCCESS] Screenshot refreshed
[[18:53:51]] [INFO] Refreshing screenshot...
[[18:53:33]] [SUCCESS] Screenshot refreshed successfully
[[18:53:33]] [SUCCESS] Screenshot refreshed successfully
[[18:53:32]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:53:32]] [SUCCESS] Screenshot refreshed
[[18:53:32]] [INFO] Refreshing screenshot...
[[18:52:42]] [SUCCESS] Screenshot refreshed successfully
[[18:52:42]] [SUCCESS] Screenshot refreshed successfully
[[18:52:41]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[18:52:41]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[18:52:41]] [INFO] Loading steps for multiStep action: Click_Paginations
[[18:52:41]] [INFO] aqs7O0Yq2p=running
[[18:52:41]] [INFO] Executing action 537/590: Execute Test Case: Click_Paginations (10 steps)
[[18:52:41]] [SUCCESS] Screenshot refreshed
[[18:52:41]] [INFO] Refreshing screenshot...
[[18:52:41]] [INFO] IL6kON0uQ9=pass
[[18:52:36]] [SUCCESS] Screenshot refreshed successfully
[[18:52:36]] [SUCCESS] Screenshot refreshed successfully
[[18:52:36]] [INFO] IL6kON0uQ9=running
[[18:52:36]] [INFO] Executing action 536/590: iOS Function: text - Text: "kids toys"
[[18:52:36]] [SUCCESS] Screenshot refreshed
[[18:52:36]] [INFO] Refreshing screenshot...
[[18:52:36]] [INFO] 6G6P3UE7Uy=pass
[[18:52:30]] [SUCCESS] Screenshot refreshed successfully
[[18:52:30]] [SUCCESS] Screenshot refreshed successfully
[[18:52:30]] [INFO] 6G6P3UE7Uy=running
[[18:52:30]] [INFO] Executing action 535/590: Tap on Text: "Find"
[[18:52:29]] [SUCCESS] Screenshot refreshed
[[18:52:29]] [INFO] Refreshing screenshot...
[[18:52:29]] [INFO] 7xs3GiydGF=pass
[[18:52:24]] [SUCCESS] Screenshot refreshed successfully
[[18:52:24]] [SUCCESS] Screenshot refreshed successfully
[[18:52:24]] [INFO] 7xs3GiydGF=running
[[18:52:24]] [INFO] Executing action 534/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:52:23]] [SUCCESS] Screenshot refreshed
[[18:52:23]] [INFO] Refreshing screenshot...
[[18:52:23]] [INFO] VqSa9z9R2Q=pass
[[18:52:22]] [INFO] VqSa9z9R2Q=running
[[18:52:22]] [INFO] Executing action 533/590: Launch app: env[appid]
[[18:52:21]] [SUCCESS] Screenshot refreshed successfully
[[18:52:21]] [SUCCESS] Screenshot refreshed successfully
[[18:52:21]] [SUCCESS] Screenshot refreshed
[[18:52:21]] [INFO] Refreshing screenshot...
[[18:52:21]] [INFO] RHEU77LRMw=pass
[[18:52:17]] [SUCCESS] Screenshot refreshed successfully
[[18:52:17]] [SUCCESS] Screenshot refreshed successfully
[[18:52:17]] [INFO] RHEU77LRMw=running
[[18:52:17]] [INFO] Executing action 532/590: Tap on Text: "+61"
[[18:52:16]] [SUCCESS] Screenshot refreshed
[[18:52:16]] [INFO] Refreshing screenshot...
[[18:52:16]] [INFO] MTRbUlaRvI=pass
[[18:52:12]] [SUCCESS] Screenshot refreshed successfully
[[18:52:12]] [SUCCESS] Screenshot refreshed successfully
[[18:52:11]] [INFO] MTRbUlaRvI=running
[[18:52:11]] [INFO] Executing action 531/590: Tap on Text: "1800"
[[18:52:11]] [SUCCESS] Screenshot refreshed
[[18:52:11]] [INFO] Refreshing screenshot...
[[18:52:11]] [INFO] I0tM87Yjhc=pass
[[18:52:06]] [SUCCESS] Screenshot refreshed successfully
[[18:52:06]] [SUCCESS] Screenshot refreshed successfully
[[18:52:06]] [INFO] I0tM87Yjhc=running
[[18:52:06]] [INFO] Executing action 530/590: Tap on Text: "click"
[[18:52:05]] [SUCCESS] Screenshot refreshed
[[18:52:05]] [INFO] Refreshing screenshot...
[[18:52:05]] [INFO] t6L5vWfBYM=pass
[[18:51:45]] [SUCCESS] Screenshot refreshed successfully
[[18:51:45]] [SUCCESS] Screenshot refreshed successfully
[[18:51:45]] [INFO] t6L5vWfBYM=running
[[18:51:45]] [INFO] Executing action 529/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:51:44]] [SUCCESS] Screenshot refreshed
[[18:51:44]] [INFO] Refreshing screenshot...
[[18:51:44]] [INFO] DhFJzlme9K=pass
[[18:51:39]] [SUCCESS] Screenshot refreshed successfully
[[18:51:39]] [SUCCESS] Screenshot refreshed successfully
[[18:51:39]] [INFO] DhFJzlme9K=running
[[18:51:39]] [INFO] Executing action 528/590: Tap on Text: "FAQ"
[[18:51:39]] [SUCCESS] Screenshot refreshed
[[18:51:39]] [INFO] Refreshing screenshot...
[[18:51:39]] [INFO] g17Boaefhg=pass
[[18:51:34]] [SUCCESS] Screenshot refreshed successfully
[[18:51:34]] [SUCCESS] Screenshot refreshed successfully
[[18:51:34]] [INFO] g17Boaefhg=running
[[18:51:34]] [INFO] Executing action 527/590: Tap on Text: "Help"
[[18:51:33]] [SUCCESS] Screenshot refreshed
[[18:51:33]] [INFO] Refreshing screenshot...
[[18:51:33]] [INFO] SqDiBhmyOG=pass
[[18:51:29]] [SUCCESS] Screenshot refreshed successfully
[[18:51:29]] [SUCCESS] Screenshot refreshed successfully
[[18:51:29]] [INFO] SqDiBhmyOG=running
[[18:51:29]] [INFO] Executing action 526/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:51:28]] [SUCCESS] Screenshot refreshed
[[18:51:28]] [INFO] Refreshing screenshot...
[[18:51:28]] [INFO] OR0SKKnFxy=pass
[[18:51:15]] [SUCCESS] Screenshot refreshed successfully
[[18:51:15]] [SUCCESS] Screenshot refreshed successfully
[[18:51:14]] [INFO] OR0SKKnFxy=running
[[18:51:14]] [INFO] Executing action 525/590: Restart app: env[appid]
[[18:51:14]] [SUCCESS] Screenshot refreshed
[[18:51:14]] [INFO] Refreshing screenshot...
[[18:51:13]] [SUCCESS] Screenshot refreshed
[[18:51:13]] [INFO] Refreshing screenshot...
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:51:10]] [SUCCESS] Screenshot refreshed
[[18:51:10]] [INFO] Refreshing screenshot...
[[18:50:57]] [SUCCESS] Screenshot refreshed successfully
[[18:50:57]] [SUCCESS] Screenshot refreshed successfully
[[18:50:57]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:50:56]] [SUCCESS] Screenshot refreshed
[[18:50:56]] [INFO] Refreshing screenshot...
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:52]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:50:52]] [SUCCESS] Screenshot refreshed
[[18:50:52]] [INFO] Refreshing screenshot...
[[18:50:48]] [SUCCESS] Screenshot refreshed successfully
[[18:50:48]] [SUCCESS] Screenshot refreshed successfully
[[18:50:47]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:50:47]] [SUCCESS] Screenshot refreshed
[[18:50:47]] [INFO] Refreshing screenshot...
[[18:50:40]] [SUCCESS] Screenshot refreshed successfully
[[18:50:40]] [SUCCESS] Screenshot refreshed successfully
[[18:50:40]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:50:39]] [SUCCESS] Screenshot refreshed
[[18:50:39]] [INFO] Refreshing screenshot...
[[18:50:32]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:50:32]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:50:32]] [SUCCESS] Screenshot refreshed successfully
[[18:50:32]] [SUCCESS] Screenshot refreshed successfully
[[18:50:32]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:50:32]] [INFO] kPdSiomhwu=running
[[18:50:32]] [INFO] Executing action 524/590: cleanupSteps action
[[18:50:31]] [SUCCESS] Screenshot refreshed
[[18:50:31]] [INFO] Refreshing screenshot...
[[18:50:31]] [INFO] Qb1AArnpCH=pass
[[18:50:25]] [SUCCESS] Screenshot refreshed successfully
[[18:50:25]] [SUCCESS] Screenshot refreshed successfully
[[18:50:24]] [INFO] Qb1AArnpCH=running
[[18:50:24]] [INFO] Executing action 523/590: Wait for 5 ms
[[18:50:24]] [SUCCESS] Screenshot refreshed
[[18:50:24]] [INFO] Refreshing screenshot...
[[18:50:24]] [INFO] yxlzTytgFT=pass
[[18:50:18]] [SUCCESS] Screenshot refreshed successfully
[[18:50:18]] [SUCCESS] Screenshot refreshed successfully
[[18:50:18]] [INFO] yxlzTytgFT=running
[[18:50:18]] [INFO] Executing action 522/590: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
[[18:50:17]] [SUCCESS] Screenshot refreshed
[[18:50:17]] [INFO] Refreshing screenshot...
[[18:50:17]] [INFO] K2w7X1cPdH=pass
[[18:50:12]] [SUCCESS] Screenshot refreshed successfully
[[18:50:12]] [SUCCESS] Screenshot refreshed successfully
[[18:50:12]] [INFO] K2w7X1cPdH=running
[[18:50:12]] [INFO] Executing action 521/590: Swipe from (50%, 50%) to (50%, 30%)
[[18:50:11]] [SUCCESS] Screenshot refreshed
[[18:50:11]] [INFO] Refreshing screenshot...
[[18:50:11]] [INFO] P26OyuqWlb=pass
[[18:49:59]] [INFO] P26OyuqWlb=running
[[18:49:59]] [INFO] Executing action 520/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:58]] [SUCCESS] Screenshot refreshed
[[18:49:58]] [INFO] Refreshing screenshot...
[[18:49:58]] [INFO] UpUSVInizv=pass
[[18:49:54]] [SUCCESS] Screenshot refreshed successfully
[[18:49:54]] [SUCCESS] Screenshot refreshed successfully
[[18:49:54]] [INFO] UpUSVInizv=running
[[18:49:54]] [INFO] Executing action 519/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:49:53]] [SUCCESS] Screenshot refreshed
[[18:49:53]] [INFO] Refreshing screenshot...
[[18:49:53]] [INFO] c4T3INQkzn=pass
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [INFO] c4T3INQkzn=running
[[18:49:48]] [INFO] Executing action 518/590: Restart app: env[appid]
[[18:49:47]] [SUCCESS] Screenshot refreshed
[[18:49:47]] [INFO] Refreshing screenshot...
[[18:49:47]] [INFO] Teyz3d55XS=pass
[[18:49:38]] [SUCCESS] Screenshot refreshed successfully
[[18:49:38]] [SUCCESS] Screenshot refreshed successfully
[[18:49:38]] [INFO] Teyz3d55XS=running
[[18:49:38]] [INFO] Executing action 517/590: Tap if locator exists: accessibility_id="Add to bag"
[[18:49:38]] [SUCCESS] Screenshot refreshed
[[18:49:38]] [INFO] Refreshing screenshot...
[[18:49:38]] [INFO] MA2re5cDWr=pass
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:30]] [INFO] MA2re5cDWr=running
[[18:49:30]] [INFO] Executing action 516/590: Swipe from (50%, 50%) to (50%, 30%)
[[18:49:30]] [SUCCESS] Screenshot refreshed
[[18:49:30]] [INFO] Refreshing screenshot...
[[18:49:30]] [INFO] 2hGhWulI52=pass
[[18:49:28]] [SUCCESS] Screenshot refreshed successfully
[[18:49:28]] [SUCCESS] Screenshot refreshed successfully
[[18:49:26]] [INFO] 2hGhWulI52=running
[[18:49:26]] [INFO] Executing action 515/590: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[18:49:25]] [SUCCESS] Screenshot refreshed
[[18:49:25]] [INFO] Refreshing screenshot...
[[18:49:25]] [INFO] n57KEWjTea=pass
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:19]] [INFO] n57KEWjTea=running
[[18:49:19]] [INFO] Executing action 514/590: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[18:49:18]] [SUCCESS] Screenshot refreshed
[[18:49:18]] [INFO] Refreshing screenshot...
[[18:49:18]] [INFO] L59V5hqMX9=pass
[[18:49:14]] [SUCCESS] Screenshot refreshed successfully
[[18:49:14]] [SUCCESS] Screenshot refreshed successfully
[[18:49:14]] [INFO] L59V5hqMX9=running
[[18:49:14]] [INFO] Executing action 513/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[18:49:13]] [SUCCESS] Screenshot refreshed
[[18:49:13]] [INFO] Refreshing screenshot...
[[18:49:13]] [INFO] OKiI82VdnE=pass
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:07]] [SUCCESS] Screenshot refreshed successfully
[[18:49:06]] [INFO] OKiI82VdnE=running
[[18:49:06]] [INFO] Executing action 512/590: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[18:49:06]] [SUCCESS] Screenshot refreshed
[[18:49:06]] [INFO] Refreshing screenshot...
[[18:49:06]] [INFO] 3KNqlNy6Bj=pass
[[18:49:01]] [SUCCESS] Screenshot refreshed successfully
[[18:49:01]] [SUCCESS] Screenshot refreshed successfully
[[18:49:01]] [INFO] 3KNqlNy6Bj=running
[[18:49:01]] [INFO] Executing action 511/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[18:49:00]] [SUCCESS] Screenshot refreshed
[[18:49:00]] [INFO] Refreshing screenshot...
[[18:49:00]] [INFO] 3NOS1fbxZs=pass
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [INFO] 3NOS1fbxZs=running
[[18:48:56]] [INFO] Executing action 510/590: Tap on image: banner-close-updated.png
[[18:48:55]] [SUCCESS] Screenshot refreshed
[[18:48:55]] [INFO] Refreshing screenshot...
[[18:48:55]] [INFO] K0c1gL9UK1=pass
[[18:48:53]] [SUCCESS] Screenshot refreshed successfully
[[18:48:53]] [SUCCESS] Screenshot refreshed successfully
[[18:48:51]] [INFO] K0c1gL9UK1=running
[[18:48:51]] [INFO] Executing action 509/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:48:50]] [SUCCESS] Screenshot refreshed
[[18:48:50]] [INFO] Refreshing screenshot...
[[18:48:50]] [INFO] IW6uAwdtiW=pass
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [INFO] IW6uAwdtiW=running
[[18:48:46]] [INFO] Executing action 508/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[18:48:46]] [SUCCESS] Screenshot refreshed
[[18:48:46]] [INFO] Refreshing screenshot...
[[18:48:46]] [INFO] DbM0d0m6rU=pass
[[18:48:42]] [SUCCESS] Screenshot refreshed successfully
[[18:48:42]] [SUCCESS] Screenshot refreshed successfully
[[18:48:41]] [INFO] DbM0d0m6rU=running
[[18:48:41]] [INFO] Executing action 507/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[18:48:41]] [SUCCESS] Screenshot refreshed
[[18:48:41]] [INFO] Refreshing screenshot...
[[18:48:41]] [INFO] saiPPHQSPa=pass
[[18:48:29]] [INFO] saiPPHQSPa=running
[[18:48:29]] [INFO] Executing action 506/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:48:29]] [SUCCESS] Screenshot refreshed successfully
[[18:48:29]] [SUCCESS] Screenshot refreshed successfully
[[18:48:28]] [SUCCESS] Screenshot refreshed
[[18:48:28]] [INFO] Refreshing screenshot...
[[18:48:28]] [INFO] UpUSVInizv=pass
[[18:48:23]] [SUCCESS] Screenshot refreshed successfully
[[18:48:23]] [SUCCESS] Screenshot refreshed successfully
[[18:48:23]] [INFO] UpUSVInizv=running
[[18:48:23]] [INFO] Executing action 505/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:48:22]] [SUCCESS] Screenshot refreshed
[[18:48:22]] [INFO] Refreshing screenshot...
[[18:48:22]] [INFO] Iab9zCfpqO=pass
[[18:48:04]] [SUCCESS] Screenshot refreshed successfully
[[18:48:04]] [SUCCESS] Screenshot refreshed successfully
[[18:48:04]] [INFO] Iab9zCfpqO=running
[[18:48:04]] [INFO] Executing action 504/590: Tap on element with accessibility_id: Add to bag
[[18:48:03]] [SUCCESS] Screenshot refreshed
[[18:48:03]] [INFO] Refreshing screenshot...
[[18:48:03]] [INFO] Qy0Y0uJchm=pass
[[18:48:00]] [SUCCESS] Screenshot refreshed successfully
[[18:48:00]] [SUCCESS] Screenshot refreshed successfully
[[18:47:59]] [INFO] Qy0Y0uJchm=running
[[18:47:59]] [INFO] Executing action 503/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:47:59]] [SUCCESS] Screenshot refreshed
[[18:47:59]] [INFO] Refreshing screenshot...
[[18:47:59]] [INFO] YHaMIjULRf=pass
[[18:47:53]] [SUCCESS] Screenshot refreshed successfully
[[18:47:53]] [SUCCESS] Screenshot refreshed successfully
[[18:47:52]] [INFO] YHaMIjULRf=running
[[18:47:52]] [INFO] Executing action 502/590: Tap on Text: "List"
[[18:47:51]] [SUCCESS] Screenshot refreshed
[[18:47:51]] [INFO] Refreshing screenshot...
[[18:47:51]] [INFO] igReeDqips=pass
[[18:47:46]] [SUCCESS] Screenshot refreshed successfully
[[18:47:46]] [SUCCESS] Screenshot refreshed successfully
[[18:47:45]] [INFO] igReeDqips=running
[[18:47:45]] [INFO] Executing action 501/590: Tap on image: env[catalogue-menu-img]
[[18:47:45]] [SUCCESS] Screenshot refreshed
[[18:47:45]] [INFO] Refreshing screenshot...
[[18:47:45]] [INFO] gcSsGpqKwk=pass
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:23]] [SUCCESS] Screenshot refreshed successfully
[[18:47:22]] [INFO] gcSsGpqKwk=running
[[18:47:22]] [INFO] Executing action 500/590: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:47:21]] [SUCCESS] Screenshot refreshed
[[18:47:21]] [INFO] Refreshing screenshot...
[[18:47:21]] [INFO] gkkQzTCmma=pass
[[18:47:15]] [SUCCESS] Screenshot refreshed successfully
[[18:47:15]] [SUCCESS] Screenshot refreshed successfully
[[18:47:15]] [INFO] gkkQzTCmma=running
[[18:47:15]] [INFO] Executing action 499/590: Tap on Text: "Catalogue"
[[18:47:15]] [SUCCESS] Screenshot refreshed
[[18:47:15]] [INFO] Refreshing screenshot...
[[18:47:15]] [INFO] UpUSVInizv=pass
[[18:47:10]] [SUCCESS] Screenshot refreshed successfully
[[18:47:10]] [SUCCESS] Screenshot refreshed successfully
[[18:47:10]] [INFO] UpUSVInizv=running
[[18:47:10]] [INFO] Executing action 498/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:47:09]] [SUCCESS] Screenshot refreshed
[[18:47:09]] [INFO] Refreshing screenshot...
[[18:47:09]] [INFO] Cmvm82hiAa=pass
[[18:47:02]] [SUCCESS] Screenshot refreshed successfully
[[18:47:02]] [SUCCESS] Screenshot refreshed successfully
[[18:47:02]] [INFO] Cmvm82hiAa=running
[[18:47:02]] [INFO] Executing action 497/590: Tap on element with accessibility_id: Add to bag
[[18:47:01]] [SUCCESS] Screenshot refreshed
[[18:47:01]] [INFO] Refreshing screenshot...
[[18:47:01]] [INFO] ZZPNqTJ65s=pass
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:55]] [INFO] ZZPNqTJ65s=running
[[18:46:55]] [INFO] Executing action 496/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:46:55]] [SUCCESS] Screenshot refreshed
[[18:46:55]] [INFO] Refreshing screenshot...
[[18:46:55]] [INFO] JcAR0JctQ6=pass
[[18:46:50]] [SUCCESS] Screenshot refreshed successfully
[[18:46:50]] [SUCCESS] Screenshot refreshed successfully
[[18:46:50]] [INFO] JcAR0JctQ6=running
[[18:46:50]] [INFO] Executing action 495/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:46:50]] [SUCCESS] Screenshot refreshed
[[18:46:50]] [INFO] Refreshing screenshot...
[[18:46:50]] [INFO] Pd7cReoJM6=pass
[[18:46:44]] [SUCCESS] Screenshot refreshed successfully
[[18:46:44]] [SUCCESS] Screenshot refreshed successfully
[[18:46:43]] [INFO] Pd7cReoJM6=running
[[18:46:43]] [INFO] Executing action 494/590: Tap on Text: "List"
[[18:46:42]] [SUCCESS] Screenshot refreshed
[[18:46:42]] [INFO] Refreshing screenshot...
[[18:46:42]] [INFO] igReeDqips=pass
[[18:46:38]] [SUCCESS] Screenshot refreshed successfully
[[18:46:38]] [SUCCESS] Screenshot refreshed successfully
[[18:46:37]] [INFO] igReeDqips=running
[[18:46:37]] [INFO] Executing action 493/590: Tap on image: env[catalogue-menu-img]
[[18:46:36]] [SUCCESS] Screenshot refreshed
[[18:46:36]] [INFO] Refreshing screenshot...
[[18:46:36]] [INFO] Jh6RTFWeOU=pass
[[18:46:14]] [SUCCESS] Screenshot refreshed successfully
[[18:46:14]] [SUCCESS] Screenshot refreshed successfully
[[18:46:13]] [INFO] Jh6RTFWeOU=running
[[18:46:13]] [INFO] Executing action 492/590: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:46:12]] [SUCCESS] Screenshot refreshed
[[18:46:12]] [INFO] Refreshing screenshot...
[[18:46:12]] [INFO] gkkQzTCmma=pass
[[18:46:06]] [SUCCESS] Screenshot refreshed successfully
[[18:46:06]] [SUCCESS] Screenshot refreshed successfully
[[18:46:06]] [INFO] gkkQzTCmma=running
[[18:46:06]] [INFO] Executing action 491/590: Tap on Text: "Catalogue"
[[18:46:06]] [SUCCESS] Screenshot refreshed
[[18:46:06]] [INFO] Refreshing screenshot...
[[18:46:06]] [INFO] QUeGIASAxV=pass
[[18:46:02]] [SUCCESS] Screenshot refreshed successfully
[[18:46:02]] [SUCCESS] Screenshot refreshed successfully
[[18:46:02]] [INFO] QUeGIASAxV=running
[[18:46:02]] [INFO] Executing action 490/590: Swipe from (50%, 50%) to (50%, 30%)
[[18:46:01]] [SUCCESS] Screenshot refreshed
[[18:46:01]] [INFO] Refreshing screenshot...
[[18:46:01]] [INFO] UpUSVInizv=pass
[[18:45:58]] [SUCCESS] Screenshot refreshed successfully
[[18:45:58]] [SUCCESS] Screenshot refreshed successfully
[[18:45:57]] [INFO] UpUSVInizv=running
[[18:45:57]] [INFO] Executing action 489/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:45:57]] [SUCCESS] Screenshot refreshed
[[18:45:57]] [INFO] Refreshing screenshot...
[[18:45:57]] [INFO] 0QtNHB5WEK=pass
[[18:45:53]] [SUCCESS] Screenshot refreshed successfully
[[18:45:53]] [SUCCESS] Screenshot refreshed successfully
[[18:45:53]] [INFO] 0QtNHB5WEK=running
[[18:45:53]] [INFO] Executing action 488/590: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[18:45:52]] [SUCCESS] Screenshot refreshed
[[18:45:52]] [INFO] Refreshing screenshot...
[[18:45:52]] [INFO] fTdGMJ3NH3=pass
[[18:45:49]] [SUCCESS] Screenshot refreshed successfully
[[18:45:49]] [SUCCESS] Screenshot refreshed successfully
[[18:45:49]] [INFO] fTdGMJ3NH3=running
[[18:45:49]] [INFO] Executing action 487/590: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[18:45:48]] [SUCCESS] Screenshot refreshed
[[18:45:48]] [INFO] Refreshing screenshot...
[[18:45:48]] [INFO] rYJcLPh8Aq=pass
[[18:45:45]] [INFO] rYJcLPh8Aq=running
[[18:45:45]] [INFO] Executing action 486/590: iOS Function: text - Text: "kmart au"
[[18:45:45]] [SUCCESS] Screenshot refreshed successfully
[[18:45:45]] [SUCCESS] Screenshot refreshed successfully
[[18:45:45]] [SUCCESS] Screenshot refreshed
[[18:45:45]] [INFO] Refreshing screenshot...
[[18:45:45]] [INFO] 0Q0fm6OTij=pass
[[18:45:42]] [SUCCESS] Screenshot refreshed successfully
[[18:45:42]] [SUCCESS] Screenshot refreshed successfully
[[18:45:42]] [INFO] 0Q0fm6OTij=running
[[18:45:42]] [INFO] Executing action 485/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[18:45:41]] [SUCCESS] Screenshot refreshed
[[18:45:41]] [INFO] Refreshing screenshot...
[[18:45:41]] [INFO] xVuuejtCFA=pass
[[18:45:37]] [SUCCESS] Screenshot refreshed successfully
[[18:45:37]] [SUCCESS] Screenshot refreshed successfully
[[18:45:36]] [INFO] xVuuejtCFA=running
[[18:45:36]] [INFO] Executing action 484/590: Restart app: com.apple.mobilesafari
[[18:45:36]] [SUCCESS] Screenshot refreshed
[[18:45:36]] [INFO] Refreshing screenshot...
[[18:45:36]] [INFO] LcYLwUffqj=pass
[[18:45:31]] [SUCCESS] Screenshot refreshed successfully
[[18:45:31]] [SUCCESS] Screenshot refreshed successfully
[[18:45:31]] [INFO] LcYLwUffqj=running
[[18:45:31]] [INFO] Executing action 483/590: Tap on Text: "out"
[[18:45:30]] [SUCCESS] Screenshot refreshed
[[18:45:30]] [INFO] Refreshing screenshot...
[[18:45:30]] [INFO] ZZPNqTJ65s=pass
[[18:45:26]] [SUCCESS] Screenshot refreshed successfully
[[18:45:26]] [SUCCESS] Screenshot refreshed successfully
[[18:45:25]] [INFO] ZZPNqTJ65s=running
[[18:45:25]] [INFO] Executing action 482/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:45:25]] [SUCCESS] Screenshot refreshed
[[18:45:25]] [INFO] Refreshing screenshot...
[[18:45:25]] [INFO] UpUSVInizv=pass
[[18:45:21]] [SUCCESS] Screenshot refreshed successfully
[[18:45:21]] [SUCCESS] Screenshot refreshed successfully
[[18:45:21]] [INFO] UpUSVInizv=running
[[18:45:21]] [INFO] Executing action 481/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:45:20]] [SUCCESS] Screenshot refreshed
[[18:45:20]] [INFO] Refreshing screenshot...
[[18:45:20]] [INFO] hCCEvRtj1A=pass
[[18:45:15]] [INFO] hCCEvRtj1A=running
[[18:45:15]] [INFO] Executing action 480/590: Restart app: env[appid]
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:14]] [SUCCESS] Screenshot refreshed
[[18:45:14]] [INFO] Refreshing screenshot...
[[18:45:14]] [INFO] V42eHtTRYW=pass
[[18:45:08]] [INFO] V42eHtTRYW=running
[[18:45:08]] [INFO] Executing action 479/590: Wait for 5 ms
[[18:45:08]] [SUCCESS] Screenshot refreshed successfully
[[18:45:08]] [SUCCESS] Screenshot refreshed successfully
[[18:45:07]] [SUCCESS] Screenshot refreshed
[[18:45:07]] [INFO] Refreshing screenshot...
[[18:45:07]] [INFO] GRwHMVK4sA=pass
[[18:45:05]] [INFO] GRwHMVK4sA=running
[[18:45:05]] [INFO] Executing action 478/590: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:45:05]] [SUCCESS] Screenshot refreshed successfully
[[18:45:05]] [SUCCESS] Screenshot refreshed successfully
[[18:45:04]] [SUCCESS] Screenshot refreshed
[[18:45:04]] [INFO] Refreshing screenshot...
[[18:45:04]] [INFO] V42eHtTRYW=pass
[[18:44:58]] [INFO] V42eHtTRYW=running
[[18:44:58]] [INFO] Executing action 477/590: Wait for 5 ms
[[18:44:58]] [SUCCESS] Screenshot refreshed successfully
[[18:44:58]] [SUCCESS] Screenshot refreshed successfully
[[18:44:57]] [SUCCESS] Screenshot refreshed
[[18:44:57]] [INFO] Refreshing screenshot...
[[18:44:57]] [INFO] LfyQctrEJn=pass
[[18:44:56]] [SUCCESS] Screenshot refreshed successfully
[[18:44:56]] [SUCCESS] Screenshot refreshed successfully
[[18:44:56]] [INFO] LfyQctrEJn=running
[[18:44:56]] [INFO] Executing action 476/590: Launch app: com.apple.Preferences
[[18:44:55]] [SUCCESS] Screenshot refreshed
[[18:44:55]] [INFO] Refreshing screenshot...
[[18:44:55]] [INFO] seQcUKjkSU=pass
[[18:44:53]] [SUCCESS] Screenshot refreshed successfully
[[18:44:53]] [SUCCESS] Screenshot refreshed successfully
[[18:44:53]] [INFO] seQcUKjkSU=running
[[18:44:53]] [INFO] Executing action 475/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:44:53]] [SUCCESS] Screenshot refreshed
[[18:44:53]] [INFO] Refreshing screenshot...
[[18:44:53]] [INFO] UpUSVInizv=pass
[[18:44:50]] [SUCCESS] Screenshot refreshed successfully
[[18:44:50]] [SUCCESS] Screenshot refreshed successfully
[[18:44:50]] [INFO] UpUSVInizv=running
[[18:44:50]] [INFO] Executing action 474/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:44:50]] [SUCCESS] Screenshot refreshed
[[18:44:50]] [INFO] Refreshing screenshot...
[[18:44:50]] [INFO] WoymrHdtrO=pass
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [SUCCESS] Screenshot refreshed successfully
[[18:44:48]] [INFO] WoymrHdtrO=running
[[18:44:48]] [INFO] Executing action 473/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:44:47]] [SUCCESS] Screenshot refreshed
[[18:44:47]] [INFO] Refreshing screenshot...
[[18:44:47]] [INFO] 6xgrAWyfZ4=pass
[[18:44:45]] [SUCCESS] Screenshot refreshed successfully
[[18:44:45]] [SUCCESS] Screenshot refreshed successfully
[[18:44:45]] [INFO] 6xgrAWyfZ4=running
[[18:44:45]] [INFO] Executing action 472/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[18:44:44]] [SUCCESS] Screenshot refreshed
[[18:44:44]] [INFO] Refreshing screenshot...
[[18:44:44]] [INFO] eSr9EFlJek=pass
[[18:44:43]] [SUCCESS] Screenshot refreshed successfully
[[18:44:43]] [SUCCESS] Screenshot refreshed successfully
[[18:44:42]] [INFO] eSr9EFlJek=running
[[18:44:42]] [INFO] Executing action 471/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:44:42]] [SUCCESS] Screenshot refreshed
[[18:44:42]] [INFO] Refreshing screenshot...
[[18:44:42]] [INFO] 3KNqlNy6Bj=pass
[[18:44:40]] [SUCCESS] Screenshot refreshed successfully
[[18:44:40]] [SUCCESS] Screenshot refreshed successfully
[[18:44:40]] [INFO] 3KNqlNy6Bj=running
[[18:44:40]] [INFO] Executing action 470/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:44:39]] [SUCCESS] Screenshot refreshed
[[18:44:39]] [INFO] Refreshing screenshot...
[[18:44:39]] [INFO] cokvFXhj4c=pass
[[18:44:37]] [SUCCESS] Screenshot refreshed successfully
[[18:44:37]] [SUCCESS] Screenshot refreshed successfully
[[18:44:37]] [INFO] cokvFXhj4c=running
[[18:44:37]] [INFO] Executing action 469/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:44:37]] [SUCCESS] Screenshot refreshed
[[18:44:37]] [INFO] Refreshing screenshot...
[[18:44:37]] [INFO] oSQ8sPdVOJ=pass
[[18:44:32]] [INFO] oSQ8sPdVOJ=running
[[18:44:32]] [INFO] Executing action 468/590: Restart app: env[appid]
[[18:44:32]] [SUCCESS] Screenshot refreshed successfully
[[18:44:32]] [SUCCESS] Screenshot refreshed successfully
[[18:44:31]] [SUCCESS] Screenshot refreshed
[[18:44:31]] [INFO] Refreshing screenshot...
[[18:44:31]] [INFO] V42eHtTRYW=pass
[[18:44:25]] [INFO] V42eHtTRYW=running
[[18:44:25]] [INFO] Executing action 467/590: Wait for 5 ms
[[18:44:25]] [SUCCESS] Screenshot refreshed successfully
[[18:44:25]] [SUCCESS] Screenshot refreshed successfully
[[18:44:24]] [SUCCESS] Screenshot refreshed
[[18:44:24]] [INFO] Refreshing screenshot...
[[18:44:24]] [INFO] jUCAk6GJc4=pass
[[18:44:22]] [INFO] jUCAk6GJc4=running
[[18:44:22]] [INFO] Executing action 466/590: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:44:22]] [SUCCESS] Screenshot refreshed successfully
[[18:44:22]] [SUCCESS] Screenshot refreshed successfully
[[18:44:21]] [SUCCESS] Screenshot refreshed
[[18:44:21]] [INFO] Refreshing screenshot...
[[18:44:21]] [INFO] V42eHtTRYW=pass
[[18:44:15]] [INFO] V42eHtTRYW=running
[[18:44:15]] [INFO] Executing action 465/590: Wait for 5 ms
[[18:44:15]] [SUCCESS] Screenshot refreshed successfully
[[18:44:15]] [SUCCESS] Screenshot refreshed successfully
[[18:44:14]] [SUCCESS] Screenshot refreshed
[[18:44:14]] [INFO] Refreshing screenshot...
[[18:44:14]] [INFO] w1RV76df9x=pass
[[18:44:10]] [INFO] w1RV76df9x=running
[[18:44:10]] [INFO] Executing action 464/590: Tap on Text: "Wi-Fi"
[[18:44:09]] [SUCCESS] Screenshot refreshed successfully
[[18:44:09]] [SUCCESS] Screenshot refreshed successfully
[[18:44:09]] [SUCCESS] Screenshot refreshed
[[18:44:09]] [INFO] Refreshing screenshot...
[[18:44:09]] [INFO] LfyQctrEJn=pass
[[18:44:07]] [SUCCESS] Screenshot refreshed successfully
[[18:44:07]] [SUCCESS] Screenshot refreshed successfully
[[18:44:06]] [INFO] LfyQctrEJn=running
[[18:44:06]] [INFO] Executing action 463/590: Launch app: com.apple.Preferences
[[18:44:06]] [SUCCESS] Screenshot refreshed
[[18:44:06]] [INFO] Refreshing screenshot...
[[18:44:06]] [INFO] mIKA85kXaW=pass
[[18:44:05]] [SUCCESS] Screenshot refreshed successfully
[[18:44:05]] [SUCCESS] Screenshot refreshed successfully
[[18:44:02]] [INFO] mIKA85kXaW=running
[[18:44:02]] [INFO] Executing action 462/590: Terminate app: com.apple.Preferences
[[18:44:02]] [SUCCESS] Screenshot refreshed
[[18:44:02]] [INFO] Refreshing screenshot...
[[18:44:02]] [SUCCESS] Screenshot refreshed
[[18:44:02]] [INFO] Refreshing screenshot...
[[18:43:57]] [SUCCESS] Screenshot refreshed successfully
[[18:43:57]] [SUCCESS] Screenshot refreshed successfully
[[18:43:57]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:43:56]] [SUCCESS] Screenshot refreshed
[[18:43:56]] [INFO] Refreshing screenshot...
[[18:43:52]] [SUCCESS] Screenshot refreshed successfully
[[18:43:52]] [SUCCESS] Screenshot refreshed successfully
[[18:43:51]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:43:50]] [SUCCESS] Screenshot refreshed
[[18:43:50]] [INFO] Refreshing screenshot...
[[18:43:45]] [SUCCESS] Screenshot refreshed successfully
[[18:43:45]] [SUCCESS] Screenshot refreshed successfully
[[18:43:45]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:43:45]] [SUCCESS] Screenshot refreshed
[[18:43:45]] [INFO] Refreshing screenshot...
[[18:43:41]] [SUCCESS] Screenshot refreshed successfully
[[18:43:41]] [SUCCESS] Screenshot refreshed successfully
[[18:43:41]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:43:40]] [SUCCESS] Screenshot refreshed
[[18:43:40]] [INFO] Refreshing screenshot...
[[18:43:34]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:43:34]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:43:34]] [SUCCESS] Screenshot refreshed successfully
[[18:43:34]] [SUCCESS] Screenshot refreshed successfully
[[18:43:34]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:43:34]] [INFO] x6vffndoRV=running
[[18:43:34]] [INFO] Executing action 461/590: Execute Test Case: Kmart-Signin (6 steps)
[[18:43:34]] [SUCCESS] Screenshot refreshed
[[18:43:34]] [INFO] Refreshing screenshot...
[[18:43:34]] [INFO] rJ86z4njuR=pass
[[18:43:31]] [SUCCESS] Screenshot refreshed successfully
[[18:43:31]] [SUCCESS] Screenshot refreshed successfully
[[18:43:31]] [INFO] rJ86z4njuR=running
[[18:43:31]] [INFO] Executing action 460/590: iOS Function: alert_accept
[[18:43:30]] [SUCCESS] Screenshot refreshed
[[18:43:30]] [INFO] Refreshing screenshot...
[[18:43:30]] [INFO] veukWo4573=pass
[[18:43:25]] [SUCCESS] Screenshot refreshed successfully
[[18:43:25]] [SUCCESS] Screenshot refreshed successfully
[[18:43:25]] [INFO] veukWo4573=running
[[18:43:25]] [INFO] Executing action 459/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[18:43:24]] [SUCCESS] Screenshot refreshed
[[18:43:24]] [INFO] Refreshing screenshot...
[[18:43:24]] [INFO] XEbZHdi0GT=pass
[[18:43:11]] [SUCCESS] Screenshot refreshed successfully
[[18:43:11]] [SUCCESS] Screenshot refreshed successfully
[[18:43:10]] [INFO] XEbZHdi0GT=running
[[18:43:10]] [INFO] Executing action 458/590: Restart app: env[appid]
[[18:43:10]] [SUCCESS] Screenshot refreshed
[[18:43:10]] [INFO] Refreshing screenshot...
[[18:43:10]] [SUCCESS] Screenshot refreshed
[[18:43:10]] [INFO] Refreshing screenshot...
[[18:43:07]] [SUCCESS] Screenshot refreshed successfully
[[18:43:07]] [SUCCESS] Screenshot refreshed successfully
[[18:43:06]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:43:06]] [SUCCESS] Screenshot refreshed
[[18:43:06]] [INFO] Refreshing screenshot...
[[18:43:00]] [SUCCESS] Screenshot refreshed successfully
[[18:43:00]] [SUCCESS] Screenshot refreshed successfully
[[18:43:00]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:43:00]] [SUCCESS] Screenshot refreshed
[[18:43:00]] [INFO] Refreshing screenshot...
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:56]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:42:55]] [SUCCESS] Screenshot refreshed
[[18:42:55]] [INFO] Refreshing screenshot...
[[18:42:53]] [SUCCESS] Screenshot refreshed successfully
[[18:42:53]] [SUCCESS] Screenshot refreshed successfully
[[18:42:51]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:42:50]] [SUCCESS] Screenshot refreshed
[[18:42:50]] [INFO] Refreshing screenshot...
[[18:42:44]] [SUCCESS] Screenshot refreshed successfully
[[18:42:44]] [SUCCESS] Screenshot refreshed successfully
[[18:42:43]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:42:43]] [SUCCESS] Screenshot refreshed
[[18:42:43]] [INFO] Refreshing screenshot...
[[18:42:36]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:42:36]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:42:36]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:42:35]] [INFO] ubySifeF65=running
[[18:42:35]] [INFO] Executing action 457/590: cleanupSteps action
[[18:42:35]] [INFO] Skipping remaining steps in failed test case (moving from action 451 to 456), but preserving cleanup steps
[[18:42:35]] [INFO] I4gwigwXSj=fail
[[18:42:35]] [ERROR] Action 451 failed: Element with xpath '//XCUIElementTypeStaticText[@name="Continue shopping"]' not found within timeout of 30.0 seconds
[[18:42:03]] [SUCCESS] Screenshot refreshed successfully
[[18:42:03]] [SUCCESS] Screenshot refreshed successfully
[[18:42:03]] [INFO] I4gwigwXSj=running
[[18:42:03]] [INFO] Executing action 451/590: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:42:02]] [SUCCESS] Screenshot refreshed
[[18:42:02]] [INFO] Refreshing screenshot...
[[18:42:02]] [INFO] eVytJrry9x=pass
[[18:41:58]] [SUCCESS] Screenshot refreshed successfully
[[18:41:58]] [SUCCESS] Screenshot refreshed successfully
[[18:41:58]] [INFO] eVytJrry9x=running
[[18:41:58]] [INFO] Executing action 450/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:41:57]] [SUCCESS] Screenshot refreshed
[[18:41:57]] [INFO] Refreshing screenshot...
[[18:41:57]] [INFO] s8h8VDUIOC=pass
[[18:41:53]] [SUCCESS] Screenshot refreshed successfully
[[18:41:53]] [SUCCESS] Screenshot refreshed successfully
[[18:41:53]] [INFO] s8h8VDUIOC=running
[[18:41:53]] [INFO] Executing action 449/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:41:52]] [SUCCESS] Screenshot refreshed
[[18:41:52]] [INFO] Refreshing screenshot...
[[18:41:52]] [INFO] bkU728TrRF=pass
[[18:41:45]] [SUCCESS] Screenshot refreshed successfully
[[18:41:45]] [SUCCESS] Screenshot refreshed successfully
[[18:41:45]] [INFO] bkU728TrRF=running
[[18:41:45]] [INFO] Executing action 448/590: Tap on element with accessibility_id: Done
[[18:41:45]] [SUCCESS] Screenshot refreshed
[[18:41:45]] [INFO] Refreshing screenshot...
[[18:41:45]] [INFO] ZWpYNcpbFA=pass
[[18:41:40]] [SUCCESS] Screenshot refreshed successfully
[[18:41:40]] [SUCCESS] Screenshot refreshed successfully
[[18:41:40]] [INFO] ZWpYNcpbFA=running
[[18:41:40]] [INFO] Executing action 447/590: Tap on Text: "VIC"
[[18:41:39]] [SUCCESS] Screenshot refreshed
[[18:41:39]] [INFO] Refreshing screenshot...
[[18:41:39]] [INFO] Wld5Urg70o=pass
[[18:41:32]] [SUCCESS] Screenshot refreshed successfully
[[18:41:32]] [SUCCESS] Screenshot refreshed successfully
[[18:41:32]] [INFO] Wld5Urg70o=running
[[18:41:32]] [INFO] Executing action 446/590: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[18:41:32]] [SUCCESS] Screenshot refreshed
[[18:41:32]] [INFO] Refreshing screenshot...
[[18:41:32]] [INFO] QpBLC6BStn=pass
[[18:41:24]] [SUCCESS] Screenshot refreshed successfully
[[18:41:24]] [SUCCESS] Screenshot refreshed successfully
[[18:41:24]] [INFO] QpBLC6BStn=running
[[18:41:24]] [INFO] Executing action 445/590: Tap on element with accessibility_id: delete
[[18:41:24]] [SUCCESS] Screenshot refreshed
[[18:41:24]] [INFO] Refreshing screenshot...
[[18:41:24]] [INFO] G4A3KBlXHq=pass
[[18:41:19]] [SUCCESS] Screenshot refreshed successfully
[[18:41:19]] [SUCCESS] Screenshot refreshed successfully
[[18:41:18]] [INFO] G4A3KBlXHq=running
[[18:41:18]] [INFO] Executing action 444/590: Tap on Text: "Nearby"
[[18:41:18]] [SUCCESS] Screenshot refreshed
[[18:41:18]] [INFO] Refreshing screenshot...
[[18:41:18]] [INFO] uArzgeZYf7=pass
[[18:41:14]] [SUCCESS] Screenshot refreshed successfully
[[18:41:14]] [SUCCESS] Screenshot refreshed successfully
[[18:41:14]] [INFO] uArzgeZYf7=running
[[18:41:14]] [INFO] Executing action 443/590: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[18:41:14]] [SUCCESS] Screenshot refreshed
[[18:41:14]] [INFO] Refreshing screenshot...
[[18:41:14]] [INFO] 3gJsiap2Ds=pass
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [INFO] 3gJsiap2Ds=running
[[18:41:10]] [INFO] Executing action 442/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:41:09]] [SUCCESS] Screenshot refreshed
[[18:41:09]] [INFO] Refreshing screenshot...
[[18:41:09]] [INFO] EReijW5iNX=pass
[[18:40:57]] [SUCCESS] Screenshot refreshed successfully
[[18:40:57]] [SUCCESS] Screenshot refreshed successfully
[[18:40:57]] [INFO] EReijW5iNX=running
[[18:40:57]] [INFO] Executing action 441/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:40:56]] [SUCCESS] Screenshot refreshed
[[18:40:56]] [INFO] Refreshing screenshot...
[[18:40:56]] [INFO] 94ikwhIEE2=pass
[[18:40:50]] [SUCCESS] Screenshot refreshed successfully
[[18:40:50]] [SUCCESS] Screenshot refreshed successfully
[[18:40:50]] [INFO] 94ikwhIEE2=running
[[18:40:50]] [INFO] Executing action 440/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:40:49]] [SUCCESS] Screenshot refreshed
[[18:40:49]] [INFO] Refreshing screenshot...
[[18:40:49]] [INFO] q8oldD8uZt=pass
[[18:40:46]] [SUCCESS] Screenshot refreshed successfully
[[18:40:46]] [SUCCESS] Screenshot refreshed successfully
[[18:40:45]] [INFO] q8oldD8uZt=running
[[18:40:45]] [INFO] Executing action 439/590: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[18:40:45]] [SUCCESS] Screenshot refreshed
[[18:40:45]] [INFO] Refreshing screenshot...
[[18:40:45]] [INFO] Jf2wJyOphY=pass
[[18:40:28]] [SUCCESS] Screenshot refreshed successfully
[[18:40:28]] [SUCCESS] Screenshot refreshed successfully
[[18:40:28]] [INFO] Jf2wJyOphY=running
[[18:40:28]] [INFO] Executing action 438/590: Tap on element with accessibility_id: Add to bag
[[18:40:27]] [SUCCESS] Screenshot refreshed
[[18:40:27]] [INFO] Refreshing screenshot...
[[18:40:27]] [INFO] eRCmRhc3re=pass
[[18:40:12]] [SUCCESS] Screenshot refreshed successfully
[[18:40:12]] [SUCCESS] Screenshot refreshed successfully
[[18:40:12]] [INFO] eRCmRhc3re=running
[[18:40:12]] [INFO] Executing action 437/590: Check if element with text="Broadway" exists
[[18:40:12]] [SUCCESS] Screenshot refreshed
[[18:40:12]] [INFO] Refreshing screenshot...
[[18:40:12]] [INFO] ORI6ZFMBK1=pass
[[18:40:07]] [SUCCESS] Screenshot refreshed successfully
[[18:40:07]] [SUCCESS] Screenshot refreshed successfully
[[18:40:07]] [INFO] ORI6ZFMBK1=running
[[18:40:07]] [INFO] Executing action 436/590: Tap on Text: "Save"
[[18:40:06]] [SUCCESS] Screenshot refreshed
[[18:40:06]] [INFO] Refreshing screenshot...
[[18:40:06]] [INFO] hr0IVckpYI=pass
[[18:40:01]] [SUCCESS] Screenshot refreshed successfully
[[18:40:01]] [SUCCESS] Screenshot refreshed successfully
[[18:40:01]] [INFO] hr0IVckpYI=running
[[18:40:01]] [INFO] Executing action 435/590: Wait till accessibility_id=btnSaveOrContinue
[[18:40:01]] [SUCCESS] Screenshot refreshed
[[18:40:01]] [INFO] Refreshing screenshot...
[[18:40:01]] [INFO] H0ODFz7sWJ=pass
[[18:39:55]] [SUCCESS] Screenshot refreshed successfully
[[18:39:55]] [SUCCESS] Screenshot refreshed successfully
[[18:39:55]] [INFO] H0ODFz7sWJ=running
[[18:39:55]] [INFO] Executing action 434/590: Tap on Text: "2000"
[[18:39:54]] [SUCCESS] Screenshot refreshed
[[18:39:54]] [INFO] Refreshing screenshot...
[[18:39:54]] [INFO] uZHvvAzVfx=pass
[[18:39:49]] [SUCCESS] Screenshot refreshed successfully
[[18:39:49]] [SUCCESS] Screenshot refreshed successfully
[[18:39:49]] [INFO] uZHvvAzVfx=running
[[18:39:49]] [INFO] Executing action 433/590: textClear action
[[18:39:49]] [SUCCESS] Screenshot refreshed
[[18:39:49]] [INFO] Refreshing screenshot...
[[18:39:49]] [INFO] WmNWcsWVHv=pass
[[18:39:43]] [SUCCESS] Screenshot refreshed successfully
[[18:39:43]] [SUCCESS] Screenshot refreshed successfully
[[18:39:43]] [INFO] WmNWcsWVHv=running
[[18:39:43]] [INFO] Executing action 432/590: Tap on element with accessibility_id: Search suburb or postcode
[[18:39:42]] [SUCCESS] Screenshot refreshed
[[18:39:42]] [INFO] Refreshing screenshot...
[[18:39:42]] [INFO] lnjoz8hHUU=pass
[[18:39:37]] [SUCCESS] Screenshot refreshed successfully
[[18:39:37]] [SUCCESS] Screenshot refreshed successfully
[[18:39:36]] [INFO] lnjoz8hHUU=running
[[18:39:36]] [INFO] Executing action 431/590: Tap on Text: "Edit"
[[18:39:36]] [SUCCESS] Screenshot refreshed
[[18:39:36]] [INFO] Refreshing screenshot...
[[18:39:36]] [INFO] letbbewlnA=pass
[[18:39:32]] [SUCCESS] Screenshot refreshed successfully
[[18:39:32]] [SUCCESS] Screenshot refreshed successfully
[[18:39:31]] [INFO] letbbewlnA=running
[[18:39:31]] [INFO] Executing action 430/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:39:31]] [SUCCESS] Screenshot refreshed
[[18:39:31]] [INFO] Refreshing screenshot...
[[18:39:31]] [INFO] trBISwJ8eZ=pass
[[18:39:28]] [SUCCESS] Screenshot refreshed successfully
[[18:39:28]] [SUCCESS] Screenshot refreshed successfully
[[18:39:26]] [INFO] trBISwJ8eZ=running
[[18:39:26]] [INFO] Executing action 429/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:39:25]] [SUCCESS] Screenshot refreshed
[[18:39:25]] [INFO] Refreshing screenshot...
[[18:39:25]] [INFO] foVGMl9wvu=pass
[[18:39:23]] [SUCCESS] Screenshot refreshed successfully
[[18:39:23]] [SUCCESS] Screenshot refreshed successfully
[[18:39:21]] [INFO] foVGMl9wvu=running
[[18:39:21]] [INFO] Executing action 428/590: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:39:21]] [SUCCESS] Screenshot refreshed
[[18:39:21]] [INFO] Refreshing screenshot...
[[18:39:21]] [INFO] 73NABkfWyY=pass
[[18:39:05]] [SUCCESS] Screenshot refreshed successfully
[[18:39:05]] [SUCCESS] Screenshot refreshed successfully
[[18:39:04]] [INFO] 73NABkfWyY=running
[[18:39:04]] [INFO] Executing action 427/590: Check if element with text="Tarneit" exists
[[18:39:04]] [SUCCESS] Screenshot refreshed
[[18:39:04]] [INFO] Refreshing screenshot...
[[18:39:04]] [INFO] pKjXoj4mNg=pass
[[18:38:59]] [SUCCESS] Screenshot refreshed successfully
[[18:38:59]] [SUCCESS] Screenshot refreshed successfully
[[18:38:59]] [INFO] pKjXoj4mNg=running
[[18:38:59]] [INFO] Executing action 426/590: Tap on Text: "Save"
[[18:38:58]] [SUCCESS] Screenshot refreshed
[[18:38:58]] [INFO] Refreshing screenshot...
[[18:38:58]] [INFO] M3dXqigqRv=pass
[[18:38:52]] [SUCCESS] Screenshot refreshed successfully
[[18:38:52]] [SUCCESS] Screenshot refreshed successfully
[[18:38:52]] [INFO] M3dXqigqRv=running
[[18:38:52]] [INFO] Executing action 425/590: Wait till accessibility_id=btnSaveOrContinue
[[18:38:52]] [SUCCESS] Screenshot refreshed
[[18:38:52]] [INFO] Refreshing screenshot...
[[18:38:52]] [INFO] GYRHQr7TWx=pass
[[18:38:47]] [SUCCESS] Screenshot refreshed successfully
[[18:38:47]] [SUCCESS] Screenshot refreshed successfully
[[18:38:47]] [INFO] GYRHQr7TWx=running
[[18:38:47]] [INFO] Executing action 424/590: Tap on Text: "current"
[[18:38:47]] [SUCCESS] Screenshot refreshed
[[18:38:47]] [INFO] Refreshing screenshot...
[[18:38:47]] [INFO] kiM0WyWE9I=pass
[[18:38:42]] [SUCCESS] Screenshot refreshed successfully
[[18:38:42]] [SUCCESS] Screenshot refreshed successfully
[[18:38:42]] [INFO] kiM0WyWE9I=running
[[18:38:42]] [INFO] Executing action 423/590: Wait till accessibility_id=btnCurrentLocationButton
[[18:38:41]] [SUCCESS] Screenshot refreshed
[[18:38:41]] [INFO] Refreshing screenshot...
[[18:38:41]] [INFO] VkUKQbf1Qt=pass
[[18:38:36]] [SUCCESS] Screenshot refreshed successfully
[[18:38:36]] [SUCCESS] Screenshot refreshed successfully
[[18:38:35]] [INFO] VkUKQbf1Qt=running
[[18:38:35]] [INFO] Executing action 422/590: Tap on Text: "Edit"
[[18:38:35]] [SUCCESS] Screenshot refreshed
[[18:38:35]] [INFO] Refreshing screenshot...
[[18:38:35]] [INFO] C6JHhLdWTv=pass
[[18:38:31]] [SUCCESS] Screenshot refreshed successfully
[[18:38:31]] [SUCCESS] Screenshot refreshed successfully
[[18:38:31]] [INFO] C6JHhLdWTv=running
[[18:38:31]] [INFO] Executing action 421/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:38:30]] [SUCCESS] Screenshot refreshed
[[18:38:30]] [INFO] Refreshing screenshot...
[[18:38:30]] [INFO] IupxLP2Jsr=pass
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:27]] [SUCCESS] Screenshot refreshed successfully
[[18:38:26]] [INFO] IupxLP2Jsr=running
[[18:38:26]] [INFO] Executing action 420/590: iOS Function: text - Text: "Uno card"
[[18:38:25]] [SUCCESS] Screenshot refreshed
[[18:38:25]] [INFO] Refreshing screenshot...
[[18:38:25]] [INFO] 70iOOakiG7=pass
[[18:38:20]] [SUCCESS] Screenshot refreshed successfully
[[18:38:20]] [SUCCESS] Screenshot refreshed successfully
[[18:38:19]] [INFO] 70iOOakiG7=running
[[18:38:19]] [INFO] Executing action 419/590: Tap on Text: "Find"
[[18:38:19]] [SUCCESS] Screenshot refreshed
[[18:38:19]] [INFO] Refreshing screenshot...
[[18:38:19]] [INFO] vL26X6PBjc=pass
[[18:38:12]] [SUCCESS] Screenshot refreshed successfully
[[18:38:12]] [SUCCESS] Screenshot refreshed successfully
[[18:38:12]] [INFO] vL26X6PBjc=running
[[18:38:12]] [INFO] Executing action 418/590: Tap if locator exists: accessibility_id="btnUpdate"
[[18:38:11]] [SUCCESS] Screenshot refreshed
[[18:38:11]] [INFO] Refreshing screenshot...
[[18:38:11]] [INFO] E2jpN7BioW=pass
[[18:38:06]] [SUCCESS] Screenshot refreshed successfully
[[18:38:06]] [SUCCESS] Screenshot refreshed successfully
[[18:38:06]] [INFO] E2jpN7BioW=running
[[18:38:06]] [INFO] Executing action 417/590: Tap on Text: "Save"
[[18:38:06]] [SUCCESS] Screenshot refreshed
[[18:38:06]] [INFO] Refreshing screenshot...
[[18:38:06]] [INFO] Sl6eiqZkRm=pass
[[18:38:01]] [SUCCESS] Screenshot refreshed successfully
[[18:38:01]] [SUCCESS] Screenshot refreshed successfully
[[18:38:01]] [INFO] Sl6eiqZkRm=running
[[18:38:01]] [INFO] Executing action 416/590: Wait till accessibility_id=btnSaveOrContinue
[[18:38:00]] [SUCCESS] Screenshot refreshed
[[18:38:00]] [INFO] Refreshing screenshot...
[[18:38:00]] [INFO] mw9GQ4mzRE=pass
[[18:37:55]] [SUCCESS] Screenshot refreshed successfully
[[18:37:55]] [SUCCESS] Screenshot refreshed successfully
[[18:37:55]] [INFO] mw9GQ4mzRE=running
[[18:37:55]] [INFO] Executing action 415/590: Tap on Text: "2000"
[[18:37:54]] [SUCCESS] Screenshot refreshed
[[18:37:54]] [INFO] Refreshing screenshot...
[[18:37:54]] [INFO] kbdEPCPYod=pass
[[18:37:48]] [SUCCESS] Screenshot refreshed successfully
[[18:37:48]] [SUCCESS] Screenshot refreshed successfully
[[18:37:48]] [INFO] kbdEPCPYod=running
[[18:37:48]] [INFO] Executing action 414/590: textClear action
[[18:37:48]] [SUCCESS] Screenshot refreshed
[[18:37:48]] [INFO] Refreshing screenshot...
[[18:37:48]] [INFO] 8WCusTZ8q9=pass
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:42]] [SUCCESS] Screenshot refreshed successfully
[[18:37:42]] [INFO] 8WCusTZ8q9=running
[[18:37:42]] [INFO] Executing action 413/590: Tap on element with accessibility_id: Search suburb or postcode
[[18:37:42]] [SUCCESS] Screenshot refreshed
[[18:37:42]] [INFO] Refreshing screenshot...
[[18:37:42]] [INFO] QMXBlswP6H=pass
[[18:37:38]] [SUCCESS] Screenshot refreshed successfully
[[18:37:38]] [SUCCESS] Screenshot refreshed successfully
[[18:37:37]] [INFO] QMXBlswP6H=running
[[18:37:37]] [INFO] Executing action 412/590: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:37:37]] [SUCCESS] Screenshot refreshed
[[18:37:37]] [INFO] Refreshing screenshot...
[[18:37:37]] [INFO] m0956RsrdM=pass
[[18:37:35]] [SUCCESS] Screenshot refreshed successfully
[[18:37:35]] [SUCCESS] Screenshot refreshed successfully
[[18:37:32]] [INFO] m0956RsrdM=running
[[18:37:32]] [INFO] Executing action 411/590: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:37:32]] [SUCCESS] Screenshot refreshed
[[18:37:32]] [INFO] Refreshing screenshot...
[[18:37:32]] [SUCCESS] Screenshot refreshed
[[18:37:32]] [INFO] Refreshing screenshot...
[[18:37:27]] [SUCCESS] Screenshot refreshed successfully
[[18:37:27]] [SUCCESS] Screenshot refreshed successfully
[[18:37:27]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:37:26]] [SUCCESS] Screenshot refreshed
[[18:37:26]] [INFO] Refreshing screenshot...
[[18:37:22]] [SUCCESS] Screenshot refreshed successfully
[[18:37:22]] [SUCCESS] Screenshot refreshed successfully
[[18:37:22]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:37:22]] [SUCCESS] Screenshot refreshed
[[18:37:22]] [INFO] Refreshing screenshot...
[[18:37:17]] [SUCCESS] Screenshot refreshed successfully
[[18:37:17]] [SUCCESS] Screenshot refreshed successfully
[[18:37:17]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:37:16]] [SUCCESS] Screenshot refreshed
[[18:37:16]] [INFO] Refreshing screenshot...
[[18:37:12]] [SUCCESS] Screenshot refreshed successfully
[[18:37:12]] [SUCCESS] Screenshot refreshed successfully
[[18:37:12]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:37:11]] [SUCCESS] Screenshot refreshed
[[18:37:11]] [INFO] Refreshing screenshot...
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:37:05]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:37:05]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:37:05]] [INFO] HlWGryBWT9=running
[[18:37:05]] [INFO] Executing action 410/590: Execute Test Case: Kmart-Signin (5 steps)
[[18:37:05]] [SUCCESS] Screenshot refreshed
[[18:37:05]] [INFO] Refreshing screenshot...
[[18:37:05]] [INFO] Azb1flbIJJ=pass
[[18:37:01]] [SUCCESS] Screenshot refreshed successfully
[[18:37:01]] [SUCCESS] Screenshot refreshed successfully
[[18:37:01]] [INFO] Azb1flbIJJ=running
[[18:37:01]] [INFO] Executing action 409/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:37:00]] [SUCCESS] Screenshot refreshed
[[18:37:00]] [INFO] Refreshing screenshot...
[[18:37:00]] [INFO] 2xC5fLfLe8=pass
[[18:36:58]] [SUCCESS] Screenshot refreshed successfully
[[18:36:58]] [SUCCESS] Screenshot refreshed successfully
[[18:36:58]] [INFO] 2xC5fLfLe8=running
[[18:36:58]] [INFO] Executing action 408/590: iOS Function: alert_accept
[[18:36:57]] [SUCCESS] Screenshot refreshed
[[18:36:57]] [INFO] Refreshing screenshot...
[[18:36:57]] [INFO] Y8vz7AJD1i=pass
[[18:36:49]] [SUCCESS] Screenshot refreshed successfully
[[18:36:49]] [SUCCESS] Screenshot refreshed successfully
[[18:36:49]] [INFO] Y8vz7AJD1i=running
[[18:36:49]] [INFO] Executing action 407/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:36:48]] [SUCCESS] Screenshot refreshed
[[18:36:48]] [INFO] Refreshing screenshot...
[[18:36:48]] [INFO] H9fy9qcFbZ=pass
[[18:36:35]] [SUCCESS] Screenshot refreshed successfully
[[18:36:35]] [SUCCESS] Screenshot refreshed successfully
[[18:36:34]] [INFO] H9fy9qcFbZ=running
[[18:36:34]] [INFO] Executing action 406/590: Restart app: env[appid]
[[18:36:34]] [SUCCESS] Screenshot refreshed
[[18:36:34]] [INFO] Refreshing screenshot...
[[18:36:34]] [SUCCESS] Screenshot refreshed
[[18:36:34]] [INFO] Refreshing screenshot...
[[18:36:30]] [SUCCESS] Screenshot refreshed successfully
[[18:36:30]] [SUCCESS] Screenshot refreshed successfully
[[18:36:30]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:36:30]] [SUCCESS] Screenshot refreshed
[[18:36:30]] [INFO] Refreshing screenshot...
[[18:36:18]] [SUCCESS] Screenshot refreshed successfully
[[18:36:18]] [SUCCESS] Screenshot refreshed successfully
[[18:36:18]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:36:18]] [SUCCESS] Screenshot refreshed
[[18:36:18]] [INFO] Refreshing screenshot...
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:36:13]] [SUCCESS] Screenshot refreshed
[[18:36:13]] [INFO] Refreshing screenshot...
[[18:36:10]] [SUCCESS] Screenshot refreshed successfully
[[18:36:10]] [SUCCESS] Screenshot refreshed successfully
[[18:36:09]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:36:09]] [SUCCESS] Screenshot refreshed
[[18:36:09]] [INFO] Refreshing screenshot...
[[18:36:02]] [SUCCESS] Screenshot refreshed successfully
[[18:36:02]] [SUCCESS] Screenshot refreshed successfully
[[18:36:02]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:36:01]] [SUCCESS] Screenshot refreshed
[[18:36:01]] [INFO] Refreshing screenshot...
[[18:35:55]] [SUCCESS] Screenshot refreshed successfully
[[18:35:55]] [SUCCESS] Screenshot refreshed successfully
[[18:35:55]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:35:55]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:35:55]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:35:55]] [INFO] OMgc2gHHyq=running
[[18:35:55]] [INFO] Executing action 405/590: cleanupSteps action
[[18:35:54]] [SUCCESS] Screenshot refreshed
[[18:35:54]] [INFO] Refreshing screenshot...
[[18:35:54]] [INFO] x4yLCZHaCR=pass
[[18:35:51]] [SUCCESS] Screenshot refreshed successfully
[[18:35:51]] [SUCCESS] Screenshot refreshed successfully
[[18:35:51]] [INFO] x4yLCZHaCR=running
[[18:35:51]] [INFO] Executing action 404/590: Terminate app: env[appid]
[[18:35:50]] [SUCCESS] Screenshot refreshed
[[18:35:50]] [INFO] Refreshing screenshot...
[[18:35:50]] [INFO] 2p13JoJbbA=pass
[[18:35:46]] [SUCCESS] Screenshot refreshed successfully
[[18:35:46]] [SUCCESS] Screenshot refreshed successfully
[[18:35:46]] [INFO] 2p13JoJbbA=running
[[18:35:46]] [INFO] Executing action 403/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:35:45]] [SUCCESS] Screenshot refreshed
[[18:35:45]] [INFO] Refreshing screenshot...
[[18:35:45]] [INFO] qHdMgerbTE=pass
[[18:35:41]] [SUCCESS] Screenshot refreshed successfully
[[18:35:41]] [SUCCESS] Screenshot refreshed successfully
[[18:35:41]] [INFO] qHdMgerbTE=running
[[18:35:41]] [INFO] Executing action 402/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:35:40]] [SUCCESS] Screenshot refreshed
[[18:35:40]] [INFO] Refreshing screenshot...
[[18:35:40]] [INFO] F4NGh9HrLw=pass
[[18:35:38]] [SUCCESS] Screenshot refreshed successfully
[[18:35:38]] [SUCCESS] Screenshot refreshed successfully
[[18:35:35]] [INFO] F4NGh9HrLw=running
[[18:35:35]] [INFO] Executing action 401/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:35:35]] [SUCCESS] Screenshot refreshed
[[18:35:35]] [INFO] Refreshing screenshot...
[[18:35:35]] [SUCCESS] Screenshot refreshed
[[18:35:35]] [INFO] Refreshing screenshot...
[[18:35:30]] [INFO] Executing Multi Step action step 42/42: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:35:30]] [SUCCESS] Screenshot refreshed successfully
[[18:35:30]] [SUCCESS] Screenshot refreshed successfully
[[18:35:30]] [SUCCESS] Screenshot refreshed
[[18:35:30]] [INFO] Refreshing screenshot...
[[18:35:28]] [SUCCESS] Screenshot refreshed successfully
[[18:35:28]] [SUCCESS] Screenshot refreshed successfully
[[18:35:26]] [INFO] Executing Multi Step action step 41/42: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:35:25]] [SUCCESS] Screenshot refreshed
[[18:35:25]] [INFO] Refreshing screenshot...
[[18:35:18]] [SUCCESS] Screenshot refreshed successfully
[[18:35:18]] [SUCCESS] Screenshot refreshed successfully
[[18:35:18]] [INFO] Executing Multi Step action step 40/42: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[18:35:18]] [SUCCESS] Screenshot refreshed
[[18:35:18]] [INFO] Refreshing screenshot...
[[18:35:14]] [SUCCESS] Screenshot refreshed successfully
[[18:35:14]] [SUCCESS] Screenshot refreshed successfully
[[18:35:14]] [INFO] Executing Multi Step action step 39/42: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:35:13]] [SUCCESS] Screenshot refreshed
[[18:35:13]] [INFO] Refreshing screenshot...
[[18:35:01]] [INFO] Executing Multi Step action step 38/42: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:35:01]] [SUCCESS] Screenshot refreshed successfully
[[18:35:01]] [SUCCESS] Screenshot refreshed successfully
[[18:35:01]] [SUCCESS] Screenshot refreshed
[[18:35:01]] [INFO] Refreshing screenshot...
[[18:34:59]] [SUCCESS] Screenshot refreshed successfully
[[18:34:59]] [SUCCESS] Screenshot refreshed successfully
[[18:34:56]] [INFO] Executing Multi Step action step 37/42: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:34:55]] [SUCCESS] Screenshot refreshed
[[18:34:55]] [INFO] Refreshing screenshot...
[[18:34:52]] [SUCCESS] Screenshot refreshed successfully
[[18:34:52]] [SUCCESS] Screenshot refreshed successfully
[[18:34:51]] [INFO] Executing Multi Step action step 36/42: Tap on image: banner-close-updated.png
[[18:34:50]] [SUCCESS] Screenshot refreshed
[[18:34:50]] [INFO] Refreshing screenshot...
[[18:34:47]] [INFO] Executing Multi Step action step 35/42: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[18:34:47]] [SUCCESS] Screenshot refreshed successfully
[[18:34:47]] [SUCCESS] Screenshot refreshed successfully
[[18:34:47]] [SUCCESS] Screenshot refreshed
[[18:34:47]] [INFO] Refreshing screenshot...
[[18:34:43]] [SUCCESS] Screenshot refreshed successfully
[[18:34:43]] [SUCCESS] Screenshot refreshed successfully
[[18:34:43]] [INFO] Executing Multi Step action step 34/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[18:34:42]] [SUCCESS] Screenshot refreshed
[[18:34:42]] [INFO] Refreshing screenshot...
[[18:34:38]] [SUCCESS] Screenshot refreshed successfully
[[18:34:38]] [SUCCESS] Screenshot refreshed successfully
[[18:34:38]] [INFO] Executing Multi Step action step 33/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[18:34:38]] [SUCCESS] Screenshot refreshed
[[18:34:38]] [INFO] Refreshing screenshot...
[[18:34:34]] [SUCCESS] Screenshot refreshed successfully
[[18:34:34]] [SUCCESS] Screenshot refreshed successfully
[[18:34:34]] [INFO] Executing Multi Step action step 32/42: Tap on image: banner-close-updated.png
[[18:34:33]] [SUCCESS] Screenshot refreshed
[[18:34:33]] [INFO] Refreshing screenshot...
[[18:34:30]] [INFO] Executing Multi Step action step 31/42: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[18:34:30]] [SUCCESS] Screenshot refreshed successfully
[[18:34:30]] [SUCCESS] Screenshot refreshed successfully
[[18:34:30]] [SUCCESS] Screenshot refreshed
[[18:34:30]] [INFO] Refreshing screenshot...
[[18:34:28]] [SUCCESS] Screenshot refreshed successfully
[[18:34:28]] [SUCCESS] Screenshot refreshed successfully
[[18:34:26]] [INFO] Executing Multi Step action step 30/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[18:34:25]] [SUCCESS] Screenshot refreshed
[[18:34:25]] [INFO] Refreshing screenshot...
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:21]] [INFO] Executing Multi Step action step 29/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[18:34:20]] [SUCCESS] Screenshot refreshed
[[18:34:20]] [INFO] Refreshing screenshot...
[[18:34:16]] [SUCCESS] Screenshot refreshed successfully
[[18:34:16]] [SUCCESS] Screenshot refreshed successfully
[[18:34:16]] [INFO] Executing Multi Step action step 28/42: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:34:16]] [SUCCESS] Screenshot refreshed
[[18:34:16]] [INFO] Refreshing screenshot...
[[18:34:12]] [SUCCESS] Screenshot refreshed successfully
[[18:34:12]] [SUCCESS] Screenshot refreshed successfully
[[18:34:12]] [INFO] Executing Multi Step action step 27/42: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[18:34:12]] [SUCCESS] Screenshot refreshed
[[18:34:12]] [INFO] Refreshing screenshot...
[[18:34:08]] [SUCCESS] Screenshot refreshed successfully
[[18:34:08]] [SUCCESS] Screenshot refreshed successfully
[[18:34:08]] [INFO] Executing Multi Step action step 26/42: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[18:34:07]] [SUCCESS] Screenshot refreshed
[[18:34:07]] [INFO] Refreshing screenshot...
[[18:34:03]] [SUCCESS] Screenshot refreshed successfully
[[18:34:03]] [SUCCESS] Screenshot refreshed successfully
[[18:34:03]] [INFO] Executing Multi Step action step 25/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[18:34:03]] [SUCCESS] Screenshot refreshed
[[18:34:03]] [INFO] Refreshing screenshot...
[[18:33:59]] [SUCCESS] Screenshot refreshed successfully
[[18:33:59]] [SUCCESS] Screenshot refreshed successfully
[[18:33:59]] [INFO] Executing Multi Step action step 24/42: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:33:58]] [SUCCESS] Screenshot refreshed
[[18:33:58]] [INFO] Refreshing screenshot...
[[18:33:54]] [SUCCESS] Screenshot refreshed successfully
[[18:33:54]] [SUCCESS] Screenshot refreshed successfully
[[18:33:54]] [INFO] Executing Multi Step action step 23/42: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[18:33:53]] [SUCCESS] Screenshot refreshed
[[18:33:53]] [INFO] Refreshing screenshot...
[[18:33:49]] [SUCCESS] Screenshot refreshed successfully
[[18:33:49]] [SUCCESS] Screenshot refreshed successfully
[[18:33:49]] [INFO] Executing Multi Step action step 22/42: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[18:33:48]] [SUCCESS] Screenshot refreshed
[[18:33:48]] [INFO] Refreshing screenshot...
[[18:33:40]] [SUCCESS] Screenshot refreshed successfully
[[18:33:40]] [SUCCESS] Screenshot refreshed successfully
[[18:33:40]] [INFO] Executing Multi Step action step 21/42: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[18:33:40]] [SUCCESS] Screenshot refreshed
[[18:33:40]] [INFO] Refreshing screenshot...
[[18:33:32]] [SUCCESS] Screenshot refreshed successfully
[[18:33:32]] [SUCCESS] Screenshot refreshed successfully
[[18:33:31]] [INFO] Executing Multi Step action step 20/42: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[18:33:31]] [SUCCESS] Screenshot refreshed
[[18:33:31]] [INFO] Refreshing screenshot...
[[18:33:27]] [SUCCESS] Screenshot refreshed successfully
[[18:33:27]] [SUCCESS] Screenshot refreshed successfully
[[18:33:27]] [INFO] Executing Multi Step action step 19/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[18:33:26]] [SUCCESS] Screenshot refreshed
[[18:33:26]] [INFO] Refreshing screenshot...
[[18:33:18]] [SUCCESS] Screenshot refreshed successfully
[[18:33:18]] [SUCCESS] Screenshot refreshed successfully
[[18:33:18]] [INFO] Executing Multi Step action step 18/42: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[18:33:18]] [SUCCESS] Screenshot refreshed
[[18:33:18]] [INFO] Refreshing screenshot...
[[18:33:14]] [SUCCESS] Screenshot refreshed successfully
[[18:33:14]] [SUCCESS] Screenshot refreshed successfully
[[18:33:14]] [INFO] Executing Multi Step action step 17/42: Tap on image: env[delivery-address-img]
[[18:33:13]] [SUCCESS] Screenshot refreshed
[[18:33:13]] [INFO] Refreshing screenshot...
[[18:33:09]] [SUCCESS] Screenshot refreshed successfully
[[18:33:09]] [SUCCESS] Screenshot refreshed successfully
[[18:33:09]] [INFO] Executing Multi Step action step 16/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:33:09]] [SUCCESS] Screenshot refreshed
[[18:33:09]] [INFO] Refreshing screenshot...
[[18:33:01]] [SUCCESS] Screenshot refreshed successfully
[[18:33:01]] [SUCCESS] Screenshot refreshed successfully
[[18:33:01]] [INFO] Executing Multi Step action step 15/42: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[18:33:01]] [SUCCESS] Screenshot refreshed
[[18:33:01]] [INFO] Refreshing screenshot...
[[18:32:55]] [SUCCESS] Screenshot refreshed successfully
[[18:32:55]] [SUCCESS] Screenshot refreshed successfully
[[18:32:55]] [INFO] Executing Multi Step action step 14/42: Tap on Text: "address"
[[18:32:55]] [SUCCESS] Screenshot refreshed
[[18:32:55]] [INFO] Refreshing screenshot...
[[18:32:50]] [SUCCESS] Screenshot refreshed successfully
[[18:32:50]] [SUCCESS] Screenshot refreshed successfully
[[18:32:50]] [INFO] Executing Multi Step action step 13/42: iOS Function: text - Text: " "
[[18:32:49]] [SUCCESS] Screenshot refreshed
[[18:32:49]] [INFO] Refreshing screenshot...
[[18:32:42]] [SUCCESS] Screenshot refreshed successfully
[[18:32:42]] [SUCCESS] Screenshot refreshed successfully
[[18:32:42]] [INFO] Executing Multi Step action step 12/42: textClear action
[[18:32:42]] [SUCCESS] Screenshot refreshed
[[18:32:42]] [INFO] Refreshing screenshot...
[[18:32:38]] [SUCCESS] Screenshot refreshed successfully
[[18:32:38]] [SUCCESS] Screenshot refreshed successfully
[[18:32:38]] [INFO] Executing Multi Step action step 11/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[18:32:37]] [SUCCESS] Screenshot refreshed
[[18:32:37]] [INFO] Refreshing screenshot...
[[18:32:30]] [SUCCESS] Screenshot refreshed successfully
[[18:32:30]] [SUCCESS] Screenshot refreshed successfully
[[18:32:30]] [INFO] Executing Multi Step action step 10/42: textClear action
[[18:32:29]] [SUCCESS] Screenshot refreshed
[[18:32:29]] [INFO] Refreshing screenshot...
[[18:32:25]] [SUCCESS] Screenshot refreshed successfully
[[18:32:25]] [SUCCESS] Screenshot refreshed successfully
[[18:32:25]] [INFO] Executing Multi Step action step 9/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:32:24]] [SUCCESS] Screenshot refreshed
[[18:32:24]] [INFO] Refreshing screenshot...
[[18:32:17]] [SUCCESS] Screenshot refreshed successfully
[[18:32:17]] [SUCCESS] Screenshot refreshed successfully
[[18:32:17]] [INFO] Executing Multi Step action step 8/42: textClear action
[[18:32:16]] [SUCCESS] Screenshot refreshed
[[18:32:16]] [INFO] Refreshing screenshot...
[[18:32:12]] [SUCCESS] Screenshot refreshed successfully
[[18:32:12]] [SUCCESS] Screenshot refreshed successfully
[[18:32:12]] [INFO] Executing Multi Step action step 7/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[18:32:11]] [SUCCESS] Screenshot refreshed
[[18:32:11]] [INFO] Refreshing screenshot...
[[18:32:05]] [SUCCESS] Screenshot refreshed successfully
[[18:32:05]] [SUCCESS] Screenshot refreshed successfully
[[18:32:05]] [INFO] Executing Multi Step action step 6/42: textClear action
[[18:32:04]] [SUCCESS] Screenshot refreshed
[[18:32:04]] [INFO] Refreshing screenshot...
[[18:32:01]] [SUCCESS] Screenshot refreshed successfully
[[18:32:01]] [SUCCESS] Screenshot refreshed successfully
[[18:32:00]] [INFO] Executing Multi Step action step 5/42: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[18:32:00]] [SUCCESS] Screenshot refreshed
[[18:32:00]] [INFO] Refreshing screenshot...
[[18:31:56]] [SUCCESS] Screenshot refreshed successfully
[[18:31:56]] [SUCCESS] Screenshot refreshed successfully
[[18:31:55]] [INFO] Executing Multi Step action step 4/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[18:31:55]] [SUCCESS] Screenshot refreshed
[[18:31:55]] [INFO] Refreshing screenshot...
[[18:31:44]] [SUCCESS] Screenshot refreshed successfully
[[18:31:44]] [SUCCESS] Screenshot refreshed successfully
[[18:31:44]] [INFO] Executing Multi Step action step 3/42: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[18:31:43]] [SUCCESS] Screenshot refreshed
[[18:31:43]] [INFO] Refreshing screenshot...
[[18:31:39]] [SUCCESS] Screenshot refreshed successfully
[[18:31:39]] [SUCCESS] Screenshot refreshed successfully
[[18:31:39]] [INFO] Executing Multi Step action step 2/42: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:31:39]] [SUCCESS] Screenshot refreshed
[[18:31:39]] [INFO] Refreshing screenshot...
[[18:31:33]] [SUCCESS] Screenshot refreshed successfully
[[18:31:33]] [SUCCESS] Screenshot refreshed successfully
[[18:31:33]] [INFO] Executing Multi Step action step 1/42: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:31:33]] [INFO] Loaded 42 steps from test case: Delivery Buy Steps
[[18:31:33]] [INFO] Loading steps for multiStep action: Delivery Buy Steps
[[18:31:33]] [INFO] ZxObWodIp8=running
[[18:31:33]] [INFO] Executing action 400/590: Execute Test Case: Delivery Buy Steps (41 steps)
[[18:31:32]] [SUCCESS] Screenshot refreshed
[[18:31:32]] [INFO] Refreshing screenshot...
[[18:31:32]] [INFO] ZlrZ0BjA1R=pass
[[18:31:20]] [INFO] ZlrZ0BjA1R=running
[[18:31:20]] [INFO] Executing action 399/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:31:20]] [SUCCESS] Screenshot refreshed successfully
[[18:31:20]] [SUCCESS] Screenshot refreshed successfully
[[18:31:20]] [SUCCESS] Screenshot refreshed
[[18:31:20]] [INFO] Refreshing screenshot...
[[18:31:20]] [INFO] F4NGh9HrLw=pass
[[18:31:16]] [SUCCESS] Screenshot refreshed successfully
[[18:31:16]] [SUCCESS] Screenshot refreshed successfully
[[18:31:15]] [INFO] F4NGh9HrLw=running
[[18:31:15]] [INFO] Executing action 398/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:31:15]] [SUCCESS] Screenshot refreshed
[[18:31:15]] [INFO] Refreshing screenshot...
[[18:31:15]] [INFO] 7mnBGa2GCk=pass
[[18:31:02]] [SUCCESS] Screenshot refreshed successfully
[[18:31:02]] [SUCCESS] Screenshot refreshed successfully
[[18:31:02]] [INFO] 7mnBGa2GCk=running
[[18:31:02]] [INFO] Executing action 397/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Save my location"]"
[[18:31:01]] [SUCCESS] Screenshot refreshed
[[18:31:01]] [INFO] Refreshing screenshot...
[[18:31:01]] [INFO] XOaZPEqzKU=pass
[[18:30:49]] [SUCCESS] Screenshot refreshed successfully
[[18:30:49]] [SUCCESS] Screenshot refreshed successfully
[[18:30:49]] [INFO] XOaZPEqzKU=running
[[18:30:49]] [INFO] Executing action 396/590: Tap if locator exists: accessibility_id="Add to bag"
[[18:30:48]] [SUCCESS] Screenshot refreshed
[[18:30:48]] [INFO] Refreshing screenshot...
[[18:30:48]] [INFO] hlB6ARmBVC=pass
[[18:30:47]] [SUCCESS] Screenshot refreshed successfully
[[18:30:47]] [SUCCESS] Screenshot refreshed successfully
[[18:30:46]] [INFO] hlB6ARmBVC=running
[[18:30:46]] [INFO] Executing action 395/590: Tap on image if exists: add-to-bag-ip14.png
[[18:30:46]] [SUCCESS] Screenshot refreshed
[[18:30:46]] [INFO] Refreshing screenshot...
[[18:30:46]] [INFO] ALWzI9hXIc=pass
[[18:30:40]] [SUCCESS] Screenshot refreshed successfully
[[18:30:40]] [SUCCESS] Screenshot refreshed successfully
[[18:30:40]] [INFO] ALWzI9hXIc=running
[[18:30:40]] [INFO] Executing action 394/590: Swipe from (50%, 50%) to (50%, 30%)
[[18:30:39]] [SUCCESS] Screenshot refreshed
[[18:30:39]] [INFO] Refreshing screenshot...
[[18:30:39]] [INFO] CcFsA41sKp=pass
[[18:30:35]] [SUCCESS] Screenshot refreshed successfully
[[18:30:35]] [SUCCESS] Screenshot refreshed successfully
[[18:30:35]] [INFO] CcFsA41sKp=running
[[18:30:35]] [INFO] Executing action 393/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:30:34]] [SUCCESS] Screenshot refreshed
[[18:30:34]] [INFO] Refreshing screenshot...
[[18:30:34]] [INFO] 8XWyF2kgwW=pass
[[18:30:27]] [INFO] 8XWyF2kgwW=running
[[18:30:27]] [INFO] Executing action 392/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:30:27]] [SUCCESS] Screenshot refreshed successfully
[[18:30:27]] [SUCCESS] Screenshot refreshed successfully
[[18:30:26]] [SUCCESS] Screenshot refreshed
[[18:30:26]] [INFO] Refreshing screenshot...
[[18:30:26]] [INFO] qG4RkNac30=pass
[[18:30:22]] [SUCCESS] Screenshot refreshed successfully
[[18:30:22]] [SUCCESS] Screenshot refreshed successfully
[[18:30:22]] [INFO] qG4RkNac30=running
[[18:30:22]] [INFO] Executing action 391/590: iOS Function: text - Text: "P_42691341"
[[18:30:22]] [SUCCESS] Screenshot refreshed
[[18:30:22]] [INFO] Refreshing screenshot...
[[18:30:22]] [INFO] Jtn2FK4THX=pass
[[18:30:16]] [SUCCESS] Screenshot refreshed successfully
[[18:30:16]] [SUCCESS] Screenshot refreshed successfully
[[18:30:15]] [INFO] Jtn2FK4THX=running
[[18:30:15]] [INFO] Executing action 390/590: Tap on Text: "Find"
[[18:30:15]] [SUCCESS] Screenshot refreshed
[[18:30:15]] [INFO] Refreshing screenshot...
[[18:30:15]] [INFO] tWq2Qzn22D=pass
[[18:30:11]] [SUCCESS] Screenshot refreshed successfully
[[18:30:11]] [SUCCESS] Screenshot refreshed successfully
[[18:30:11]] [INFO] tWq2Qzn22D=running
[[18:30:11]] [INFO] Executing action 389/590: Tap on image: env[device-back-img]
[[18:30:10]] [SUCCESS] Screenshot refreshed
[[18:30:10]] [INFO] Refreshing screenshot...
[[18:30:10]] [INFO] ysJIY9A3gq=pass
[[18:29:57]] [SUCCESS] Screenshot refreshed successfully
[[18:29:57]] [SUCCESS] Screenshot refreshed successfully
[[18:29:57]] [INFO] ysJIY9A3gq=running
[[18:29:57]] [INFO] Executing action 388/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]"
[[18:29:57]] [SUCCESS] Screenshot refreshed
[[18:29:57]] [INFO] Refreshing screenshot...
[[18:29:57]] [INFO] jmKjclMUWT=pass
[[18:29:52]] [SUCCESS] Screenshot refreshed successfully
[[18:29:52]] [SUCCESS] Screenshot refreshed successfully
[[18:29:52]] [INFO] jmKjclMUWT=running
[[18:29:52]] [INFO] Executing action 387/590: Tap on Text: "current"
[[18:29:51]] [SUCCESS] Screenshot refreshed
[[18:29:51]] [INFO] Refreshing screenshot...
[[18:29:51]] [INFO] UoH0wdtcLk=pass
[[18:29:46]] [SUCCESS] Screenshot refreshed successfully
[[18:29:46]] [SUCCESS] Screenshot refreshed successfully
[[18:29:46]] [INFO] UoH0wdtcLk=running
[[18:29:46]] [INFO] Executing action 386/590: Tap on Text: "Edit"
[[18:29:45]] [SUCCESS] Screenshot refreshed
[[18:29:45]] [INFO] Refreshing screenshot...
[[18:29:45]] [INFO] U48qCNydwd=pass
[[18:29:40]] [SUCCESS] Screenshot refreshed successfully
[[18:29:40]] [SUCCESS] Screenshot refreshed successfully
[[18:29:40]] [INFO] U48qCNydwd=running
[[18:29:40]] [INFO] Executing action 385/590: Restart app: env[appid]
[[18:29:39]] [SUCCESS] Screenshot refreshed
[[18:29:39]] [INFO] Refreshing screenshot...
[[18:29:39]] [INFO] XjclKOaCTh=pass
[[18:29:35]] [SUCCESS] Screenshot refreshed successfully
[[18:29:35]] [SUCCESS] Screenshot refreshed successfully
[[18:29:35]] [INFO] XjclKOaCTh=running
[[18:29:35]] [INFO] Executing action 384/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:29:34]] [SUCCESS] Screenshot refreshed
[[18:29:34]] [INFO] Refreshing screenshot...
[[18:29:34]] [INFO] q6cKxgMAIn=pass
[[18:29:31]] [SUCCESS] Screenshot refreshed successfully
[[18:29:31]] [SUCCESS] Screenshot refreshed successfully
[[18:29:30]] [INFO] q6cKxgMAIn=running
[[18:29:30]] [INFO] Executing action 383/590: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[18:29:30]] [SUCCESS] Screenshot refreshed
[[18:29:30]] [INFO] Refreshing screenshot...
[[18:29:30]] [INFO] zdh8hKYC1a=pass
[[18:29:26]] [SUCCESS] Screenshot refreshed successfully
[[18:29:26]] [SUCCESS] Screenshot refreshed successfully
[[18:29:26]] [INFO] zdh8hKYC1a=running
[[18:29:26]] [INFO] Executing action 382/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[18:29:25]] [SUCCESS] Screenshot refreshed
[[18:29:25]] [INFO] Refreshing screenshot...
[[18:29:25]] [INFO] P4b2BITpCf=pass
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [INFO] P4b2BITpCf=running
[[18:29:22]] [INFO] Executing action 381/590: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[18:29:22]] [SUCCESS] Screenshot refreshed
[[18:29:22]] [INFO] Refreshing screenshot...
[[18:29:22]] [INFO] inrxgdWzXr=pass
[[18:29:17]] [SUCCESS] Screenshot refreshed successfully
[[18:29:17]] [SUCCESS] Screenshot refreshed successfully
[[18:29:17]] [INFO] inrxgdWzXr=running
[[18:29:17]] [INFO] Executing action 380/590: Tap on Text: "Store"
[[18:29:16]] [SUCCESS] Screenshot refreshed
[[18:29:16]] [INFO] Refreshing screenshot...
[[18:29:16]] [INFO] inrxgdWzXr=pass
[[18:29:12]] [SUCCESS] Screenshot refreshed successfully
[[18:29:12]] [SUCCESS] Screenshot refreshed successfully
[[18:29:12]] [INFO] inrxgdWzXr=running
[[18:29:12]] [INFO] Executing action 379/590: Tap on Text: "receipts"
[[18:29:11]] [SUCCESS] Screenshot refreshed
[[18:29:11]] [INFO] Refreshing screenshot...
[[18:29:11]] [INFO] GEMv6goQtW=pass
[[18:29:08]] [SUCCESS] Screenshot refreshed successfully
[[18:29:08]] [SUCCESS] Screenshot refreshed successfully
[[18:29:07]] [INFO] GEMv6goQtW=running
[[18:29:07]] [INFO] Executing action 378/590: Tap on image: env[device-back-img]
[[18:29:07]] [SUCCESS] Screenshot refreshed
[[18:29:07]] [INFO] Refreshing screenshot...
[[18:29:07]] [INFO] DhWa2PCBXE=pass
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:04]] [SUCCESS] Screenshot refreshed successfully
[[18:29:04]] [INFO] DhWa2PCBXE=running
[[18:29:04]] [INFO] Executing action 377/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[18:29:03]] [SUCCESS] Screenshot refreshed
[[18:29:03]] [INFO] Refreshing screenshot...
[[18:29:03]] [INFO] pk2DLZFBmx=pass
[[18:28:59]] [SUCCESS] Screenshot refreshed successfully
[[18:28:59]] [SUCCESS] Screenshot refreshed successfully
[[18:28:59]] [INFO] pk2DLZFBmx=running
[[18:28:59]] [INFO] Executing action 376/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[18:28:58]] [SUCCESS] Screenshot refreshed
[[18:28:58]] [INFO] Refreshing screenshot...
[[18:28:58]] [INFO] ShJSdXvmVL=pass
[[18:28:55]] [SUCCESS] Screenshot refreshed successfully
[[18:28:55]] [SUCCESS] Screenshot refreshed successfully
[[18:28:55]] [INFO] ShJSdXvmVL=running
[[18:28:55]] [INFO] Executing action 375/590: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[18:28:54]] [SUCCESS] Screenshot refreshed
[[18:28:54]] [INFO] Refreshing screenshot...
[[18:28:54]] [INFO] A57bC3QuEM=pass
[[18:28:49]] [SUCCESS] Screenshot refreshed successfully
[[18:28:49]] [SUCCESS] Screenshot refreshed successfully
[[18:28:49]] [INFO] A57bC3QuEM=running
[[18:28:49]] [INFO] Executing action 374/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:28:48]] [SUCCESS] Screenshot refreshed
[[18:28:48]] [INFO] Refreshing screenshot...
[[18:28:48]] [INFO] d6vTfR4Y0D=pass
[[18:28:44]] [SUCCESS] Screenshot refreshed successfully
[[18:28:44]] [SUCCESS] Screenshot refreshed successfully
[[18:28:44]] [INFO] d6vTfR4Y0D=running
[[18:28:44]] [INFO] Executing action 373/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:28:43]] [SUCCESS] Screenshot refreshed
[[18:28:43]] [INFO] Refreshing screenshot...
[[18:28:43]] [INFO] pe9W6tZdXT=pass
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [INFO] pe9W6tZdXT=running
[[18:28:38]] [INFO] Executing action 372/590: iOS Function: text - Text: "env[uname-op]"
[[18:28:38]] [SUCCESS] Screenshot refreshed
[[18:28:38]] [INFO] Refreshing screenshot...
[[18:28:38]] [INFO] u928vFzSni=pass
[[18:28:34]] [SUCCESS] Screenshot refreshed successfully
[[18:28:34]] [SUCCESS] Screenshot refreshed successfully
[[18:28:34]] [INFO] u928vFzSni=running
[[18:28:34]] [INFO] Executing action 371/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:28:33]] [SUCCESS] Screenshot refreshed
[[18:28:33]] [INFO] Refreshing screenshot...
[[18:28:33]] [INFO] s0WyiD1w0B=pass
[[18:28:30]] [SUCCESS] Screenshot refreshed successfully
[[18:28:30]] [SUCCESS] Screenshot refreshed successfully
[[18:28:30]] [INFO] s0WyiD1w0B=running
[[18:28:30]] [INFO] Executing action 370/590: iOS Function: alert_accept
[[18:28:30]] [SUCCESS] Screenshot refreshed
[[18:28:30]] [INFO] Refreshing screenshot...
[[18:28:30]] [INFO] gekNSY5O2E=pass
[[18:28:25]] [SUCCESS] Screenshot refreshed successfully
[[18:28:25]] [SUCCESS] Screenshot refreshed successfully
[[18:28:25]] [INFO] gekNSY5O2E=running
[[18:28:25]] [INFO] Executing action 369/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[18:28:25]] [SUCCESS] Screenshot refreshed
[[18:28:25]] [INFO] Refreshing screenshot...
[[18:28:25]] [INFO] VJJ3EXXotU=pass
[[18:28:20]] [SUCCESS] Screenshot refreshed successfully
[[18:28:20]] [SUCCESS] Screenshot refreshed successfully
[[18:28:20]] [INFO] VJJ3EXXotU=running
[[18:28:20]] [INFO] Executing action 368/590: Tap on image: env[device-back-img]
[[18:28:19]] [SUCCESS] Screenshot refreshed
[[18:28:19]] [INFO] Refreshing screenshot...
[[18:28:19]] [INFO] 83tV9A4NOn=pass
[[18:28:16]] [SUCCESS] Screenshot refreshed successfully
[[18:28:16]] [SUCCESS] Screenshot refreshed successfully
[[18:28:16]] [INFO] 83tV9A4NOn=running
[[18:28:16]] [INFO] Executing action 367/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[18:28:16]] [SUCCESS] Screenshot refreshed
[[18:28:16]] [INFO] Refreshing screenshot...
[[18:28:16]] [INFO] aNN0yYFLEd=pass
[[18:28:12]] [SUCCESS] Screenshot refreshed successfully
[[18:28:12]] [SUCCESS] Screenshot refreshed successfully
[[18:28:12]] [INFO] aNN0yYFLEd=running
[[18:28:12]] [INFO] Executing action 366/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[18:28:11]] [SUCCESS] Screenshot refreshed
[[18:28:11]] [INFO] Refreshing screenshot...
[[18:28:11]] [INFO] XJv08Gkucs=pass
[[18:28:08]] [SUCCESS] Screenshot refreshed successfully
[[18:28:08]] [SUCCESS] Screenshot refreshed successfully
[[18:28:08]] [INFO] XJv08Gkucs=running
[[18:28:08]] [INFO] Executing action 365/590: Input text: "<EMAIL>"
[[18:28:07]] [SUCCESS] Screenshot refreshed
[[18:28:07]] [INFO] Refreshing screenshot...
[[18:28:07]] [INFO] kAQ1yIIw3h=pass
[[18:28:03]] [SUCCESS] Screenshot refreshed successfully
[[18:28:03]] [SUCCESS] Screenshot refreshed successfully
[[18:28:03]] [INFO] kAQ1yIIw3h=running
[[18:28:03]] [INFO] Executing action 364/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[18:28:03]] [SUCCESS] Screenshot refreshed
[[18:28:03]] [INFO] Refreshing screenshot...
[[18:28:03]] [INFO] 7YbjwQH1Jc=pass
[[18:27:59]] [SUCCESS] Screenshot refreshed successfully
[[18:27:59]] [SUCCESS] Screenshot refreshed successfully
[[18:27:59]] [INFO] 7YbjwQH1Jc=running
[[18:27:59]] [INFO] Executing action 363/590: Input text: "env[searchorder]"
[[18:27:59]] [SUCCESS] Screenshot refreshed
[[18:27:59]] [INFO] Refreshing screenshot...
[[18:27:59]] [INFO] OmKfD9iBjD=pass
[[18:27:54]] [SUCCESS] Screenshot refreshed successfully
[[18:27:54]] [SUCCESS] Screenshot refreshed successfully
[[18:27:54]] [INFO] OmKfD9iBjD=running
[[18:27:54]] [INFO] Executing action 362/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[18:27:54]] [SUCCESS] Screenshot refreshed
[[18:27:54]] [INFO] Refreshing screenshot...
[[18:27:54]] [INFO] eHLWiRoqqS=pass
[[18:27:49]] [SUCCESS] Screenshot refreshed successfully
[[18:27:49]] [SUCCESS] Screenshot refreshed successfully
[[18:27:49]] [INFO] eHLWiRoqqS=running
[[18:27:49]] [INFO] Executing action 361/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[18:27:48]] [SUCCESS] Screenshot refreshed
[[18:27:48]] [INFO] Refreshing screenshot...
[[18:27:48]] [INFO] F4NGh9HrLw=pass
[[18:27:45]] [SUCCESS] Screenshot refreshed successfully
[[18:27:45]] [SUCCESS] Screenshot refreshed successfully
[[18:27:44]] [INFO] F4NGh9HrLw=running
[[18:27:44]] [INFO] Executing action 360/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:27:44]] [SUCCESS] Screenshot refreshed
[[18:27:44]] [INFO] Refreshing screenshot...
[[18:27:44]] [INFO] 74XW7x54ad=pass
[[18:27:41]] [SUCCESS] Screenshot refreshed successfully
[[18:27:41]] [SUCCESS] Screenshot refreshed successfully
[[18:27:39]] [INFO] 74XW7x54ad=running
[[18:27:39]] [INFO] Executing action 359/590: Tap on image: env[device-back-img]
[[18:27:39]] [SUCCESS] Screenshot refreshed
[[18:27:39]] [INFO] Refreshing screenshot...
[[18:27:39]] [INFO] xUbWFa8Ok2=pass
[[18:27:36]] [SUCCESS] Screenshot refreshed successfully
[[18:27:36]] [SUCCESS] Screenshot refreshed successfully
[[18:27:35]] [INFO] xUbWFa8Ok2=running
[[18:27:35]] [INFO] Executing action 358/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[18:27:34]] [SUCCESS] Screenshot refreshed
[[18:27:34]] [INFO] Refreshing screenshot...
[[18:27:34]] [INFO] RbNtEW6N9T=pass
[[18:27:31]] [SUCCESS] Screenshot refreshed successfully
[[18:27:31]] [SUCCESS] Screenshot refreshed successfully
[[18:27:30]] [INFO] RbNtEW6N9T=running
[[18:27:30]] [INFO] Executing action 357/590: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[18:27:30]] [SUCCESS] Screenshot refreshed
[[18:27:30]] [INFO] Refreshing screenshot...
[[18:27:30]] [INFO] F4NGh9HrLw=pass
[[18:27:27]] [SUCCESS] Screenshot refreshed successfully
[[18:27:27]] [SUCCESS] Screenshot refreshed successfully
[[18:27:25]] [INFO] F4NGh9HrLw=running
[[18:27:25]] [INFO] Executing action 356/590: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[18:27:25]] [SUCCESS] Screenshot refreshed
[[18:27:25]] [INFO] Refreshing screenshot...
[[18:27:25]] [INFO] RlDZFks4Lc=pass
[[18:27:23]] [SUCCESS] Screenshot refreshed successfully
[[18:27:23]] [SUCCESS] Screenshot refreshed successfully
[[18:27:22]] [INFO] RlDZFks4Lc=running
[[18:27:22]] [INFO] Executing action 355/590: iOS Function: alert_accept
[[18:27:21]] [SUCCESS] Screenshot refreshed
[[18:27:21]] [INFO] Refreshing screenshot...
[[18:27:21]] [INFO] Dzn2Q7JTe0=pass
[[18:27:16]] [SUCCESS] Screenshot refreshed successfully
[[18:27:16]] [SUCCESS] Screenshot refreshed successfully
[[18:27:16]] [INFO] Dzn2Q7JTe0=running
[[18:27:16]] [INFO] Executing action 354/590: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[18:27:15]] [SUCCESS] Screenshot refreshed
[[18:27:15]] [INFO] Refreshing screenshot...
[[18:27:15]] [INFO] H9fy9qcFbZ=pass
[[18:27:02]] [SUCCESS] Screenshot refreshed successfully
[[18:27:02]] [SUCCESS] Screenshot refreshed successfully
[[18:27:01]] [INFO] H9fy9qcFbZ=running
[[18:27:01]] [INFO] Executing action 353/590: Restart app: env[appid]
[[18:27:01]] [SUCCESS] Screenshot refreshed
[[18:27:01]] [INFO] Refreshing screenshot...
[[18:27:01]] [SUCCESS] Screenshot refreshed
[[18:27:01]] [INFO] Refreshing screenshot...
[[18:26:58]] [SUCCESS] Screenshot refreshed successfully
[[18:26:58]] [SUCCESS] Screenshot refreshed successfully
[[18:26:57]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:26:57]] [SUCCESS] Screenshot refreshed
[[18:26:57]] [INFO] Refreshing screenshot...
[[18:26:51]] [SUCCESS] Screenshot refreshed successfully
[[18:26:51]] [SUCCESS] Screenshot refreshed successfully
[[18:26:51]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:26:50]] [SUCCESS] Screenshot refreshed
[[18:26:50]] [INFO] Refreshing screenshot...
[[18:26:46]] [SUCCESS] Screenshot refreshed successfully
[[18:26:46]] [SUCCESS] Screenshot refreshed successfully
[[18:26:46]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:26:46]] [SUCCESS] Screenshot refreshed
[[18:26:46]] [INFO] Refreshing screenshot...
[[18:26:42]] [SUCCESS] Screenshot refreshed successfully
[[18:26:42]] [SUCCESS] Screenshot refreshed successfully
[[18:26:42]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:26:41]] [SUCCESS] Screenshot refreshed
[[18:26:41]] [INFO] Refreshing screenshot...
[[18:26:35]] [SUCCESS] Screenshot refreshed successfully
[[18:26:35]] [SUCCESS] Screenshot refreshed successfully
[[18:26:34]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:26:34]] [SUCCESS] Screenshot refreshed
[[18:26:34]] [INFO] Refreshing screenshot...
[[18:26:26]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:26:26]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:26:26]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:26:26]] [INFO] AeQaElnzUN=running
[[18:26:26]] [INFO] Executing action 352/590: cleanupSteps action
[[18:26:26]] [INFO] Skipping remaining steps in failed test case (moving from action 307 to 351), but preserving cleanup steps
[[18:26:26]] [INFO] 7g6MFJSGIO=fail
[[18:26:26]] [ERROR] Action 307 failed: Element not found or not tappable: xpath='(//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]'
[[18:26:11]] [SUCCESS] Screenshot refreshed successfully
[[18:26:11]] [SUCCESS] Screenshot refreshed successfully
[[18:26:11]] [INFO] 7g6MFJSGIO=running
[[18:26:11]] [INFO] Executing action 307/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:26:10]] [SUCCESS] Screenshot refreshed
[[18:26:10]] [INFO] Refreshing screenshot...
[[18:26:10]] [INFO] zNwyPagPE1=pass
[[18:26:04]] [SUCCESS] Screenshot refreshed successfully
[[18:26:04]] [SUCCESS] Screenshot refreshed successfully
[[18:26:04]] [INFO] zNwyPagPE1=running
[[18:26:04]] [INFO] Executing action 306/590: Wait for 5 ms
[[18:26:03]] [SUCCESS] Screenshot refreshed
[[18:26:03]] [INFO] Refreshing screenshot...
[[18:26:03]] [INFO] qXsL3wzg6J=pass
[[18:25:59]] [SUCCESS] Screenshot refreshed successfully
[[18:25:59]] [SUCCESS] Screenshot refreshed successfully
[[18:25:59]] [INFO] qXsL3wzg6J=running
[[18:25:59]] [INFO] Executing action 305/590: Tap on image: env[device-back-img]
[[18:25:59]] [SUCCESS] Screenshot refreshed
[[18:25:59]] [INFO] Refreshing screenshot...
[[18:25:59]] [INFO] YuuQe2KupX=pass
[[18:25:53]] [INFO] YuuQe2KupX=running
[[18:25:53]] [INFO] Executing action 304/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[18:25:53]] [SUCCESS] Screenshot refreshed successfully
[[18:25:53]] [SUCCESS] Screenshot refreshed successfully
[[18:25:53]] [SUCCESS] Screenshot refreshed
[[18:25:53]] [INFO] Refreshing screenshot...
[[18:25:53]] [INFO] g0PE7Mofye=pass
[[18:25:47]] [SUCCESS] Screenshot refreshed successfully
[[18:25:47]] [SUCCESS] Screenshot refreshed successfully
[[18:25:47]] [INFO] g0PE7Mofye=running
[[18:25:47]] [INFO] Executing action 303/590: Tap on element with accessibility_id: Print order details
[[18:25:46]] [SUCCESS] Screenshot refreshed
[[18:25:46]] [INFO] Refreshing screenshot...
[[18:25:46]] [INFO] GgQaBLWYkb=pass
[[18:25:42]] [SUCCESS] Screenshot refreshed successfully
[[18:25:42]] [SUCCESS] Screenshot refreshed successfully
[[18:25:42]] [INFO] GgQaBLWYkb=running
[[18:25:42]] [INFO] Executing action 302/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[18:25:42]] [SUCCESS] Screenshot refreshed
[[18:25:42]] [INFO] Refreshing screenshot...
[[18:25:42]] [INFO] f3OrHHzTFN=pass
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:25]] [INFO] f3OrHHzTFN=running
[[18:25:25]] [INFO] Executing action 301/590: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[18:25:25]] [SUCCESS] Screenshot refreshed
[[18:25:25]] [INFO] Refreshing screenshot...
[[18:25:25]] [INFO] 7g6MFJSGIO=pass
[[18:25:20]] [SUCCESS] Screenshot refreshed successfully
[[18:25:20]] [SUCCESS] Screenshot refreshed successfully
[[18:25:20]] [INFO] 7g6MFJSGIO=running
[[18:25:20]] [INFO] Executing action 300/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:25:19]] [SUCCESS] Screenshot refreshed
[[18:25:19]] [INFO] Refreshing screenshot...
[[18:25:19]] [INFO] Z6g3sGuHTp=pass
[[18:25:13]] [INFO] Z6g3sGuHTp=running
[[18:25:13]] [INFO] Executing action 299/590: Wait for 5 ms
[[18:25:13]] [SUCCESS] Screenshot refreshed successfully
[[18:25:13]] [SUCCESS] Screenshot refreshed successfully
[[18:25:12]] [SUCCESS] Screenshot refreshed
[[18:25:12]] [INFO] Refreshing screenshot...
[[18:25:12]] [INFO] pFlYwTS53v=pass
[[18:25:08]] [SUCCESS] Screenshot refreshed successfully
[[18:25:08]] [SUCCESS] Screenshot refreshed successfully
[[18:25:08]] [INFO] pFlYwTS53v=running
[[18:25:08]] [INFO] Executing action 298/590: Tap on Text: "receipts"
[[18:25:07]] [SUCCESS] Screenshot refreshed
[[18:25:07]] [INFO] Refreshing screenshot...
[[18:25:07]] [INFO] V59u3l1wkM=pass
[[18:25:04]] [SUCCESS] Screenshot refreshed successfully
[[18:25:04]] [SUCCESS] Screenshot refreshed successfully
[[18:25:04]] [INFO] V59u3l1wkM=running
[[18:25:04]] [INFO] Executing action 297/590: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[18:25:03]] [SUCCESS] Screenshot refreshed
[[18:25:03]] [INFO] Refreshing screenshot...
[[18:25:03]] [INFO] sl3Wk1gK8X=pass
[[18:24:59]] [SUCCESS] Screenshot refreshed successfully
[[18:24:59]] [SUCCESS] Screenshot refreshed successfully
[[18:24:58]] [INFO] sl3Wk1gK8X=running
[[18:24:58]] [INFO] Executing action 296/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:24:58]] [SUCCESS] Screenshot refreshed
[[18:24:58]] [INFO] Refreshing screenshot...
[[18:24:57]] [SUCCESS] Screenshot refreshed
[[18:24:57]] [INFO] Refreshing screenshot...
[[18:24:53]] [SUCCESS] Screenshot refreshed successfully
[[18:24:53]] [SUCCESS] Screenshot refreshed successfully
[[18:24:53]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:24:52]] [SUCCESS] Screenshot refreshed
[[18:24:52]] [INFO] Refreshing screenshot...
[[18:24:48]] [SUCCESS] Screenshot refreshed successfully
[[18:24:48]] [SUCCESS] Screenshot refreshed successfully
[[18:24:48]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:24:47]] [SUCCESS] Screenshot refreshed
[[18:24:47]] [INFO] Refreshing screenshot...
[[18:24:42]] [SUCCESS] Screenshot refreshed successfully
[[18:24:42]] [SUCCESS] Screenshot refreshed successfully
[[18:24:42]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:24:42]] [SUCCESS] Screenshot refreshed
[[18:24:42]] [INFO] Refreshing screenshot...
[[18:24:38]] [SUCCESS] Screenshot refreshed successfully
[[18:24:38]] [SUCCESS] Screenshot refreshed successfully
[[18:24:38]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:24:37]] [SUCCESS] Screenshot refreshed
[[18:24:37]] [INFO] Refreshing screenshot...
[[18:24:31]] [SUCCESS] Screenshot refreshed successfully
[[18:24:31]] [SUCCESS] Screenshot refreshed successfully
[[18:24:31]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:24:31]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:24:31]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:24:31]] [INFO] vjK6GqOF3r=running
[[18:24:31]] [INFO] Executing action 295/590: Execute Test Case: Kmart-Signin (8 steps)
[[18:24:31]] [SUCCESS] Screenshot refreshed
[[18:24:31]] [INFO] Refreshing screenshot...
[[18:24:31]] [INFO] ly2oT3zqmf=pass
[[18:24:28]] [SUCCESS] Screenshot refreshed successfully
[[18:24:28]] [SUCCESS] Screenshot refreshed successfully
[[18:24:28]] [INFO] ly2oT3zqmf=running
[[18:24:28]] [INFO] Executing action 294/590: iOS Function: alert_accept
[[18:24:27]] [SUCCESS] Screenshot refreshed
[[18:24:27]] [INFO] Refreshing screenshot...
[[18:24:27]] [INFO] xAPeBnVHrT=pass
[[18:24:19]] [SUCCESS] Screenshot refreshed successfully
[[18:24:19]] [SUCCESS] Screenshot refreshed successfully
[[18:24:19]] [INFO] xAPeBnVHrT=running
[[18:24:19]] [INFO] Executing action 293/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:24:18]] [SUCCESS] Screenshot refreshed
[[18:24:18]] [INFO] Refreshing screenshot...
[[18:24:18]] [INFO] u6bRYZZFAv=pass
[[18:24:12]] [SUCCESS] Screenshot refreshed successfully
[[18:24:12]] [SUCCESS] Screenshot refreshed successfully
[[18:24:11]] [INFO] u6bRYZZFAv=running
[[18:24:11]] [INFO] Executing action 292/590: Wait for 5 ms
[[18:24:11]] [SUCCESS] Screenshot refreshed
[[18:24:11]] [INFO] Refreshing screenshot...
[[18:24:11]] [INFO] pjFNt3w5Fr=pass
[[18:23:58]] [SUCCESS] Screenshot refreshed successfully
[[18:23:58]] [SUCCESS] Screenshot refreshed successfully
[[18:23:56]] [INFO] pjFNt3w5Fr=running
[[18:23:56]] [INFO] Executing action 291/590: Restart app: env[appid]
[[18:23:56]] [SUCCESS] Screenshot refreshed
[[18:23:56]] [INFO] Refreshing screenshot...
[[18:23:56]] [SUCCESS] Screenshot refreshed
[[18:23:56]] [INFO] Refreshing screenshot...
[[18:23:53]] [SUCCESS] Screenshot refreshed successfully
[[18:23:53]] [SUCCESS] Screenshot refreshed successfully
[[18:23:53]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:23:52]] [SUCCESS] Screenshot refreshed
[[18:23:52]] [INFO] Refreshing screenshot...
[[18:23:39]] [SUCCESS] Screenshot refreshed successfully
[[18:23:39]] [SUCCESS] Screenshot refreshed successfully
[[18:23:39]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:23:39]] [SUCCESS] Screenshot refreshed
[[18:23:39]] [INFO] Refreshing screenshot...
[[18:23:35]] [SUCCESS] Screenshot refreshed successfully
[[18:23:35]] [SUCCESS] Screenshot refreshed successfully
[[18:23:35]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:23:34]] [SUCCESS] Screenshot refreshed
[[18:23:34]] [INFO] Refreshing screenshot...
[[18:23:31]] [SUCCESS] Screenshot refreshed successfully
[[18:23:31]] [SUCCESS] Screenshot refreshed successfully
[[18:23:30]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:23:30]] [SUCCESS] Screenshot refreshed
[[18:23:30]] [INFO] Refreshing screenshot...
[[18:23:23]] [SUCCESS] Screenshot refreshed successfully
[[18:23:23]] [SUCCESS] Screenshot refreshed successfully
[[18:23:23]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:23:22]] [SUCCESS] Screenshot refreshed
[[18:23:22]] [INFO] Refreshing screenshot...
[[18:23:15]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:23:15]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:23:15]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:23:15]] [INFO] PGvsG6rpU4=running
[[18:23:15]] [INFO] Executing action 290/590: cleanupSteps action
[[18:23:15]] [INFO] Skipping remaining steps in failed test case (moving from action 256 to 289), but preserving cleanup steps
[[18:23:15]] [INFO] M6HdLxu76S=fail
[[18:23:15]] [ERROR] Action 256 failed: Element not found: xpath='//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]'
[[18:22:58]] [SUCCESS] Screenshot refreshed successfully
[[18:22:58]] [SUCCESS] Screenshot refreshed successfully
[[18:22:58]] [INFO] M6HdLxu76S=running
[[18:22:58]] [INFO] Executing action 256/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:22:57]] [SUCCESS] Screenshot refreshed
[[18:22:57]] [INFO] Refreshing screenshot...
[[18:22:57]] [INFO] pCPTAtSZbf=pass
[[18:22:52]] [SUCCESS] Screenshot refreshed successfully
[[18:22:52]] [SUCCESS] Screenshot refreshed successfully
[[18:22:52]] [INFO] pCPTAtSZbf=running
[[18:22:52]] [INFO] Executing action 255/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:22:52]] [SUCCESS] Screenshot refreshed
[[18:22:52]] [INFO] Refreshing screenshot...
[[18:22:52]] [INFO] DaVBARRwft=pass
[[18:22:47]] [SUCCESS] Screenshot refreshed successfully
[[18:22:47]] [SUCCESS] Screenshot refreshed successfully
[[18:22:47]] [INFO] DaVBARRwft=running
[[18:22:47]] [INFO] Executing action 254/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:22:47]] [SUCCESS] Screenshot refreshed
[[18:22:47]] [INFO] Refreshing screenshot...
[[18:22:47]] [INFO] e1RoZWCZJb=pass
[[18:22:42]] [SUCCESS] Screenshot refreshed successfully
[[18:22:42]] [SUCCESS] Screenshot refreshed successfully
[[18:22:42]] [INFO] e1RoZWCZJb=running
[[18:22:42]] [INFO] Executing action 253/590: iOS Function: text - Text: "<EMAIL>"
[[18:22:41]] [SUCCESS] Screenshot refreshed
[[18:22:41]] [INFO] Refreshing screenshot...
[[18:22:41]] [INFO] y8ZMTkG38M=pass
[[18:22:37]] [INFO] y8ZMTkG38M=running
[[18:22:37]] [INFO] Executing action 252/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:22:37]] [SUCCESS] Screenshot refreshed successfully
[[18:22:37]] [SUCCESS] Screenshot refreshed successfully
[[18:22:36]] [SUCCESS] Screenshot refreshed
[[18:22:36]] [INFO] Refreshing screenshot...
[[18:22:36]] [INFO] UUhQjmzfO2=pass
[[18:22:31]] [SUCCESS] Screenshot refreshed successfully
[[18:22:31]] [SUCCESS] Screenshot refreshed successfully
[[18:22:31]] [INFO] UUhQjmzfO2=running
[[18:22:31]] [INFO] Executing action 251/590: Tap on Text: "OnePass"
[[18:22:31]] [SUCCESS] Screenshot refreshed
[[18:22:31]] [INFO] Refreshing screenshot...
[[18:22:31]] [INFO] FciJcOsMsB=pass
[[18:22:25]] [SUCCESS] Screenshot refreshed successfully
[[18:22:25]] [SUCCESS] Screenshot refreshed successfully
[[18:22:25]] [INFO] FciJcOsMsB=running
[[18:22:25]] [INFO] Executing action 250/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:22:25]] [SUCCESS] Screenshot refreshed
[[18:22:25]] [INFO] Refreshing screenshot...
[[18:22:25]] [INFO] NCyuT8W5Xz=pass
[[18:22:20]] [SUCCESS] Screenshot refreshed successfully
[[18:22:20]] [SUCCESS] Screenshot refreshed successfully
[[18:22:20]] [INFO] NCyuT8W5Xz=running
[[18:22:20]] [INFO] Executing action 249/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:22:19]] [SUCCESS] Screenshot refreshed
[[18:22:19]] [INFO] Refreshing screenshot...
[[18:22:19]] [INFO] 2kwu2VBmuZ=pass
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [INFO] 2kwu2VBmuZ=running
[[18:22:17]] [INFO] Executing action 248/590: iOS Function: alert_accept
[[18:22:16]] [SUCCESS] Screenshot refreshed
[[18:22:16]] [INFO] Refreshing screenshot...
[[18:22:16]] [INFO] cJDpd7aK3d=pass
[[18:22:10]] [SUCCESS] Screenshot refreshed successfully
[[18:22:10]] [SUCCESS] Screenshot refreshed successfully
[[18:22:10]] [INFO] cJDpd7aK3d=running
[[18:22:10]] [INFO] Executing action 247/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:22:09]] [SUCCESS] Screenshot refreshed
[[18:22:09]] [INFO] Refreshing screenshot...
[[18:22:09]] [INFO] FlEukNkjlS=pass
[[18:22:05]] [SUCCESS] Screenshot refreshed successfully
[[18:22:05]] [SUCCESS] Screenshot refreshed successfully
[[18:22:05]] [INFO] FlEukNkjlS=running
[[18:22:05]] [INFO] Executing action 246/590: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:22:04]] [SUCCESS] Screenshot refreshed
[[18:22:04]] [INFO] Refreshing screenshot...
[[18:22:04]] [INFO] LlRfimKPrn=pass
[[18:22:00]] [SUCCESS] Screenshot refreshed successfully
[[18:22:00]] [SUCCESS] Screenshot refreshed successfully
[[18:22:00]] [INFO] LlRfimKPrn=running
[[18:22:00]] [INFO] Executing action 245/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:21:59]] [SUCCESS] Screenshot refreshed
[[18:21:59]] [INFO] Refreshing screenshot...
[[18:21:59]] [INFO] FciJcOsMsB=pass
[[18:21:52]] [SUCCESS] Screenshot refreshed successfully
[[18:21:52]] [SUCCESS] Screenshot refreshed successfully
[[18:21:52]] [INFO] FciJcOsMsB=running
[[18:21:52]] [INFO] Executing action 244/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:21:51]] [SUCCESS] Screenshot refreshed
[[18:21:51]] [INFO] Refreshing screenshot...
[[18:21:51]] [INFO] 08NzsvhQXK=pass
[[18:21:47]] [SUCCESS] Screenshot refreshed successfully
[[18:21:47]] [SUCCESS] Screenshot refreshed successfully
[[18:21:47]] [INFO] 08NzsvhQXK=running
[[18:21:47]] [INFO] Executing action 243/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:21:46]] [SUCCESS] Screenshot refreshed
[[18:21:46]] [INFO] Refreshing screenshot...
[[18:21:46]] [INFO] IsGWxLFpIn=pass
[[18:21:43]] [SUCCESS] Screenshot refreshed successfully
[[18:21:43]] [SUCCESS] Screenshot refreshed successfully
[[18:21:43]] [INFO] IsGWxLFpIn=running
[[18:21:43]] [INFO] Executing action 242/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:21:42]] [SUCCESS] Screenshot refreshed
[[18:21:42]] [INFO] Refreshing screenshot...
[[18:21:42]] [INFO] dyECdbRifp=pass
[[18:21:37]] [SUCCESS] Screenshot refreshed successfully
[[18:21:37]] [SUCCESS] Screenshot refreshed successfully
[[18:21:37]] [INFO] dyECdbRifp=running
[[18:21:37]] [INFO] Executing action 241/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:21:37]] [SUCCESS] Screenshot refreshed
[[18:21:37]] [INFO] Refreshing screenshot...
[[18:21:37]] [INFO] I5bRbYY1hD=pass
[[18:21:32]] [SUCCESS] Screenshot refreshed successfully
[[18:21:32]] [SUCCESS] Screenshot refreshed successfully
[[18:21:32]] [INFO] I5bRbYY1hD=running
[[18:21:32]] [INFO] Executing action 240/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:21:32]] [SUCCESS] Screenshot refreshed
[[18:21:32]] [INFO] Refreshing screenshot...
[[18:21:32]] [INFO] WMl5g82CCq=pass
[[18:21:27]] [SUCCESS] Screenshot refreshed successfully
[[18:21:27]] [SUCCESS] Screenshot refreshed successfully
[[18:21:27]] [INFO] WMl5g82CCq=running
[[18:21:27]] [INFO] Executing action 239/590: iOS Function: text - Text: "<EMAIL>"
[[18:21:26]] [SUCCESS] Screenshot refreshed
[[18:21:26]] [INFO] Refreshing screenshot...
[[18:21:26]] [INFO] 8OsQmoVYqW=pass
[[18:21:22]] [SUCCESS] Screenshot refreshed successfully
[[18:21:22]] [SUCCESS] Screenshot refreshed successfully
[[18:21:22]] [INFO] 8OsQmoVYqW=running
[[18:21:22]] [INFO] Executing action 238/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:21:22]] [SUCCESS] Screenshot refreshed
[[18:21:22]] [INFO] Refreshing screenshot...
[[18:21:22]] [INFO] ImienLpJEN=pass
[[18:21:18]] [SUCCESS] Screenshot refreshed successfully
[[18:21:18]] [SUCCESS] Screenshot refreshed successfully
[[18:21:18]] [INFO] ImienLpJEN=running
[[18:21:18]] [INFO] Executing action 237/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:21:17]] [SUCCESS] Screenshot refreshed
[[18:21:17]] [INFO] Refreshing screenshot...
[[18:21:17]] [INFO] q4hPXCBtx4=pass
[[18:21:15]] [SUCCESS] Screenshot refreshed successfully
[[18:21:15]] [SUCCESS] Screenshot refreshed successfully
[[18:21:14]] [INFO] q4hPXCBtx4=running
[[18:21:14]] [INFO] Executing action 236/590: iOS Function: alert_accept
[[18:21:14]] [SUCCESS] Screenshot refreshed
[[18:21:14]] [INFO] Refreshing screenshot...
[[18:21:14]] [INFO] 2cTZvK1psn=pass
[[18:21:07]] [SUCCESS] Screenshot refreshed successfully
[[18:21:07]] [SUCCESS] Screenshot refreshed successfully
[[18:21:06]] [INFO] 2cTZvK1psn=running
[[18:21:06]] [INFO] Executing action 235/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:21:06]] [SUCCESS] Screenshot refreshed
[[18:21:06]] [INFO] Refreshing screenshot...
[[18:21:06]] [INFO] Vxt7QOYeDD=pass
[[18:20:53]] [SUCCESS] Screenshot refreshed successfully
[[18:20:53]] [SUCCESS] Screenshot refreshed successfully
[[18:20:52]] [INFO] Vxt7QOYeDD=running
[[18:20:52]] [INFO] Executing action 234/590: Restart app: env[appid]
[[18:20:52]] [SUCCESS] Screenshot refreshed
[[18:20:52]] [INFO] Refreshing screenshot...
[[18:20:51]] [SUCCESS] Screenshot refreshed
[[18:20:51]] [INFO] Refreshing screenshot...
[[18:20:48]] [SUCCESS] Screenshot refreshed successfully
[[18:20:48]] [SUCCESS] Screenshot refreshed successfully
[[18:20:48]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:20:47]] [SUCCESS] Screenshot refreshed
[[18:20:47]] [INFO] Refreshing screenshot...
[[18:20:35]] [SUCCESS] Screenshot refreshed successfully
[[18:20:35]] [SUCCESS] Screenshot refreshed successfully
[[18:20:34]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:20:34]] [SUCCESS] Screenshot refreshed
[[18:20:34]] [INFO] Refreshing screenshot...
[[18:20:30]] [SUCCESS] Screenshot refreshed successfully
[[18:20:30]] [SUCCESS] Screenshot refreshed successfully
[[18:20:30]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:20:30]] [SUCCESS] Screenshot refreshed
[[18:20:30]] [INFO] Refreshing screenshot...
[[18:20:25]] [SUCCESS] Screenshot refreshed successfully
[[18:20:25]] [SUCCESS] Screenshot refreshed successfully
[[18:20:25]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:20:24]] [SUCCESS] Screenshot refreshed
[[18:20:24]] [INFO] Refreshing screenshot...
[[18:20:18]] [SUCCESS] Screenshot refreshed successfully
[[18:20:18]] [SUCCESS] Screenshot refreshed successfully
[[18:20:17]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:20:17]] [SUCCESS] Screenshot refreshed
[[18:20:17]] [INFO] Refreshing screenshot...
[[18:20:10]] [SUCCESS] Screenshot refreshed successfully
[[18:20:10]] [SUCCESS] Screenshot refreshed successfully
[[18:20:09]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:20:09]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:20:09]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:20:09]] [INFO] DYWpUY7xB6=running
[[18:20:09]] [INFO] Executing action 233/590: cleanupSteps action
[[18:20:09]] [SUCCESS] Screenshot refreshed
[[18:20:09]] [INFO] Refreshing screenshot...
[[18:20:09]] [INFO] OyUowAaBzD=pass
[[18:20:04]] [SUCCESS] Screenshot refreshed successfully
[[18:20:04]] [SUCCESS] Screenshot refreshed successfully
[[18:20:04]] [INFO] OyUowAaBzD=running
[[18:20:04]] [INFO] Executing action 232/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:20:04]] [SUCCESS] Screenshot refreshed
[[18:20:04]] [INFO] Refreshing screenshot...
[[18:20:04]] [INFO] Ob26qqcA0p=pass
[[18:19:57]] [SUCCESS] Screenshot refreshed successfully
[[18:19:57]] [SUCCESS] Screenshot refreshed successfully
[[18:19:57]] [INFO] Ob26qqcA0p=running
[[18:19:57]] [INFO] Executing action 231/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:19:56]] [SUCCESS] Screenshot refreshed
[[18:19:56]] [INFO] Refreshing screenshot...
[[18:19:56]] [INFO] k3mu9Mt7Ec=pass
[[18:19:52]] [SUCCESS] Screenshot refreshed successfully
[[18:19:52]] [SUCCESS] Screenshot refreshed successfully
[[18:19:52]] [INFO] k3mu9Mt7Ec=running
[[18:19:52]] [INFO] Executing action 230/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:19:51]] [SUCCESS] Screenshot refreshed
[[18:19:51]] [INFO] Refreshing screenshot...
[[18:19:51]] [INFO] yhmzeynQyu=pass
[[18:19:47]] [SUCCESS] Screenshot refreshed successfully
[[18:19:47]] [SUCCESS] Screenshot refreshed successfully
[[18:19:47]] [INFO] yhmzeynQyu=running
[[18:19:47]] [INFO] Executing action 229/590: Tap on Text: "Remove"
[[18:19:47]] [SUCCESS] Screenshot refreshed
[[18:19:47]] [INFO] Refreshing screenshot...
[[18:19:47]] [INFO] zCHZwhvc44=pass
[[18:19:41]] [SUCCESS] Screenshot refreshed successfully
[[18:19:41]] [SUCCESS] Screenshot refreshed successfully
[[18:19:41]] [INFO] zCHZwhvc44=running
[[18:19:41]] [INFO] Executing action 228/590: ifThenSteps action
[[18:19:40]] [SUCCESS] Screenshot refreshed
[[18:19:40]] [INFO] Refreshing screenshot...
[[18:19:40]] [INFO] yhmzeynQyu=pass
[[18:19:36]] [SUCCESS] Screenshot refreshed successfully
[[18:19:36]] [SUCCESS] Screenshot refreshed successfully
[[18:19:36]] [INFO] yhmzeynQyu=running
[[18:19:36]] [INFO] Executing action 227/590: Tap on Text: "Remove"
[[18:19:35]] [SUCCESS] Screenshot refreshed
[[18:19:35]] [INFO] Refreshing screenshot...
[[18:19:35]] [INFO] zCHZwhvc44=pass
[[18:19:29]] [SUCCESS] Screenshot refreshed successfully
[[18:19:29]] [SUCCESS] Screenshot refreshed successfully
[[18:19:29]] [INFO] zCHZwhvc44=running
[[18:19:29]] [INFO] Executing action 226/590: ifThenSteps action
[[18:19:29]] [SUCCESS] Screenshot refreshed
[[18:19:29]] [INFO] Refreshing screenshot...
[[18:19:29]] [INFO] F1olhgKhUt=pass
[[18:19:23]] [SUCCESS] Screenshot refreshed successfully
[[18:19:23]] [SUCCESS] Screenshot refreshed successfully
[[18:19:23]] [INFO] F1olhgKhUt=running
[[18:19:23]] [INFO] Executing action 225/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:19:22]] [SUCCESS] Screenshot refreshed
[[18:19:22]] [INFO] Refreshing screenshot...
[[18:19:22]] [INFO] 8umPSX0vrr=pass
[[18:19:17]] [INFO] 8umPSX0vrr=running
[[18:19:17]] [INFO] Executing action 224/590: Tap on image: banner-close-updated.png
[[18:19:17]] [SUCCESS] Screenshot refreshed successfully
[[18:19:17]] [SUCCESS] Screenshot refreshed successfully
[[18:19:16]] [SUCCESS] Screenshot refreshed
[[18:19:16]] [INFO] Refreshing screenshot...
[[18:19:16]] [INFO] pr9o8Zsm5p=pass
[[18:19:12]] [SUCCESS] Screenshot refreshed successfully
[[18:19:12]] [SUCCESS] Screenshot refreshed successfully
[[18:19:12]] [INFO] pr9o8Zsm5p=running
[[18:19:12]] [INFO] Executing action 223/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[18:19:12]] [SUCCESS] Screenshot refreshed
[[18:19:12]] [INFO] Refreshing screenshot...
[[18:19:12]] [INFO] Qbg9bipTGs=pass
[[18:19:08]] [SUCCESS] Screenshot refreshed successfully
[[18:19:08]] [SUCCESS] Screenshot refreshed successfully
[[18:19:08]] [INFO] Qbg9bipTGs=running
[[18:19:08]] [INFO] Executing action 222/590: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[18:19:08]] [SUCCESS] Screenshot refreshed
[[18:19:08]] [INFO] Refreshing screenshot...
[[18:19:08]] [INFO] Ob26qqcA0p=pass
[[18:19:03]] [SUCCESS] Screenshot refreshed successfully
[[18:19:03]] [SUCCESS] Screenshot refreshed successfully
[[18:19:03]] [INFO] Ob26qqcA0p=running
[[18:19:03]] [INFO] Executing action 221/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:19:03]] [SUCCESS] Screenshot refreshed
[[18:19:03]] [INFO] Refreshing screenshot...
[[18:19:03]] [INFO] ByviEQxEgr=pass
[[18:18:50]] [INFO] ByviEQxEgr=running
[[18:18:50]] [INFO] Executing action 220/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:18:50]] [SUCCESS] Screenshot refreshed successfully
[[18:18:50]] [SUCCESS] Screenshot refreshed successfully
[[18:18:49]] [SUCCESS] Screenshot refreshed
[[18:18:49]] [INFO] Refreshing screenshot...
[[18:18:49]] [INFO] lWIRxRm6HE=pass
[[18:18:45]] [SUCCESS] Screenshot refreshed successfully
[[18:18:45]] [SUCCESS] Screenshot refreshed successfully
[[18:18:45]] [INFO] lWIRxRm6HE=running
[[18:18:45]] [INFO] Executing action 219/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:18:44]] [SUCCESS] Screenshot refreshed
[[18:18:44]] [INFO] Refreshing screenshot...
[[18:18:44]] [INFO] uOt2cFGhGr=pass
[[18:18:40]] [SUCCESS] Screenshot refreshed successfully
[[18:18:40]] [SUCCESS] Screenshot refreshed successfully
[[18:18:39]] [INFO] uOt2cFGhGr=running
[[18:18:39]] [INFO] Executing action 218/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:18:39]] [SUCCESS] Screenshot refreshed
[[18:18:39]] [INFO] Refreshing screenshot...
[[18:18:39]] [INFO] Q0fomJIDoQ=pass
[[18:18:35]] [SUCCESS] Screenshot refreshed successfully
[[18:18:35]] [SUCCESS] Screenshot refreshed successfully
[[18:18:35]] [INFO] Q0fomJIDoQ=running
[[18:18:35]] [INFO] Executing action 217/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[18:18:34]] [SUCCESS] Screenshot refreshed
[[18:18:34]] [INFO] Refreshing screenshot...
[[18:18:34]] [INFO] yhmzeynQyu=pass
[[18:18:30]] [SUCCESS] Screenshot refreshed successfully
[[18:18:30]] [SUCCESS] Screenshot refreshed successfully
[[18:18:30]] [INFO] yhmzeynQyu=running
[[18:18:30]] [INFO] Executing action 216/590: Tap on Text: "Remove"
[[18:18:29]] [SUCCESS] Screenshot refreshed
[[18:18:29]] [INFO] Refreshing screenshot...
[[18:18:29]] [INFO] Q0fomJIDoQ=pass
[[18:18:25]] [SUCCESS] Screenshot refreshed successfully
[[18:18:25]] [SUCCESS] Screenshot refreshed successfully
[[18:18:24]] [INFO] Q0fomJIDoQ=running
[[18:18:24]] [INFO] Executing action 215/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[18:18:24]] [SUCCESS] Screenshot refreshed
[[18:18:24]] [INFO] Refreshing screenshot...
[[18:18:24]] [INFO] y4i304JeJj=pass
[[18:18:19]] [SUCCESS] Screenshot refreshed successfully
[[18:18:19]] [SUCCESS] Screenshot refreshed successfully
[[18:18:19]] [INFO] y4i304JeJj=running
[[18:18:19]] [INFO] Executing action 214/590: Tap on Text: "Move"
[[18:18:19]] [SUCCESS] Screenshot refreshed
[[18:18:19]] [INFO] Refreshing screenshot...
[[18:18:19]] [INFO] Q0fomJIDoQ=pass
[[18:18:15]] [SUCCESS] Screenshot refreshed successfully
[[18:18:15]] [SUCCESS] Screenshot refreshed successfully
[[18:18:15]] [INFO] Q0fomJIDoQ=running
[[18:18:15]] [INFO] Executing action 213/590: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:18:14]] [SUCCESS] Screenshot refreshed
[[18:18:14]] [INFO] Refreshing screenshot...
[[18:18:14]] [INFO] Q0fomJIDoQ=pass
[[18:18:11]] [SUCCESS] Screenshot refreshed successfully
[[18:18:11]] [SUCCESS] Screenshot refreshed successfully
[[18:18:11]] [INFO] Q0fomJIDoQ=running
[[18:18:11]] [INFO] Executing action 212/590: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:18:10]] [SUCCESS] Screenshot refreshed
[[18:18:10]] [INFO] Refreshing screenshot...
[[18:18:10]] [INFO] F1olhgKhUt=pass
[[18:18:06]] [SUCCESS] Screenshot refreshed successfully
[[18:18:06]] [SUCCESS] Screenshot refreshed successfully
[[18:18:05]] [INFO] F1olhgKhUt=running
[[18:18:05]] [INFO] Executing action 211/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:18:05]] [SUCCESS] Screenshot refreshed
[[18:18:05]] [INFO] Refreshing screenshot...
[[18:18:05]] [INFO] WbxRVpWtjw=pass
[[18:18:00]] [SUCCESS] Screenshot refreshed successfully
[[18:18:00]] [SUCCESS] Screenshot refreshed successfully
[[18:18:00]] [INFO] WbxRVpWtjw=running
[[18:18:00]] [INFO] Executing action 210/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:17:59]] [SUCCESS] Screenshot refreshed
[[18:17:59]] [INFO] Refreshing screenshot...
[[18:17:59]] [INFO] H3IAmq3r3i=pass
[[18:17:50]] [SUCCESS] Screenshot refreshed successfully
[[18:17:50]] [SUCCESS] Screenshot refreshed successfully
[[18:17:49]] [INFO] H3IAmq3r3i=running
[[18:17:49]] [INFO] Executing action 209/590: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:17:49]] [SUCCESS] Screenshot refreshed
[[18:17:49]] [INFO] Refreshing screenshot...
[[18:17:49]] [INFO] uOt2cFGhGr=pass
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:44]] [INFO] uOt2cFGhGr=running
[[18:17:44]] [INFO] Executing action 208/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:17:44]] [SUCCESS] Screenshot refreshed
[[18:17:44]] [INFO] Refreshing screenshot...
[[18:17:44]] [INFO] eLxHVWKeDQ=pass
[[18:17:40]] [SUCCESS] Screenshot refreshed successfully
[[18:17:40]] [SUCCESS] Screenshot refreshed successfully
[[18:17:40]] [INFO] eLxHVWKeDQ=running
[[18:17:40]] [INFO] Executing action 207/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:17:39]] [SUCCESS] Screenshot refreshed
[[18:17:39]] [INFO] Refreshing screenshot...
[[18:17:39]] [INFO] ghzdMuwrHj=pass
[[18:17:35]] [SUCCESS] Screenshot refreshed successfully
[[18:17:35]] [SUCCESS] Screenshot refreshed successfully
[[18:17:35]] [INFO] ghzdMuwrHj=running
[[18:17:35]] [INFO] Executing action 206/590: iOS Function: text - Text: "P_43386093"
[[18:17:34]] [SUCCESS] Screenshot refreshed
[[18:17:34]] [INFO] Refreshing screenshot...
[[18:17:34]] [INFO] fMzoZJg9I7=pass
[[18:17:29]] [SUCCESS] Screenshot refreshed successfully
[[18:17:29]] [SUCCESS] Screenshot refreshed successfully
[[18:17:29]] [INFO] fMzoZJg9I7=running
[[18:17:29]] [INFO] Executing action 205/590: Tap on Text: "Find"
[[18:17:28]] [SUCCESS] Screenshot refreshed
[[18:17:28]] [INFO] Refreshing screenshot...
[[18:17:28]] [INFO] j1JjmfPRaE=pass
[[18:17:23]] [SUCCESS] Screenshot refreshed successfully
[[18:17:23]] [SUCCESS] Screenshot refreshed successfully
[[18:17:23]] [INFO] j1JjmfPRaE=running
[[18:17:23]] [INFO] Executing action 204/590: Restart app: env[appid]
[[18:17:22]] [SUCCESS] Screenshot refreshed
[[18:17:22]] [INFO] Refreshing screenshot...
[[18:17:22]] [INFO] WbxRVpWtjw=pass
[[18:17:18]] [SUCCESS] Screenshot refreshed successfully
[[18:17:18]] [SUCCESS] Screenshot refreshed successfully
[[18:17:17]] [INFO] WbxRVpWtjw=running
[[18:17:17]] [INFO] Executing action 203/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:17:17]] [SUCCESS] Screenshot refreshed
[[18:17:17]] [INFO] Refreshing screenshot...
[[18:17:17]] [INFO] H3IAmq3r3i=pass
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [INFO] H3IAmq3r3i=running
[[18:17:09]] [INFO] Executing action 202/590: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:17:08]] [SUCCESS] Screenshot refreshed
[[18:17:08]] [INFO] Refreshing screenshot...
[[18:17:08]] [INFO] ITHvSyXXmu=pass
[[18:17:04]] [SUCCESS] Screenshot refreshed successfully
[[18:17:04]] [SUCCESS] Screenshot refreshed successfully
[[18:17:04]] [INFO] ITHvSyXXmu=running
[[18:17:04]] [INFO] Executing action 201/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:17:03]] [SUCCESS] Screenshot refreshed
[[18:17:03]] [INFO] Refreshing screenshot...
[[18:17:03]] [INFO] eLxHVWKeDQ=pass
[[18:16:47]] [SUCCESS] Screenshot refreshed successfully
[[18:16:47]] [SUCCESS] Screenshot refreshed successfully
[[18:16:47]] [INFO] eLxHVWKeDQ=running
[[18:16:47]] [INFO] Executing action 200/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[18:16:46]] [SUCCESS] Screenshot refreshed
[[18:16:46]] [INFO] Refreshing screenshot...
[[18:16:46]] [INFO] WbxRVpWtjw=pass
[[18:16:42]] [SUCCESS] Screenshot refreshed successfully
[[18:16:42]] [SUCCESS] Screenshot refreshed successfully
[[18:16:41]] [INFO] WbxRVpWtjw=running
[[18:16:41]] [INFO] Executing action 199/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:16:41]] [SUCCESS] Screenshot refreshed
[[18:16:41]] [INFO] Refreshing screenshot...
[[18:16:41]] [INFO] H3IAmq3r3i=pass
[[18:16:33]] [SUCCESS] Screenshot refreshed successfully
[[18:16:33]] [SUCCESS] Screenshot refreshed successfully
[[18:16:33]] [INFO] H3IAmq3r3i=running
[[18:16:33]] [INFO] Executing action 198/590: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:16:32]] [SUCCESS] Screenshot refreshed
[[18:16:32]] [INFO] Refreshing screenshot...
[[18:16:32]] [INFO] ITHvSyXXmu=pass
[[18:16:28]] [SUCCESS] Screenshot refreshed successfully
[[18:16:28]] [SUCCESS] Screenshot refreshed successfully
[[18:16:27]] [INFO] ITHvSyXXmu=running
[[18:16:27]] [INFO] Executing action 197/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:16:27]] [SUCCESS] Screenshot refreshed
[[18:16:27]] [INFO] Refreshing screenshot...
[[18:16:27]] [INFO] eLxHVWKeDQ=pass
[[18:16:23]] [SUCCESS] Screenshot refreshed successfully
[[18:16:23]] [SUCCESS] Screenshot refreshed successfully
[[18:16:23]] [INFO] eLxHVWKeDQ=running
[[18:16:23]] [INFO] Executing action 196/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:16:22]] [SUCCESS] Screenshot refreshed
[[18:16:22]] [INFO] Refreshing screenshot...
[[18:16:22]] [INFO] nAB6Q8LAdv=pass
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:19]] [SUCCESS] Screenshot refreshed successfully
[[18:16:18]] [INFO] nAB6Q8LAdv=running
[[18:16:18]] [INFO] Executing action 195/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:16:18]] [SUCCESS] Screenshot refreshed
[[18:16:18]] [INFO] Refreshing screenshot...
[[18:16:18]] [INFO] sc2KH9bG6H=pass
[[18:16:13]] [SUCCESS] Screenshot refreshed successfully
[[18:16:13]] [SUCCESS] Screenshot refreshed successfully
[[18:16:13]] [INFO] sc2KH9bG6H=running
[[18:16:13]] [INFO] Executing action 194/590: iOS Function: text - Text: "Uno card"
[[18:16:12]] [SUCCESS] Screenshot refreshed
[[18:16:12]] [INFO] Refreshing screenshot...
[[18:16:12]] [INFO] rqLJpAP0mA=pass
[[18:16:07]] [SUCCESS] Screenshot refreshed successfully
[[18:16:07]] [SUCCESS] Screenshot refreshed successfully
[[18:16:07]] [INFO] rqLJpAP0mA=running
[[18:16:07]] [INFO] Executing action 193/590: Tap on Text: "Find"
[[18:16:06]] [SUCCESS] Screenshot refreshed
[[18:16:06]] [INFO] Refreshing screenshot...
[[18:16:06]] [INFO] yiKyF5FJwN=pass
[[18:16:03]] [SUCCESS] Screenshot refreshed successfully
[[18:16:03]] [SUCCESS] Screenshot refreshed successfully
[[18:16:02]] [INFO] yiKyF5FJwN=running
[[18:16:02]] [INFO] Executing action 192/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:16:02]] [SUCCESS] Screenshot refreshed
[[18:16:02]] [INFO] Refreshing screenshot...
[[18:16:02]] [INFO] sTtseHOKfa=pass
[[18:15:57]] [SUCCESS] Screenshot refreshed successfully
[[18:15:57]] [SUCCESS] Screenshot refreshed successfully
[[18:15:57]] [INFO] sTtseHOKfa=running
[[18:15:57]] [INFO] Executing action 191/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:15:56]] [SUCCESS] Screenshot refreshed
[[18:15:56]] [INFO] Refreshing screenshot...
[[18:15:56]] [INFO] T3MmUw30SF=pass
[[18:15:52]] [SUCCESS] Screenshot refreshed successfully
[[18:15:52]] [SUCCESS] Screenshot refreshed successfully
[[18:15:52]] [INFO] T3MmUw30SF=running
[[18:15:52]] [INFO] Executing action 190/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:15:52]] [SUCCESS] Screenshot refreshed
[[18:15:52]] [INFO] Refreshing screenshot...
[[18:15:52]] [INFO] PPIBJbaXNx=pass
[[18:15:47]] [SUCCESS] Screenshot refreshed successfully
[[18:15:47]] [SUCCESS] Screenshot refreshed successfully
[[18:15:47]] [INFO] PPIBJbaXNx=running
[[18:15:47]] [INFO] Executing action 189/590: iOS Function: text - Text: "<EMAIL>"
[[18:15:46]] [SUCCESS] Screenshot refreshed
[[18:15:46]] [INFO] Refreshing screenshot...
[[18:15:46]] [INFO] LDkFLWks00=pass
[[18:15:42]] [SUCCESS] Screenshot refreshed successfully
[[18:15:42]] [SUCCESS] Screenshot refreshed successfully
[[18:15:42]] [INFO] LDkFLWks00=running
[[18:15:42]] [INFO] Executing action 188/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:15:41]] [SUCCESS] Screenshot refreshed
[[18:15:41]] [INFO] Refreshing screenshot...
[[18:15:41]] [INFO] 3caMBvQX7k=pass
[[18:15:38]] [SUCCESS] Screenshot refreshed successfully
[[18:15:38]] [SUCCESS] Screenshot refreshed successfully
[[18:15:38]] [INFO] 3caMBvQX7k=running
[[18:15:38]] [INFO] Executing action 187/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:15:37]] [SUCCESS] Screenshot refreshed
[[18:15:37]] [INFO] Refreshing screenshot...
[[18:15:37]] [INFO] yUJyVO5Wev=pass
[[18:15:35]] [SUCCESS] Screenshot refreshed successfully
[[18:15:35]] [SUCCESS] Screenshot refreshed successfully
[[18:15:34]] [INFO] yUJyVO5Wev=running
[[18:15:34]] [INFO] Executing action 186/590: iOS Function: alert_accept
[[18:15:34]] [SUCCESS] Screenshot refreshed
[[18:15:34]] [INFO] Refreshing screenshot...
[[18:15:34]] [INFO] rkL0oz4kiL=pass
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [INFO] rkL0oz4kiL=running
[[18:15:28]] [INFO] Executing action 185/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:15:27]] [SUCCESS] Screenshot refreshed
[[18:15:27]] [INFO] Refreshing screenshot...
[[18:15:27]] [INFO] HotUJOd6oB=pass
[[18:15:14]] [SUCCESS] Screenshot refreshed successfully
[[18:15:14]] [SUCCESS] Screenshot refreshed successfully
[[18:15:13]] [INFO] HotUJOd6oB=running
[[18:15:13]] [INFO] Executing action 184/590: Restart app: env[appid]
[[18:15:13]] [SUCCESS] Screenshot refreshed
[[18:15:13]] [INFO] Refreshing screenshot...
[[18:15:13]] [SUCCESS] Screenshot refreshed
[[18:15:13]] [INFO] Refreshing screenshot...
[[18:15:10]] [SUCCESS] Screenshot refreshed successfully
[[18:15:10]] [SUCCESS] Screenshot refreshed successfully
[[18:15:09]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:15:09]] [SUCCESS] Screenshot refreshed
[[18:15:09]] [INFO] Refreshing screenshot...
[[18:15:03]] [SUCCESS] Screenshot refreshed successfully
[[18:15:03]] [SUCCESS] Screenshot refreshed successfully
[[18:15:03]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:15:03]] [SUCCESS] Screenshot refreshed
[[18:15:03]] [INFO] Refreshing screenshot...
[[18:14:59]] [SUCCESS] Screenshot refreshed successfully
[[18:14:59]] [SUCCESS] Screenshot refreshed successfully
[[18:14:59]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:14:58]] [SUCCESS] Screenshot refreshed
[[18:14:58]] [INFO] Refreshing screenshot...
[[18:14:54]] [SUCCESS] Screenshot refreshed successfully
[[18:14:54]] [SUCCESS] Screenshot refreshed successfully
[[18:14:53]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:14:53]] [SUCCESS] Screenshot refreshed
[[18:14:53]] [INFO] Refreshing screenshot...
[[18:14:46]] [SUCCESS] Screenshot refreshed successfully
[[18:14:46]] [SUCCESS] Screenshot refreshed successfully
[[18:14:46]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:14:45]] [SUCCESS] Screenshot refreshed
[[18:14:45]] [INFO] Refreshing screenshot...
[[18:14:38]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:14:38]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:14:38]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:14:38]] [INFO] IR7wnjW7C8=running
[[18:14:38]] [INFO] Executing action 183/590: cleanupSteps action
[[18:14:38]] [INFO] Skipping remaining steps in failed test case (moving from action 164 to 182), but preserving cleanup steps
[[18:14:38]] [INFO] NurQsFoMkE=fail
[[18:14:38]] [ERROR] Action 164 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]'
[[18:14:23]] [INFO] NurQsFoMkE=running
[[18:14:23]] [INFO] Executing action 164/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:14:23]] [SUCCESS] Screenshot refreshed successfully
[[18:14:23]] [SUCCESS] Screenshot refreshed successfully
[[18:14:23]] [SUCCESS] Screenshot refreshed
[[18:14:23]] [INFO] Refreshing screenshot...
[[18:14:23]] [INFO] CkfAScJNq8=pass
[[18:14:19]] [SUCCESS] Screenshot refreshed successfully
[[18:14:19]] [SUCCESS] Screenshot refreshed successfully
[[18:14:19]] [INFO] CkfAScJNq8=running
[[18:14:19]] [INFO] Executing action 163/590: Tap on image: env[closebtnimage]
[[18:14:18]] [SUCCESS] Screenshot refreshed
[[18:14:18]] [INFO] Refreshing screenshot...
[[18:14:18]] [INFO] 1NWfFsDiTQ=pass
[[18:14:14]] [SUCCESS] Screenshot refreshed successfully
[[18:14:14]] [SUCCESS] Screenshot refreshed successfully
[[18:14:14]] [INFO] 1NWfFsDiTQ=running
[[18:14:14]] [INFO] Executing action 162/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:14:14]] [SUCCESS] Screenshot refreshed
[[18:14:14]] [INFO] Refreshing screenshot...
[[18:14:14]] [INFO] tufIibCj03=pass
[[18:14:10]] [SUCCESS] Screenshot refreshed successfully
[[18:14:10]] [SUCCESS] Screenshot refreshed successfully
[[18:14:09]] [INFO] tufIibCj03=running
[[18:14:09]] [INFO] Executing action 161/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:14:09]] [SUCCESS] Screenshot refreshed
[[18:14:09]] [INFO] Refreshing screenshot...
[[18:14:09]] [INFO] g8u66qfKkX=pass
[[18:14:05]] [SUCCESS] Screenshot refreshed successfully
[[18:14:05]] [SUCCESS] Screenshot refreshed successfully
[[18:14:05]] [INFO] g8u66qfKkX=running
[[18:14:05]] [INFO] Executing action 160/590: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:14:05]] [SUCCESS] Screenshot refreshed
[[18:14:05]] [INFO] Refreshing screenshot...
[[18:14:05]] [INFO] mg4S62Rdtq=pass
[[18:13:52]] [INFO] mg4S62Rdtq=running
[[18:13:52]] [INFO] Executing action 159/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:13:52]] [SUCCESS] Screenshot refreshed successfully
[[18:13:52]] [SUCCESS] Screenshot refreshed successfully
[[18:13:52]] [SUCCESS] Screenshot refreshed
[[18:13:52]] [INFO] Refreshing screenshot...
[[18:13:52]] [INFO] pCPTAtSZbf=pass
[[18:13:47]] [SUCCESS] Screenshot refreshed successfully
[[18:13:47]] [SUCCESS] Screenshot refreshed successfully
[[18:13:47]] [INFO] pCPTAtSZbf=running
[[18:13:47]] [INFO] Executing action 158/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:13:47]] [SUCCESS] Screenshot refreshed
[[18:13:47]] [INFO] Refreshing screenshot...
[[18:13:47]] [INFO] DaVBARRwft=pass
[[18:13:43]] [SUCCESS] Screenshot refreshed successfully
[[18:13:43]] [SUCCESS] Screenshot refreshed successfully
[[18:13:42]] [INFO] DaVBARRwft=running
[[18:13:42]] [INFO] Executing action 157/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[18:13:42]] [SUCCESS] Screenshot refreshed
[[18:13:42]] [INFO] Refreshing screenshot...
[[18:13:42]] [INFO] e1RoZWCZJb=pass
[[18:13:37]] [SUCCESS] Screenshot refreshed successfully
[[18:13:37]] [SUCCESS] Screenshot refreshed successfully
[[18:13:37]] [INFO] e1RoZWCZJb=running
[[18:13:37]] [INFO] Executing action 156/590: iOS Function: text - Text: "<EMAIL>"
[[18:13:37]] [SUCCESS] Screenshot refreshed
[[18:13:37]] [INFO] Refreshing screenshot...
[[18:13:37]] [INFO] 50Z2jrodNd=pass
[[18:13:32]] [SUCCESS] Screenshot refreshed successfully
[[18:13:32]] [SUCCESS] Screenshot refreshed successfully
[[18:13:32]] [INFO] 50Z2jrodNd=running
[[18:13:32]] [INFO] Executing action 155/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:13:32]] [SUCCESS] Screenshot refreshed
[[18:13:32]] [INFO] Refreshing screenshot...
[[18:13:32]] [INFO] q9ZiyYoE5B=pass
[[18:13:29]] [SUCCESS] Screenshot refreshed successfully
[[18:13:29]] [SUCCESS] Screenshot refreshed successfully
[[18:13:29]] [INFO] q9ZiyYoE5B=running
[[18:13:29]] [INFO] Executing action 154/590: iOS Function: alert_accept
[[18:13:28]] [SUCCESS] Screenshot refreshed
[[18:13:28]] [INFO] Refreshing screenshot...
[[18:13:28]] [INFO] 6PL8P3rT57=pass
[[18:13:23]] [SUCCESS] Screenshot refreshed successfully
[[18:13:23]] [SUCCESS] Screenshot refreshed successfully
[[18:13:23]] [INFO] 6PL8P3rT57=running
[[18:13:23]] [INFO] Executing action 153/590: Tap on Text: "Sign"
[[18:13:23]] [SUCCESS] Screenshot refreshed
[[18:13:23]] [INFO] Refreshing screenshot...
[[18:13:23]] [INFO] 2YGctqXNED=pass
[[18:13:17]] [SUCCESS] Screenshot refreshed successfully
[[18:13:17]] [SUCCESS] Screenshot refreshed successfully
[[18:13:17]] [INFO] 2YGctqXNED=running
[[18:13:17]] [INFO] Executing action 152/590: Tap on element with accessibility_id: Continue to details
[[18:13:16]] [SUCCESS] Screenshot refreshed
[[18:13:16]] [INFO] Refreshing screenshot...
[[18:13:16]] [INFO] 2YGctqXNED=pass
[[18:13:08]] [SUCCESS] Screenshot refreshed successfully
[[18:13:08]] [SUCCESS] Screenshot refreshed successfully
[[18:13:08]] [INFO] 2YGctqXNED=running
[[18:13:08]] [INFO] Executing action 151/590: Swipe up till element accessibilityid: "Continue to details" is visible
[[18:13:07]] [SUCCESS] Screenshot refreshed
[[18:13:07]] [INFO] Refreshing screenshot...
[[18:13:07]] [INFO] tufIibCj03=pass
[[18:13:03]] [SUCCESS] Screenshot refreshed successfully
[[18:13:03]] [SUCCESS] Screenshot refreshed successfully
[[18:13:03]] [INFO] tufIibCj03=running
[[18:13:03]] [INFO] Executing action 150/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:13:02]] [SUCCESS] Screenshot refreshed
[[18:13:02]] [INFO] Refreshing screenshot...
[[18:13:02]] [INFO] g8u66qfKkX=pass
[[18:12:59]] [SUCCESS] Screenshot refreshed successfully
[[18:12:59]] [SUCCESS] Screenshot refreshed successfully
[[18:12:59]] [INFO] g8u66qfKkX=running
[[18:12:59]] [INFO] Executing action 149/590: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:12:58]] [SUCCESS] Screenshot refreshed
[[18:12:58]] [INFO] Refreshing screenshot...
[[18:12:58]] [INFO] ZBXuV4sJUR=pass
[[18:12:46]] [INFO] ZBXuV4sJUR=running
[[18:12:46]] [INFO] Executing action 148/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:12:46]] [SUCCESS] Screenshot refreshed successfully
[[18:12:46]] [SUCCESS] Screenshot refreshed successfully
[[18:12:46]] [SUCCESS] Screenshot refreshed
[[18:12:46]] [INFO] Refreshing screenshot...
[[18:12:46]] [INFO] XryN8qR1DX=pass
[[18:12:41]] [SUCCESS] Screenshot refreshed successfully
[[18:12:41]] [SUCCESS] Screenshot refreshed successfully
[[18:12:41]] [INFO] XryN8qR1DX=running
[[18:12:41]] [INFO] Executing action 147/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:12:40]] [SUCCESS] Screenshot refreshed
[[18:12:40]] [INFO] Refreshing screenshot...
[[18:12:40]] [INFO] XcWXIMtv1E=pass
[[18:12:34]] [SUCCESS] Screenshot refreshed successfully
[[18:12:34]] [SUCCESS] Screenshot refreshed successfully
[[18:12:33]] [INFO] XcWXIMtv1E=running
[[18:12:33]] [INFO] Executing action 146/590: Wait for 5 ms
[[18:12:33]] [SUCCESS] Screenshot refreshed
[[18:12:33]] [INFO] Refreshing screenshot...
[[18:12:33]] [INFO] S1cQQxksEj=pass
[[18:12:24]] [SUCCESS] Screenshot refreshed successfully
[[18:12:24]] [SUCCESS] Screenshot refreshed successfully
[[18:12:24]] [INFO] S1cQQxksEj=running
[[18:12:24]] [INFO] Executing action 145/590: Tap on element with accessibility_id: Add to bag
[[18:12:23]] [SUCCESS] Screenshot refreshed
[[18:12:23]] [INFO] Refreshing screenshot...
[[18:12:23]] [INFO] K2w9XUGwnb=pass
[[18:12:14]] [SUCCESS] Screenshot refreshed successfully
[[18:12:14]] [SUCCESS] Screenshot refreshed successfully
[[18:12:14]] [INFO] K2w9XUGwnb=running
[[18:12:14]] [INFO] Executing action 144/590: Swipe up till element accessibility_id: "Add to bag" is visible
[[18:12:13]] [SUCCESS] Screenshot refreshed
[[18:12:13]] [INFO] Refreshing screenshot...
[[18:12:13]] [INFO] BTYxjEaZEk=pass
[[18:12:09]] [SUCCESS] Screenshot refreshed successfully
[[18:12:09]] [SUCCESS] Screenshot refreshed successfully
[[18:12:09]] [INFO] BTYxjEaZEk=running
[[18:12:09]] [INFO] Executing action 143/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:12:08]] [SUCCESS] Screenshot refreshed
[[18:12:08]] [INFO] Refreshing screenshot...
[[18:12:08]] [INFO] YC6bBrKQgq=pass
[[18:12:05]] [SUCCESS] Screenshot refreshed successfully
[[18:12:05]] [SUCCESS] Screenshot refreshed successfully
[[18:12:04]] [INFO] YC6bBrKQgq=running
[[18:12:04]] [INFO] Executing action 142/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:12:04]] [SUCCESS] Screenshot refreshed
[[18:12:04]] [INFO] Refreshing screenshot...
[[18:12:04]] [INFO] aRgHcQcLDP=pass
[[18:12:00]] [SUCCESS] Screenshot refreshed successfully
[[18:12:00]] [SUCCESS] Screenshot refreshed successfully
[[18:11:59]] [INFO] aRgHcQcLDP=running
[[18:11:59]] [INFO] Executing action 141/590: iOS Function: text - Text: "uno card"
[[18:11:59]] [SUCCESS] Screenshot refreshed
[[18:11:59]] [INFO] Refreshing screenshot...
[[18:11:59]] [INFO] 4PZC1vVWJW=pass
[[18:11:54]] [SUCCESS] Screenshot refreshed successfully
[[18:11:54]] [SUCCESS] Screenshot refreshed successfully
[[18:11:53]] [INFO] 4PZC1vVWJW=running
[[18:11:53]] [INFO] Executing action 140/590: Tap on Text: "Find"
[[18:11:53]] [SUCCESS] Screenshot refreshed
[[18:11:53]] [INFO] Refreshing screenshot...
[[18:11:53]] [INFO] XryN8qR1DX=pass
[[18:11:49]] [SUCCESS] Screenshot refreshed successfully
[[18:11:49]] [SUCCESS] Screenshot refreshed successfully
[[18:11:48]] [INFO] XryN8qR1DX=running
[[18:11:48]] [INFO] Executing action 139/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:11:48]] [SUCCESS] Screenshot refreshed
[[18:11:48]] [INFO] Refreshing screenshot...
[[18:11:48]] [INFO] 7WYExJTqjp=pass
[[18:11:43]] [SUCCESS] Screenshot refreshed successfully
[[18:11:43]] [SUCCESS] Screenshot refreshed successfully
[[18:11:43]] [INFO] 7WYExJTqjp=running
[[18:11:43]] [INFO] Executing action 138/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:11:43]] [SUCCESS] Screenshot refreshed
[[18:11:43]] [INFO] Refreshing screenshot...
[[18:11:43]] [INFO] 4WfPFN961S=pass
[[18:11:36]] [SUCCESS] Screenshot refreshed successfully
[[18:11:36]] [SUCCESS] Screenshot refreshed successfully
[[18:11:36]] [INFO] 4WfPFN961S=running
[[18:11:36]] [INFO] Executing action 137/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:11:35]] [SUCCESS] Screenshot refreshed
[[18:11:35]] [INFO] Refreshing screenshot...
[[18:11:35]] [INFO] NurQsFoMkE=pass
[[18:11:33]] [SUCCESS] Screenshot refreshed successfully
[[18:11:33]] [SUCCESS] Screenshot refreshed successfully
[[18:11:31]] [INFO] NurQsFoMkE=running
[[18:11:31]] [INFO] Executing action 136/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:11:31]] [SUCCESS] Screenshot refreshed
[[18:11:31]] [INFO] Refreshing screenshot...
[[18:11:31]] [SUCCESS] Screenshot refreshed
[[18:11:31]] [INFO] Refreshing screenshot...
[[18:11:27]] [SUCCESS] Screenshot refreshed successfully
[[18:11:27]] [SUCCESS] Screenshot refreshed successfully
[[18:11:26]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:11:25]] [SUCCESS] Screenshot refreshed
[[18:11:25]] [INFO] Refreshing screenshot...
[[18:11:20]] [SUCCESS] Screenshot refreshed successfully
[[18:11:20]] [SUCCESS] Screenshot refreshed successfully
[[18:11:20]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:11:20]] [SUCCESS] Screenshot refreshed
[[18:11:20]] [INFO] Refreshing screenshot...
[[18:11:15]] [SUCCESS] Screenshot refreshed successfully
[[18:11:15]] [SUCCESS] Screenshot refreshed successfully
[[18:11:15]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:11:14]] [SUCCESS] Screenshot refreshed
[[18:11:14]] [INFO] Refreshing screenshot...
[[18:11:10]] [SUCCESS] Screenshot refreshed successfully
[[18:11:10]] [SUCCESS] Screenshot refreshed successfully
[[18:11:10]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:11:10]] [SUCCESS] Screenshot refreshed
[[18:11:10]] [INFO] Refreshing screenshot...
[[18:11:04]] [SUCCESS] Screenshot refreshed successfully
[[18:11:04]] [SUCCESS] Screenshot refreshed successfully
[[18:11:04]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:11:04]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:11:04]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:11:04]] [INFO] APqAlKbucp=running
[[18:11:04]] [INFO] Executing action 135/590: Execute Test Case: Kmart-Signin (5 steps)
[[18:11:03]] [SUCCESS] Screenshot refreshed
[[18:11:03]] [INFO] Refreshing screenshot...
[[18:11:03]] [INFO] byEe7qbCpq=pass
[[18:11:00]] [SUCCESS] Screenshot refreshed successfully
[[18:11:00]] [SUCCESS] Screenshot refreshed successfully
[[18:11:00]] [INFO] byEe7qbCpq=running
[[18:11:00]] [INFO] Executing action 134/590: iOS Function: alert_accept
[[18:11:00]] [SUCCESS] Screenshot refreshed
[[18:11:00]] [INFO] Refreshing screenshot...
[[18:11:00]] [INFO] L6wTorOX8B=pass
[[18:10:57]] [SUCCESS] Screenshot refreshed successfully
[[18:10:57]] [SUCCESS] Screenshot refreshed successfully
[[18:10:56]] [INFO] L6wTorOX8B=running
[[18:10:56]] [INFO] Executing action 133/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[18:10:55]] [SUCCESS] Screenshot refreshed
[[18:10:55]] [INFO] Refreshing screenshot...
[[18:10:55]] [INFO] XryN8qR1DX=pass
[[18:10:53]] [SUCCESS] Screenshot refreshed successfully
[[18:10:53]] [SUCCESS] Screenshot refreshed successfully
[[18:10:51]] [INFO] XryN8qR1DX=running
[[18:10:51]] [INFO] Executing action 132/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:10:51]] [SUCCESS] Screenshot refreshed
[[18:10:51]] [INFO] Refreshing screenshot...
[[18:10:51]] [INFO] lCSewtjn1z=pass
[[18:10:46]] [SUCCESS] Screenshot refreshed successfully
[[18:10:46]] [SUCCESS] Screenshot refreshed successfully
[[18:10:45]] [INFO] lCSewtjn1z=running
[[18:10:45]] [INFO] Executing action 131/590: Restart app: env[appid]
[[18:10:45]] [SUCCESS] Screenshot refreshed
[[18:10:45]] [INFO] Refreshing screenshot...
[[18:10:45]] [INFO] IJh702cxG0=pass
[[18:10:40]] [SUCCESS] Screenshot refreshed successfully
[[18:10:40]] [SUCCESS] Screenshot refreshed successfully
[[18:10:40]] [INFO] IJh702cxG0=running
[[18:10:40]] [INFO] Executing action 130/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:10:40]] [SUCCESS] Screenshot refreshed
[[18:10:40]] [INFO] Refreshing screenshot...
[[18:10:40]] [INFO] 4WfPFN961S=pass
[[18:10:33]] [SUCCESS] Screenshot refreshed successfully
[[18:10:33]] [SUCCESS] Screenshot refreshed successfully
[[18:10:33]] [INFO] 4WfPFN961S=running
[[18:10:33]] [INFO] Executing action 129/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:10:32]] [SUCCESS] Screenshot refreshed
[[18:10:32]] [INFO] Refreshing screenshot...
[[18:10:32]] [INFO] AOcOOSuOsB=pass
[[18:10:28]] [SUCCESS] Screenshot refreshed successfully
[[18:10:28]] [SUCCESS] Screenshot refreshed successfully
[[18:10:28]] [INFO] AOcOOSuOsB=running
[[18:10:28]] [INFO] Executing action 128/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:10:27]] [SUCCESS] Screenshot refreshed
[[18:10:27]] [INFO] Refreshing screenshot...
[[18:10:27]] [INFO] AOcOOSuOsB=pass
[[18:10:21]] [SUCCESS] Screenshot refreshed successfully
[[18:10:21]] [SUCCESS] Screenshot refreshed successfully
[[18:10:20]] [INFO] AOcOOSuOsB=running
[[18:10:20]] [INFO] Executing action 127/590: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:10:20]] [SUCCESS] Screenshot refreshed
[[18:10:20]] [INFO] Refreshing screenshot...
[[18:10:20]] [INFO] N2yjynioko=pass
[[18:10:15]] [SUCCESS] Screenshot refreshed successfully
[[18:10:15]] [SUCCESS] Screenshot refreshed successfully
[[18:10:15]] [INFO] N2yjynioko=running
[[18:10:15]] [INFO] Executing action 126/590: iOS Function: text - Text: "Wonderbaby@5"
[[18:10:14]] [SUCCESS] Screenshot refreshed
[[18:10:14]] [INFO] Refreshing screenshot...
[[18:10:14]] [INFO] SHaIduBnay=pass
[[18:10:10]] [SUCCESS] Screenshot refreshed successfully
[[18:10:10]] [SUCCESS] Screenshot refreshed successfully
[[18:10:10]] [INFO] SHaIduBnay=running
[[18:10:10]] [INFO] Executing action 125/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[18:10:09]] [SUCCESS] Screenshot refreshed
[[18:10:09]] [INFO] Refreshing screenshot...
[[18:10:09]] [INFO] wuIMlAwYVA=pass
[[18:10:04]] [SUCCESS] Screenshot refreshed successfully
[[18:10:04]] [SUCCESS] Screenshot refreshed successfully
[[18:10:04]] [INFO] wuIMlAwYVA=running
[[18:10:04]] [INFO] Executing action 124/590: iOS Function: text - Text: "env[uname1]"
[[18:10:04]] [SUCCESS] Screenshot refreshed
[[18:10:04]] [INFO] Refreshing screenshot...
[[18:10:04]] [INFO] 50Z2jrodNd=pass
[[18:10:00]] [SUCCESS] Screenshot refreshed successfully
[[18:10:00]] [SUCCESS] Screenshot refreshed successfully
[[18:09:59]] [INFO] 50Z2jrodNd=running
[[18:09:59]] [INFO] Executing action 123/590: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[18:09:59]] [SUCCESS] Screenshot refreshed
[[18:09:59]] [INFO] Refreshing screenshot...
[[18:09:59]] [INFO] VK2oI6mXSB=pass
[[18:09:54]] [SUCCESS] Screenshot refreshed successfully
[[18:09:54]] [SUCCESS] Screenshot refreshed successfully
[[18:09:54]] [INFO] VK2oI6mXSB=running
[[18:09:54]] [INFO] Executing action 122/590: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[18:09:54]] [SUCCESS] Screenshot refreshed
[[18:09:54]] [INFO] Refreshing screenshot...
[[18:09:54]] [INFO] q9ZiyYoE5B=pass
[[18:09:52]] [SUCCESS] Screenshot refreshed successfully
[[18:09:52]] [SUCCESS] Screenshot refreshed successfully
[[18:09:51]] [INFO] q9ZiyYoE5B=running
[[18:09:51]] [INFO] Executing action 121/590: iOS Function: alert_accept
[[18:09:51]] [SUCCESS] Screenshot refreshed
[[18:09:51]] [INFO] Refreshing screenshot...
[[18:09:51]] [INFO] 4PZC1vVWJW=pass
[[18:09:45]] [SUCCESS] Screenshot refreshed successfully
[[18:09:45]] [SUCCESS] Screenshot refreshed successfully
[[18:09:45]] [INFO] 4PZC1vVWJW=running
[[18:09:45]] [INFO] Executing action 120/590: Tap on Text: "Sign"
[[18:09:44]] [SUCCESS] Screenshot refreshed
[[18:09:44]] [INFO] Refreshing screenshot...
[[18:09:44]] [INFO] mcscWdhpn2=pass
[[18:09:28]] [SUCCESS] Screenshot refreshed successfully
[[18:09:28]] [SUCCESS] Screenshot refreshed successfully
[[18:09:26]] [INFO] mcscWdhpn2=running
[[18:09:26]] [INFO] Executing action 119/590: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[18:09:25]] [SUCCESS] Screenshot refreshed
[[18:09:25]] [INFO] Refreshing screenshot...
[[18:09:25]] [INFO] 6zUBxjSFym=pass
[[18:09:20]] [SUCCESS] Screenshot refreshed successfully
[[18:09:20]] [SUCCESS] Screenshot refreshed successfully
[[18:09:20]] [INFO] 6zUBxjSFym=running
[[18:09:20]] [INFO] Executing action 118/590: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:09:19]] [SUCCESS] Screenshot refreshed
[[18:09:19]] [INFO] Refreshing screenshot...
[[18:09:19]] [INFO] BTYxjEaZEk=pass
[[18:09:15]] [SUCCESS] Screenshot refreshed successfully
[[18:09:15]] [SUCCESS] Screenshot refreshed successfully
[[18:09:14]] [INFO] BTYxjEaZEk=running
[[18:09:14]] [INFO] Executing action 117/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:09:14]] [SUCCESS] Screenshot refreshed
[[18:09:14]] [INFO] Refreshing screenshot...
[[18:09:14]] [INFO] YC6bBrKQgq=pass
[[18:09:10]] [SUCCESS] Screenshot refreshed successfully
[[18:09:10]] [SUCCESS] Screenshot refreshed successfully
[[18:09:10]] [INFO] YC6bBrKQgq=running
[[18:09:10]] [INFO] Executing action 116/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:09:09]] [SUCCESS] Screenshot refreshed
[[18:09:09]] [INFO] Refreshing screenshot...
[[18:09:09]] [INFO] aRgHcQcLDP=pass
[[18:09:05]] [SUCCESS] Screenshot refreshed successfully
[[18:09:05]] [SUCCESS] Screenshot refreshed successfully
[[18:09:05]] [INFO] aRgHcQcLDP=running
[[18:09:05]] [INFO] Executing action 115/590: iOS Function: text - Text: "uno card"
[[18:09:04]] [SUCCESS] Screenshot refreshed
[[18:09:04]] [INFO] Refreshing screenshot...
[[18:09:04]] [INFO] 4PZC1vVWJW=pass
[[18:08:59]] [SUCCESS] Screenshot refreshed successfully
[[18:08:59]] [SUCCESS] Screenshot refreshed successfully
[[18:08:58]] [INFO] 4PZC1vVWJW=running
[[18:08:58]] [INFO] Executing action 114/590: Tap on Text: "Find"
[[18:08:58]] [SUCCESS] Screenshot refreshed
[[18:08:58]] [INFO] Refreshing screenshot...
[[18:08:58]] [INFO] lCSewtjn1z=pass
[[18:08:53]] [SUCCESS] Screenshot refreshed successfully
[[18:08:53]] [SUCCESS] Screenshot refreshed successfully
[[18:08:52]] [INFO] lCSewtjn1z=running
[[18:08:52]] [INFO] Executing action 113/590: Restart app: env[appid]
[[18:08:52]] [SUCCESS] Screenshot refreshed
[[18:08:52]] [INFO] Refreshing screenshot...
[[18:08:52]] [INFO] A1Wz7p1iVG=pass
[[18:08:47]] [SUCCESS] Screenshot refreshed successfully
[[18:08:47]] [SUCCESS] Screenshot refreshed successfully
[[18:08:47]] [INFO] A1Wz7p1iVG=running
[[18:08:47]] [INFO] Executing action 112/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:08:47]] [SUCCESS] Screenshot refreshed
[[18:08:47]] [INFO] Refreshing screenshot...
[[18:08:47]] [INFO] ehyLmdZWP2=pass
[[18:08:40]] [SUCCESS] Screenshot refreshed successfully
[[18:08:40]] [SUCCESS] Screenshot refreshed successfully
[[18:08:40]] [INFO] ehyLmdZWP2=running
[[18:08:40]] [INFO] Executing action 111/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:08:39]] [SUCCESS] Screenshot refreshed
[[18:08:39]] [INFO] Refreshing screenshot...
[[18:08:39]] [INFO] ydRnBBO1vR=pass
[[18:08:36]] [SUCCESS] Screenshot refreshed successfully
[[18:08:36]] [SUCCESS] Screenshot refreshed successfully
[[18:08:35]] [INFO] ydRnBBO1vR=running
[[18:08:35]] [INFO] Executing action 110/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:08:35]] [SUCCESS] Screenshot refreshed
[[18:08:35]] [INFO] Refreshing screenshot...
[[18:08:35]] [INFO] quZwUwj3a8=pass
[[18:08:31]] [SUCCESS] Screenshot refreshed successfully
[[18:08:31]] [SUCCESS] Screenshot refreshed successfully
[[18:08:30]] [INFO] quZwUwj3a8=running
[[18:08:30]] [INFO] Executing action 109/590: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:08:30]] [SUCCESS] Screenshot refreshed
[[18:08:30]] [INFO] Refreshing screenshot...
[[18:08:30]] [INFO] FHRlQXe58T=pass
[[18:08:25]] [SUCCESS] Screenshot refreshed successfully
[[18:08:25]] [SUCCESS] Screenshot refreshed successfully
[[18:08:25]] [INFO] FHRlQXe58T=running
[[18:08:25]] [INFO] Executing action 108/590: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:08:25]] [SUCCESS] Screenshot refreshed
[[18:08:25]] [INFO] Refreshing screenshot...
[[18:08:25]] [INFO] 8uojw2klHA=pass
[[18:08:19]] [SUCCESS] Screenshot refreshed successfully
[[18:08:19]] [SUCCESS] Screenshot refreshed successfully
[[18:08:19]] [INFO] 8uojw2klHA=running
[[18:08:19]] [INFO] Executing action 107/590: iOS Function: text - Text: "env[pwd]"
[[18:08:19]] [SUCCESS] Screenshot refreshed
[[18:08:19]] [INFO] Refreshing screenshot...
[[18:08:19]] [INFO] SHaIduBnay=pass
[[18:08:14]] [SUCCESS] Screenshot refreshed successfully
[[18:08:14]] [SUCCESS] Screenshot refreshed successfully
[[18:08:14]] [INFO] SHaIduBnay=running
[[18:08:14]] [INFO] Executing action 106/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:08:14]] [SUCCESS] Screenshot refreshed
[[18:08:14]] [INFO] Refreshing screenshot...
[[18:08:14]] [INFO] TGoXyeQtB7=pass
[[18:08:09]] [SUCCESS] Screenshot refreshed successfully
[[18:08:09]] [SUCCESS] Screenshot refreshed successfully
[[18:08:09]] [INFO] TGoXyeQtB7=running
[[18:08:09]] [INFO] Executing action 105/590: iOS Function: text - Text: "env[uname]"
[[18:08:08]] [SUCCESS] Screenshot refreshed
[[18:08:08]] [INFO] Refreshing screenshot...
[[18:08:08]] [INFO] rLCI6NVxSc=pass
[[18:08:04]] [SUCCESS] Screenshot refreshed successfully
[[18:08:04]] [SUCCESS] Screenshot refreshed successfully
[[18:08:04]] [INFO] rLCI6NVxSc=running
[[18:08:04]] [INFO] Executing action 104/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:08:04]] [SUCCESS] Screenshot refreshed
[[18:08:04]] [INFO] Refreshing screenshot...
[[18:08:04]] [INFO] 6mHVWI3j5e=pass
[[18:08:00]] [SUCCESS] Screenshot refreshed successfully
[[18:08:00]] [SUCCESS] Screenshot refreshed successfully
[[18:08:00]] [INFO] 6mHVWI3j5e=running
[[18:08:00]] [INFO] Executing action 103/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:07:59]] [SUCCESS] Screenshot refreshed
[[18:07:59]] [INFO] Refreshing screenshot...
[[18:07:59]] [INFO] rJVGLpLWM3=pass
[[18:07:57]] [SUCCESS] Screenshot refreshed successfully
[[18:07:57]] [SUCCESS] Screenshot refreshed successfully
[[18:07:56]] [INFO] rJVGLpLWM3=running
[[18:07:56]] [INFO] Executing action 102/590: iOS Function: alert_accept
[[18:07:56]] [SUCCESS] Screenshot refreshed
[[18:07:56]] [INFO] Refreshing screenshot...
[[18:07:56]] [INFO] WlISsMf9QA=pass
[[18:07:52]] [INFO] WlISsMf9QA=running
[[18:07:52]] [INFO] Executing action 101/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[18:07:52]] [SUCCESS] Screenshot refreshed successfully
[[18:07:52]] [SUCCESS] Screenshot refreshed successfully
[[18:07:51]] [SUCCESS] Screenshot refreshed
[[18:07:51]] [INFO] Refreshing screenshot...
[[18:07:51]] [INFO] IvqPpScAJa=pass
[[18:07:48]] [SUCCESS] Screenshot refreshed successfully
[[18:07:48]] [SUCCESS] Screenshot refreshed successfully
[[18:07:47]] [INFO] IvqPpScAJa=running
[[18:07:47]] [INFO] Executing action 100/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:07:47]] [SUCCESS] Screenshot refreshed
[[18:07:47]] [INFO] Refreshing screenshot...
[[18:07:47]] [INFO] bGo3feCwBQ=pass
[[18:07:42]] [SUCCESS] Screenshot refreshed successfully
[[18:07:42]] [SUCCESS] Screenshot refreshed successfully
[[18:07:42]] [INFO] bGo3feCwBQ=running
[[18:07:42]] [INFO] Executing action 99/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:07:42]] [SUCCESS] Screenshot refreshed
[[18:07:42]] [INFO] Refreshing screenshot...
[[18:07:42]] [INFO] 4WfPFN961S=pass
[[18:07:35]] [SUCCESS] Screenshot refreshed successfully
[[18:07:35]] [SUCCESS] Screenshot refreshed successfully
[[18:07:35]] [INFO] 4WfPFN961S=running
[[18:07:35]] [INFO] Executing action 98/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:07:34]] [SUCCESS] Screenshot refreshed
[[18:07:34]] [INFO] Refreshing screenshot...
[[18:07:34]] [INFO] F0gZF1jEnT=pass
[[18:07:31]] [SUCCESS] Screenshot refreshed successfully
[[18:07:31]] [SUCCESS] Screenshot refreshed successfully
[[18:07:30]] [INFO] F0gZF1jEnT=running
[[18:07:30]] [INFO] Executing action 97/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:07:30]] [SUCCESS] Screenshot refreshed
[[18:07:30]] [INFO] Refreshing screenshot...
[[18:07:30]] [INFO] EDHl0X27Wi=pass
[[18:07:18]] [SUCCESS] Screenshot refreshed successfully
[[18:07:18]] [SUCCESS] Screenshot refreshed successfully
[[18:07:18]] [INFO] EDHl0X27Wi=running
[[18:07:18]] [INFO] Executing action 96/590: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:07:17]] [SUCCESS] Screenshot refreshed
[[18:07:17]] [INFO] Refreshing screenshot...
[[18:07:17]] [INFO] j8NXU87gV3=pass
[[18:07:12]] [SUCCESS] Screenshot refreshed successfully
[[18:07:12]] [SUCCESS] Screenshot refreshed successfully
[[18:07:12]] [INFO] j8NXU87gV3=running
[[18:07:12]] [INFO] Executing action 95/590: iOS Function: text - Text: "env[pwd]"
[[18:07:12]] [SUCCESS] Screenshot refreshed
[[18:07:12]] [INFO] Refreshing screenshot...
[[18:07:12]] [INFO] dpVaKL19uc=pass
[[18:07:07]] [SUCCESS] Screenshot refreshed successfully
[[18:07:07]] [SUCCESS] Screenshot refreshed successfully
[[18:07:07]] [INFO] dpVaKL19uc=running
[[18:07:07]] [INFO] Executing action 94/590: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:07:07]] [SUCCESS] Screenshot refreshed
[[18:07:07]] [INFO] Refreshing screenshot...
[[18:07:07]] [INFO] eOm1WExcrK=pass
[[18:07:02]] [SUCCESS] Screenshot refreshed successfully
[[18:07:02]] [SUCCESS] Screenshot refreshed successfully
[[18:07:02]] [INFO] eOm1WExcrK=running
[[18:07:02]] [INFO] Executing action 93/590: iOS Function: text - Text: "env[uname]"
[[18:07:01]] [SUCCESS] Screenshot refreshed
[[18:07:01]] [INFO] Refreshing screenshot...
[[18:07:01]] [INFO] 50Z2jrodNd=pass
[[18:06:57]] [SUCCESS] Screenshot refreshed successfully
[[18:06:57]] [SUCCESS] Screenshot refreshed successfully
[[18:06:57]] [INFO] 50Z2jrodNd=running
[[18:06:57]] [INFO] Executing action 92/590: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:06:56]] [SUCCESS] Screenshot refreshed
[[18:06:56]] [INFO] Refreshing screenshot...
[[18:06:56]] [INFO] eJnHS9n9VL=pass
[[18:06:52]] [SUCCESS] Screenshot refreshed successfully
[[18:06:52]] [SUCCESS] Screenshot refreshed successfully
[[18:06:52]] [INFO] eJnHS9n9VL=running
[[18:06:52]] [INFO] Executing action 91/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:06:52]] [SUCCESS] Screenshot refreshed
[[18:06:52]] [INFO] Refreshing screenshot...
[[18:06:52]] [INFO] XuLgjNG74w=pass
[[18:06:49]] [SUCCESS] Screenshot refreshed successfully
[[18:06:49]] [SUCCESS] Screenshot refreshed successfully
[[18:06:49]] [INFO] XuLgjNG74w=running
[[18:06:49]] [INFO] Executing action 90/590: iOS Function: alert_accept
[[18:06:49]] [SUCCESS] Screenshot refreshed
[[18:06:49]] [INFO] Refreshing screenshot...
[[18:06:49]] [INFO] qA1ap4n1m4=pass
[[18:06:41]] [SUCCESS] Screenshot refreshed successfully
[[18:06:41]] [SUCCESS] Screenshot refreshed successfully
[[18:06:41]] [INFO] qA1ap4n1m4=running
[[18:06:41]] [INFO] Executing action 89/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:06:40]] [SUCCESS] Screenshot refreshed
[[18:06:40]] [INFO] Refreshing screenshot...
[[18:06:40]] [INFO] JXFxYCr98V=pass
[[18:06:27]] [SUCCESS] Screenshot refreshed successfully
[[18:06:27]] [SUCCESS] Screenshot refreshed successfully
[[18:06:26]] [INFO] JXFxYCr98V=running
[[18:06:26]] [INFO] Executing action 88/590: Restart app: env[appid]
[[18:06:26]] [SUCCESS] Screenshot refreshed
[[18:06:26]] [INFO] Refreshing screenshot...
[[18:06:26]] [SUCCESS] Screenshot refreshed
[[18:06:26]] [INFO] Refreshing screenshot...
[[18:06:23]] [SUCCESS] Screenshot refreshed successfully
[[18:06:23]] [SUCCESS] Screenshot refreshed successfully
[[18:06:22]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:06:22]] [SUCCESS] Screenshot refreshed
[[18:06:22]] [INFO] Refreshing screenshot...
[[18:06:09]] [SUCCESS] Screenshot refreshed successfully
[[18:06:09]] [SUCCESS] Screenshot refreshed successfully
[[18:06:09]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:06:09]] [SUCCESS] Screenshot refreshed
[[18:06:09]] [INFO] Refreshing screenshot...
[[18:06:05]] [SUCCESS] Screenshot refreshed successfully
[[18:06:05]] [SUCCESS] Screenshot refreshed successfully
[[18:06:05]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:06:04]] [SUCCESS] Screenshot refreshed
[[18:06:04]] [INFO] Refreshing screenshot...
[[18:06:01]] [SUCCESS] Screenshot refreshed successfully
[[18:06:01]] [SUCCESS] Screenshot refreshed successfully
[[18:06:00]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:06:00]] [SUCCESS] Screenshot refreshed
[[18:06:00]] [INFO] Refreshing screenshot...
[[18:05:53]] [SUCCESS] Screenshot refreshed successfully
[[18:05:53]] [SUCCESS] Screenshot refreshed successfully
[[18:05:53]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:05:52]] [SUCCESS] Screenshot refreshed
[[18:05:52]] [INFO] Refreshing screenshot...
[[18:05:46]] [SUCCESS] Screenshot refreshed successfully
[[18:05:46]] [SUCCESS] Screenshot refreshed successfully
[[18:05:45]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:05:45]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:05:45]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:05:45]] [INFO] hbIlJIWlVN=running
[[18:05:45]] [INFO] Executing action 87/590: cleanupSteps action
[[18:05:45]] [SUCCESS] Screenshot refreshed
[[18:05:45]] [INFO] Refreshing screenshot...
[[18:05:44]] [SUCCESS] Screenshot refreshed
[[18:05:44]] [INFO] Refreshing screenshot...
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:05:39]] [SUCCESS] Screenshot refreshed
[[18:05:39]] [INFO] Refreshing screenshot...
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:35]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:05:34]] [SUCCESS] Screenshot refreshed
[[18:05:34]] [INFO] Refreshing screenshot...
[[18:05:22]] [SUCCESS] Screenshot refreshed successfully
[[18:05:22]] [SUCCESS] Screenshot refreshed successfully
[[18:05:22]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:05:22]] [SUCCESS] Screenshot refreshed
[[18:05:22]] [INFO] Refreshing screenshot...
[[18:05:17]] [SUCCESS] Screenshot refreshed successfully
[[18:05:17]] [SUCCESS] Screenshot refreshed successfully
[[18:05:17]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:05:16]] [SUCCESS] Screenshot refreshed
[[18:05:16]] [INFO] Refreshing screenshot...
[[18:05:11]] [SUCCESS] Screenshot refreshed successfully
[[18:05:11]] [SUCCESS] Screenshot refreshed successfully
[[18:05:11]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[18:05:11]] [SUCCESS] Screenshot refreshed
[[18:05:11]] [INFO] Refreshing screenshot...
[[18:05:01]] [SUCCESS] Screenshot refreshed successfully
[[18:05:01]] [SUCCESS] Screenshot refreshed successfully
[[18:05:00]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[18:05:00]] [SUCCESS] Screenshot refreshed
[[18:05:00]] [INFO] Refreshing screenshot...
[[18:04:56]] [SUCCESS] Screenshot refreshed successfully
[[18:04:56]] [SUCCESS] Screenshot refreshed successfully
[[18:04:55]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[18:04:55]] [SUCCESS] Screenshot refreshed
[[18:04:55]] [INFO] Refreshing screenshot...
[[18:04:50]] [SUCCESS] Screenshot refreshed successfully
[[18:04:50]] [SUCCESS] Screenshot refreshed successfully
[[18:04:50]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:04:49]] [SUCCESS] Screenshot refreshed
[[18:04:49]] [INFO] Refreshing screenshot...
[[18:04:42]] [SUCCESS] Screenshot refreshed successfully
[[18:04:42]] [SUCCESS] Screenshot refreshed successfully
[[18:04:42]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[18:04:41]] [SUCCESS] Screenshot refreshed
[[18:04:41]] [INFO] Refreshing screenshot...
[[18:04:36]] [SUCCESS] Screenshot refreshed successfully
[[18:04:36]] [SUCCESS] Screenshot refreshed successfully
[[18:04:36]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[18:04:35]] [SUCCESS] Screenshot refreshed
[[18:04:35]] [INFO] Refreshing screenshot...
[[18:04:31]] [SUCCESS] Screenshot refreshed successfully
[[18:04:31]] [SUCCESS] Screenshot refreshed successfully
[[18:04:31]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[18:04:30]] [SUCCESS] Screenshot refreshed
[[18:04:30]] [INFO] Refreshing screenshot...
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:26]] [INFO] Executing Multi Step action step 25/36: textClear action
[[18:04:25]] [SUCCESS] Screenshot refreshed
[[18:04:25]] [INFO] Refreshing screenshot...
[[18:04:21]] [SUCCESS] Screenshot refreshed successfully
[[18:04:21]] [SUCCESS] Screenshot refreshed successfully
[[18:04:21]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[18:04:20]] [SUCCESS] Screenshot refreshed
[[18:04:20]] [INFO] Refreshing screenshot...
[[18:04:15]] [SUCCESS] Screenshot refreshed successfully
[[18:04:15]] [SUCCESS] Screenshot refreshed successfully
[[18:04:15]] [INFO] Executing Multi Step action step 23/36: textClear action
[[18:04:15]] [SUCCESS] Screenshot refreshed
[[18:04:15]] [INFO] Refreshing screenshot...
[[18:04:11]] [SUCCESS] Screenshot refreshed successfully
[[18:04:11]] [SUCCESS] Screenshot refreshed successfully
[[18:04:11]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:04:10]] [SUCCESS] Screenshot refreshed
[[18:04:10]] [INFO] Refreshing screenshot...
[[18:04:06]] [SUCCESS] Screenshot refreshed successfully
[[18:04:06]] [SUCCESS] Screenshot refreshed successfully
[[18:04:06]] [INFO] Executing Multi Step action step 21/36: textClear action
[[18:04:05]] [SUCCESS] Screenshot refreshed
[[18:04:05]] [INFO] Refreshing screenshot...
[[18:04:01]] [SUCCESS] Screenshot refreshed successfully
[[18:04:01]] [SUCCESS] Screenshot refreshed successfully
[[18:04:01]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[18:04:00]] [SUCCESS] Screenshot refreshed
[[18:04:00]] [INFO] Refreshing screenshot...
[[18:03:57]] [SUCCESS] Screenshot refreshed successfully
[[18:03:57]] [SUCCESS] Screenshot refreshed successfully
[[18:03:56]] [INFO] Executing Multi Step action step 19/36: textClear action
[[18:03:55]] [SUCCESS] Screenshot refreshed
[[18:03:55]] [INFO] Refreshing screenshot...
[[18:03:53]] [SUCCESS] Screenshot refreshed successfully
[[18:03:53]] [SUCCESS] Screenshot refreshed successfully
[[18:03:51]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[18:03:50]] [SUCCESS] Screenshot refreshed
[[18:03:50]] [INFO] Refreshing screenshot...
[[18:03:46]] [SUCCESS] Screenshot refreshed successfully
[[18:03:46]] [SUCCESS] Screenshot refreshed successfully
[[18:03:46]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[18:03:46]] [SUCCESS] Screenshot refreshed
[[18:03:46]] [INFO] Refreshing screenshot...
[[18:03:35]] [SUCCESS] Screenshot refreshed successfully
[[18:03:35]] [SUCCESS] Screenshot refreshed successfully
[[18:03:35]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[18:03:34]] [SUCCESS] Screenshot refreshed
[[18:03:34]] [INFO] Refreshing screenshot...
[[18:03:30]] [SUCCESS] Screenshot refreshed successfully
[[18:03:30]] [SUCCESS] Screenshot refreshed successfully
[[18:03:30]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:03:30]] [SUCCESS] Screenshot refreshed
[[18:03:30]] [INFO] Refreshing screenshot...
[[18:03:28]] [SUCCESS] Screenshot refreshed successfully
[[18:03:28]] [SUCCESS] Screenshot refreshed successfully
[[18:03:26]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:03:25]] [SUCCESS] Screenshot refreshed
[[18:03:25]] [INFO] Refreshing screenshot...
[[18:03:13]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:03:13]] [SUCCESS] Screenshot refreshed successfully
[[18:03:13]] [SUCCESS] Screenshot refreshed successfully
[[18:03:12]] [SUCCESS] Screenshot refreshed
[[18:03:12]] [INFO] Refreshing screenshot...
[[18:03:09]] [SUCCESS] Screenshot refreshed successfully
[[18:03:09]] [SUCCESS] Screenshot refreshed successfully
[[18:03:08]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:03:08]] [SUCCESS] Screenshot refreshed
[[18:03:08]] [INFO] Refreshing screenshot...
[[18:03:01]] [SUCCESS] Screenshot refreshed successfully
[[18:03:01]] [SUCCESS] Screenshot refreshed successfully
[[18:03:00]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[18:03:00]] [SUCCESS] Screenshot refreshed
[[18:03:00]] [INFO] Refreshing screenshot...
[[18:02:55]] [SUCCESS] Screenshot refreshed successfully
[[18:02:55]] [SUCCESS] Screenshot refreshed successfully
[[18:02:55]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:02:54]] [SUCCESS] Screenshot refreshed
[[18:02:54]] [INFO] Refreshing screenshot...
[[18:02:49]] [SUCCESS] Screenshot refreshed successfully
[[18:02:49]] [SUCCESS] Screenshot refreshed successfully
[[18:02:49]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[18:02:49]] [SUCCESS] Screenshot refreshed
[[18:02:49]] [INFO] Refreshing screenshot...
[[18:02:43]] [SUCCESS] Screenshot refreshed successfully
[[18:02:43]] [SUCCESS] Screenshot refreshed successfully
[[18:02:42]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[18:02:42]] [SUCCESS] Screenshot refreshed
[[18:02:42]] [INFO] Refreshing screenshot...
[[18:02:30]] [SUCCESS] Screenshot refreshed successfully
[[18:02:30]] [SUCCESS] Screenshot refreshed successfully
[[18:02:30]] [INFO] Executing Multi Step action step 7/36: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]"
[[18:02:29]] [SUCCESS] Screenshot refreshed
[[18:02:29]] [INFO] Refreshing screenshot...
[[18:02:17]] [SUCCESS] Screenshot refreshed successfully
[[18:02:17]] [SUCCESS] Screenshot refreshed successfully
[[18:02:16]] [INFO] Executing Multi Step action step 6/36: Tap if locator exists: accessibility_id="btnUpdate"
[[18:02:16]] [SUCCESS] Screenshot refreshed
[[18:02:16]] [INFO] Refreshing screenshot...
[[18:02:04]] [SUCCESS] Screenshot refreshed successfully
[[18:02:04]] [SUCCESS] Screenshot refreshed successfully
[[18:02:03]] [INFO] Executing Multi Step action step 5/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[18:02:03]] [SUCCESS] Screenshot refreshed
[[18:02:03]] [INFO] Refreshing screenshot...
[[18:01:58]] [SUCCESS] Screenshot refreshed successfully
[[18:01:58]] [SUCCESS] Screenshot refreshed successfully
[[18:01:57]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[18:01:57]] [SUCCESS] Screenshot refreshed
[[18:01:57]] [INFO] Refreshing screenshot...
[[18:01:52]] [SUCCESS] Screenshot refreshed successfully
[[18:01:52]] [SUCCESS] Screenshot refreshed successfully
[[18:01:51]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[18:01:51]] [SUCCESS] Screenshot refreshed
[[18:01:51]] [INFO] Refreshing screenshot...
[[18:01:46]] [SUCCESS] Screenshot refreshed successfully
[[18:01:46]] [SUCCESS] Screenshot refreshed successfully
[[18:01:46]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[18:01:45]] [SUCCESS] Screenshot refreshed
[[18:01:45]] [INFO] Refreshing screenshot...
[[18:01:38]] [SUCCESS] Screenshot refreshed successfully
[[18:01:38]] [SUCCESS] Screenshot refreshed successfully
[[18:01:38]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[18:01:38]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[18:01:38]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[18:01:38]] [INFO] 8ZYdW2lMKv=running
[[18:01:38]] [INFO] Executing action 86/590: Execute Test Case: Delivery  Buy (36 steps)
[[18:01:37]] [SUCCESS] Screenshot refreshed
[[18:01:37]] [INFO] Refreshing screenshot...
[[18:01:37]] [INFO] cKNu2QoRC1=pass
[[18:01:33]] [SUCCESS] Screenshot refreshed successfully
[[18:01:33]] [SUCCESS] Screenshot refreshed successfully
[[18:01:33]] [INFO] cKNu2QoRC1=running
[[18:01:33]] [INFO] Executing action 85/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:01:32]] [SUCCESS] Screenshot refreshed
[[18:01:32]] [INFO] Refreshing screenshot...
[[18:01:32]] [INFO] OyUowAaBzD=pass
[[18:01:28]] [SUCCESS] Screenshot refreshed successfully
[[18:01:28]] [SUCCESS] Screenshot refreshed successfully
[[18:01:28]] [INFO] OyUowAaBzD=running
[[18:01:28]] [INFO] Executing action 84/590: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:01:27]] [SUCCESS] Screenshot refreshed
[[18:01:27]] [INFO] Refreshing screenshot...
[[18:01:27]] [INFO] Ob26qqcA0p=pass
[[18:01:20]] [SUCCESS] Screenshot refreshed successfully
[[18:01:20]] [SUCCESS] Screenshot refreshed successfully
[[18:01:20]] [INFO] Ob26qqcA0p=running
[[18:01:20]] [INFO] Executing action 83/590: Swipe from (50%, 70%) to (50%, 30%)
[[18:01:19]] [SUCCESS] Screenshot refreshed
[[18:01:19]] [INFO] Refreshing screenshot...
[[18:01:19]] [INFO] k3mu9Mt7Ec=pass
[[18:01:15]] [SUCCESS] Screenshot refreshed successfully
[[18:01:15]] [SUCCESS] Screenshot refreshed successfully
[[18:01:15]] [INFO] k3mu9Mt7Ec=running
[[18:01:15]] [INFO] Executing action 82/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:01:14]] [SUCCESS] Screenshot refreshed
[[18:01:14]] [INFO] Refreshing screenshot...
[[18:01:14]] [INFO] 8umPSX0vrr=pass
[[18:01:10]] [INFO] 8umPSX0vrr=running
[[18:01:10]] [INFO] Executing action 81/590: Tap on image: banner-close-updated.png
[[18:01:10]] [SUCCESS] Screenshot refreshed successfully
[[18:01:10]] [SUCCESS] Screenshot refreshed successfully
[[18:01:10]] [SUCCESS] Screenshot refreshed
[[18:01:10]] [INFO] Refreshing screenshot...
[[18:01:10]] [INFO] pr9o8Zsm5p=pass
[[18:01:06]] [SUCCESS] Screenshot refreshed successfully
[[18:01:06]] [SUCCESS] Screenshot refreshed successfully
[[18:01:05]] [INFO] pr9o8Zsm5p=running
[[18:01:05]] [INFO] Executing action 80/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:01:05]] [SUCCESS] Screenshot refreshed
[[18:01:05]] [INFO] Refreshing screenshot...
[[18:01:05]] [INFO] XCynRs6gJ3=pass
[[18:00:57]] [SUCCESS] Screenshot refreshed successfully
[[18:00:57]] [SUCCESS] Screenshot refreshed successfully
[[18:00:57]] [INFO] XCynRs6gJ3=running
[[18:00:57]] [INFO] Executing action 79/590: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[18:00:56]] [SUCCESS] Screenshot refreshed
[[18:00:56]] [INFO] Refreshing screenshot...
[[18:00:56]] [INFO] UnxZdeLmYu=pass
[[18:00:45]] [SUCCESS] Screenshot refreshed successfully
[[18:00:45]] [SUCCESS] Screenshot refreshed successfully
[[18:00:45]] [INFO] UnxZdeLmYu=running
[[18:00:45]] [INFO] Executing action 78/590: Wait for 10 ms
[[18:00:44]] [SUCCESS] Screenshot refreshed
[[18:00:44]] [INFO] Refreshing screenshot...
[[18:00:44]] [INFO] qjj0i3rcUh=pass
[[18:00:40]] [SUCCESS] Screenshot refreshed successfully
[[18:00:40]] [SUCCESS] Screenshot refreshed successfully
[[18:00:40]] [INFO] qjj0i3rcUh=running
[[18:00:40]] [INFO] Executing action 77/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:00:39]] [SUCCESS] Screenshot refreshed
[[18:00:39]] [INFO] Refreshing screenshot...
[[18:00:39]] [INFO] 42Jm6o7r1t=pass
[[18:00:27]] [INFO] 42Jm6o7r1t=running
[[18:00:27]] [INFO] Executing action 76/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:00:27]] [SUCCESS] Screenshot refreshed successfully
[[18:00:27]] [SUCCESS] Screenshot refreshed successfully
[[18:00:27]] [SUCCESS] Screenshot refreshed
[[18:00:27]] [INFO] Refreshing screenshot...
[[18:00:27]] [INFO] lWIRxRm6HE=pass
[[18:00:23]] [SUCCESS] Screenshot refreshed successfully
[[18:00:23]] [SUCCESS] Screenshot refreshed successfully
[[18:00:23]] [INFO] lWIRxRm6HE=running
[[18:00:23]] [INFO] Executing action 75/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:00:22]] [SUCCESS] Screenshot refreshed
[[18:00:22]] [INFO] Refreshing screenshot...
[[18:00:22]] [INFO] Q0fomJIDoQ=pass
[[18:00:17]] [SUCCESS] Screenshot refreshed successfully
[[18:00:17]] [SUCCESS] Screenshot refreshed successfully
[[18:00:17]] [INFO] Q0fomJIDoQ=running
[[18:00:17]] [INFO] Executing action 74/590: Tap on image: banner-close-updated.png
[[18:00:16]] [SUCCESS] Screenshot refreshed
[[18:00:16]] [INFO] Refreshing screenshot...
[[18:00:16]] [INFO] 7SpDO20tS2=pass
[[18:00:05]] [SUCCESS] Screenshot refreshed successfully
[[18:00:05]] [SUCCESS] Screenshot refreshed successfully
[[18:00:05]] [INFO] 7SpDO20tS2=running
[[18:00:05]] [INFO] Executing action 73/590: Wait for 10 ms
[[18:00:04]] [SUCCESS] Screenshot refreshed
[[18:00:04]] [INFO] Refreshing screenshot...
[[18:00:04]] [INFO] FKZs2qCWoU=pass
[[17:59:59]] [SUCCESS] Screenshot refreshed successfully
[[17:59:59]] [SUCCESS] Screenshot refreshed successfully
[[17:59:59]] [INFO] FKZs2qCWoU=running
[[17:59:59]] [INFO] Executing action 72/590: Tap on Text: "Tarneit"
[[17:59:59]] [SUCCESS] Screenshot refreshed
[[17:59:59]] [INFO] Refreshing screenshot...
[[17:59:59]] [INFO] Qbg9bipTGs=pass
[[17:59:54]] [SUCCESS] Screenshot refreshed successfully
[[17:59:54]] [SUCCESS] Screenshot refreshed successfully
[[17:59:54]] [INFO] Qbg9bipTGs=running
[[17:59:54]] [INFO] Executing action 71/590: Swipe from (50%, 70%) to (50%, 30%)
[[17:59:54]] [SUCCESS] Screenshot refreshed
[[17:59:54]] [INFO] Refreshing screenshot...
[[17:59:54]] [INFO] qjj0i3rcUh=pass
[[17:59:49]] [SUCCESS] Screenshot refreshed successfully
[[17:59:49]] [SUCCESS] Screenshot refreshed successfully
[[17:59:49]] [INFO] qjj0i3rcUh=running
[[17:59:49]] [INFO] Executing action 70/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:59:48]] [SUCCESS] Screenshot refreshed
[[17:59:48]] [INFO] Refreshing screenshot...
[[17:59:48]] [INFO] uM5FOSrU5U=pass
[[17:59:45]] [SUCCESS] Screenshot refreshed successfully
[[17:59:45]] [SUCCESS] Screenshot refreshed successfully
[[17:59:45]] [INFO] uM5FOSrU5U=running
[[17:59:45]] [INFO] Executing action 69/590: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[17:59:44]] [SUCCESS] Screenshot refreshed
[[17:59:44]] [INFO] Refreshing screenshot...
[[17:59:44]] [INFO] QB2bKb0SsP=pass
[[17:59:32]] [SUCCESS] Screenshot refreshed successfully
[[17:59:32]] [SUCCESS] Screenshot refreshed successfully
[[17:59:32]] [INFO] QB2bKb0SsP=running
[[17:59:32]] [INFO] Executing action 68/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:59:32]] [SUCCESS] Screenshot refreshed
[[17:59:32]] [INFO] Refreshing screenshot...
[[17:59:32]] [INFO] F1olhgKhUt=pass
[[17:59:28]] [SUCCESS] Screenshot refreshed successfully
[[17:59:28]] [SUCCESS] Screenshot refreshed successfully
[[17:59:27]] [INFO] F1olhgKhUt=running
[[17:59:27]] [INFO] Executing action 67/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:59:27]] [SUCCESS] Screenshot refreshed
[[17:59:27]] [INFO] Refreshing screenshot...
[[17:59:27]] [INFO] jY0oPjKbuS=pass
[[17:59:23]] [SUCCESS] Screenshot refreshed successfully
[[17:59:23]] [SUCCESS] Screenshot refreshed successfully
[[17:59:23]] [INFO] jY0oPjKbuS=running
[[17:59:23]] [INFO] Executing action 66/590: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[17:59:22]] [SUCCESS] Screenshot refreshed
[[17:59:22]] [INFO] Refreshing screenshot...
[[17:59:22]] [INFO] FnrbyHq7bU=pass
[[17:59:15]] [SUCCESS] Screenshot refreshed successfully
[[17:59:15]] [SUCCESS] Screenshot refreshed successfully
[[17:59:14]] [INFO] FnrbyHq7bU=running
[[17:59:14]] [INFO] Executing action 65/590: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[17:59:14]] [SUCCESS] Screenshot refreshed
[[17:59:14]] [INFO] Refreshing screenshot...
[[17:59:14]] [INFO] nAB6Q8LAdv=pass
[[17:59:10]] [SUCCESS] Screenshot refreshed successfully
[[17:59:10]] [SUCCESS] Screenshot refreshed successfully
[[17:59:10]] [INFO] nAB6Q8LAdv=running
[[17:59:10]] [INFO] Executing action 64/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:59:09]] [SUCCESS] Screenshot refreshed
[[17:59:09]] [INFO] Refreshing screenshot...
[[17:59:09]] [INFO] sc2KH9bG6H=pass
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [INFO] sc2KH9bG6H=running
[[17:59:05]] [INFO] Executing action 63/590: iOS Function: text - Text: "Uno card"
[[17:59:05]] [SUCCESS] Screenshot refreshed
[[17:59:05]] [INFO] Refreshing screenshot...
[[17:59:05]] [INFO] ZBXCQNlT8z=pass
[[17:58:59]] [SUCCESS] Screenshot refreshed successfully
[[17:58:59]] [SUCCESS] Screenshot refreshed successfully
[[17:58:59]] [INFO] ZBXCQNlT8z=running
[[17:58:59]] [INFO] Executing action 62/590: Tap on Text: "Find"
[[17:58:58]] [SUCCESS] Screenshot refreshed
[[17:58:58]] [INFO] Refreshing screenshot...
[[17:58:58]] [INFO] HYl6Z7Gvqz=pass
[[17:58:54]] [SUCCESS] Screenshot refreshed successfully
[[17:58:54]] [SUCCESS] Screenshot refreshed successfully
[[17:58:52]] [INFO] HYl6Z7Gvqz=running
[[17:58:52]] [INFO] Executing action 61/590: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:58:52]] [SUCCESS] Screenshot refreshed
[[17:58:52]] [INFO] Refreshing screenshot...
[[17:58:52]] [SUCCESS] Screenshot refreshed
[[17:58:52]] [INFO] Refreshing screenshot...
[[17:58:46]] [SUCCESS] Screenshot refreshed successfully
[[17:58:46]] [SUCCESS] Screenshot refreshed successfully
[[17:58:46]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[17:58:46]] [SUCCESS] Screenshot refreshed
[[17:58:46]] [INFO] Refreshing screenshot...
[[17:58:41]] [SUCCESS] Screenshot refreshed successfully
[[17:58:41]] [SUCCESS] Screenshot refreshed successfully
[[17:58:41]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:58:41]] [SUCCESS] Screenshot refreshed
[[17:58:41]] [INFO] Refreshing screenshot...
[[17:58:36]] [SUCCESS] Screenshot refreshed successfully
[[17:58:36]] [SUCCESS] Screenshot refreshed successfully
[[17:58:36]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[17:58:35]] [SUCCESS] Screenshot refreshed
[[17:58:35]] [INFO] Refreshing screenshot...
[[17:58:31]] [SUCCESS] Screenshot refreshed successfully
[[17:58:31]] [SUCCESS] Screenshot refreshed successfully
[[17:58:31]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:58:31]] [SUCCESS] Screenshot refreshed
[[17:58:31]] [INFO] Refreshing screenshot...
[[17:58:25]] [SUCCESS] Screenshot refreshed successfully
[[17:58:25]] [SUCCESS] Screenshot refreshed successfully
[[17:58:25]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:58:25]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[17:58:25]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[17:58:25]] [INFO] El6k4IPZly=running
[[17:58:25]] [INFO] Executing action 60/590: Execute Test Case: Kmart-Signin (8 steps)
[[17:58:24]] [SUCCESS] Screenshot refreshed
[[17:58:24]] [INFO] Refreshing screenshot...
[[17:58:24]] [INFO] 3caMBvQX7k=pass
[[17:58:20]] [SUCCESS] Screenshot refreshed successfully
[[17:58:20]] [SUCCESS] Screenshot refreshed successfully
[[17:58:20]] [INFO] 3caMBvQX7k=running
[[17:58:20]] [INFO] Executing action 59/590: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:58:19]] [SUCCESS] Screenshot refreshed
[[17:58:19]] [INFO] Refreshing screenshot...
[[17:58:19]] [INFO] yUJyVO5Wev=pass
[[17:58:17]] [SUCCESS] Screenshot refreshed successfully
[[17:58:17]] [SUCCESS] Screenshot refreshed successfully
[[17:58:16]] [INFO] yUJyVO5Wev=running
[[17:58:16]] [INFO] Executing action 58/590: iOS Function: alert_accept
[[17:58:16]] [SUCCESS] Screenshot refreshed
[[17:58:16]] [INFO] Refreshing screenshot...
[[17:58:16]] [INFO] rkL0oz4kiL=pass
[[17:58:09]] [SUCCESS] Screenshot refreshed successfully
[[17:58:09]] [SUCCESS] Screenshot refreshed successfully
[[17:58:08]] [INFO] rkL0oz4kiL=running
[[17:58:08]] [INFO] Executing action 57/590: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:58:08]] [SUCCESS] Screenshot refreshed
[[17:58:08]] [INFO] Refreshing screenshot...
[[17:58:08]] [INFO] HotUJOd6oB=pass
[[17:57:55]] [SUCCESS] Screenshot refreshed successfully
[[17:57:55]] [SUCCESS] Screenshot refreshed successfully
[[17:57:54]] [INFO] HotUJOd6oB=running
[[17:57:54]] [INFO] Executing action 56/590: Restart app: env[appid]
[[17:57:54]] [SUCCESS] Screenshot refreshed
[[17:57:54]] [INFO] Refreshing screenshot...
[[17:57:53]] [SUCCESS] Screenshot refreshed
[[17:57:53]] [INFO] Refreshing screenshot...
[[17:57:50]] [SUCCESS] Screenshot refreshed successfully
[[17:57:50]] [SUCCESS] Screenshot refreshed successfully
[[17:57:50]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[17:57:49]] [SUCCESS] Screenshot refreshed
[[17:57:49]] [INFO] Refreshing screenshot...
[[17:57:37]] [SUCCESS] Screenshot refreshed successfully
[[17:57:37]] [SUCCESS] Screenshot refreshed successfully
[[17:57:37]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[17:57:36]] [SUCCESS] Screenshot refreshed
[[17:57:36]] [INFO] Refreshing screenshot...
[[17:57:32]] [SUCCESS] Screenshot refreshed successfully
[[17:57:32]] [SUCCESS] Screenshot refreshed successfully
[[17:57:32]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[17:57:32]] [SUCCESS] Screenshot refreshed
[[17:57:32]] [INFO] Refreshing screenshot...
[[17:57:28]] [SUCCESS] Screenshot refreshed successfully
[[17:57:28]] [SUCCESS] Screenshot refreshed successfully
[[17:57:27]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:57:27]] [SUCCESS] Screenshot refreshed
[[17:57:27]] [INFO] Refreshing screenshot...
[[17:57:20]] [SUCCESS] Screenshot refreshed successfully
[[17:57:20]] [SUCCESS] Screenshot refreshed successfully
[[17:57:20]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[17:57:19]] [SUCCESS] Screenshot refreshed
[[17:57:19]] [INFO] Refreshing screenshot...
[[17:57:14]] [SUCCESS] Screenshot refreshed successfully
[[17:57:14]] [SUCCESS] Screenshot refreshed successfully
[[17:57:13]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[17:57:13]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[17:57:13]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[17:57:13]] [INFO] vKo6Ox3YrP=running
[[17:57:13]] [INFO] Executing action 55/590: cleanupSteps action
[[17:57:13]] [SUCCESS] Screenshot refreshed
[[17:57:13]] [INFO] Refreshing screenshot...
[[17:57:13]] [INFO] x4yLCZHaCR=pass
[[17:57:09]] [SUCCESS] Screenshot refreshed successfully
[[17:57:09]] [SUCCESS] Screenshot refreshed successfully
[[17:57:09]] [INFO] x4yLCZHaCR=running
[[17:57:09]] [INFO] Executing action 54/590: Terminate app: env[appid]
[[17:57:09]] [SUCCESS] Screenshot refreshed
[[17:57:09]] [INFO] Refreshing screenshot...
[[17:57:09]] [INFO] 2p13JoJbbA=pass
[[17:57:05]] [SUCCESS] Screenshot refreshed successfully
[[17:57:05]] [SUCCESS] Screenshot refreshed successfully
[[17:57:05]] [INFO] 2p13JoJbbA=running
[[17:57:05]] [INFO] Executing action 53/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:57:04]] [SUCCESS] Screenshot refreshed
[[17:57:04]] [INFO] Refreshing screenshot...
[[17:57:04]] [INFO] 2p13JoJbbA=pass
[[17:57:00]] [SUCCESS] Screenshot refreshed successfully
[[17:57:00]] [SUCCESS] Screenshot refreshed successfully
[[17:57:00]] [INFO] 2p13JoJbbA=running
[[17:57:00]] [INFO] Executing action 52/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:57:00]] [SUCCESS] Screenshot refreshed
[[17:57:00]] [INFO] Refreshing screenshot...
[[17:57:00]] [INFO] nyBidG0kHp=pass
[[17:56:53]] [SUCCESS] Screenshot refreshed successfully
[[17:56:53]] [SUCCESS] Screenshot refreshed successfully
[[17:56:52]] [INFO] nyBidG0kHp=running
[[17:56:52]] [INFO] Executing action 51/590: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[17:56:52]] [SUCCESS] Screenshot refreshed
[[17:56:52]] [INFO] Refreshing screenshot...
[[17:56:52]] [INFO] w7I4F66YKQ=pass
[[17:56:40]] [INFO] w7I4F66YKQ=running
[[17:56:40]] [INFO] Executing action 50/590: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:56:40]] [SUCCESS] Screenshot refreshed successfully
[[17:56:40]] [SUCCESS] Screenshot refreshed successfully
[[17:56:39]] [SUCCESS] Screenshot refreshed
[[17:56:39]] [INFO] Refreshing screenshot...
[[17:56:39]] [INFO] F4NGh9HrLw=pass
[[17:56:34]] [SUCCESS] Screenshot refreshed successfully
[[17:56:34]] [SUCCESS] Screenshot refreshed successfully
[[17:56:34]] [INFO] F4NGh9HrLw=running
[[17:56:34]] [INFO] Executing action 49/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:56:34]] [SUCCESS] Screenshot refreshed
[[17:56:34]] [INFO] Refreshing screenshot...
[[17:56:34]] [INFO] VtMfqK1V9t=pass
[[17:56:14]] [SUCCESS] Screenshot refreshed successfully
[[17:56:14]] [SUCCESS] Screenshot refreshed successfully
[[17:56:14]] [INFO] VtMfqK1V9t=running
[[17:56:14]] [INFO] Executing action 48/590: Tap on element with accessibility_id: Add to bag
[[17:56:14]] [SUCCESS] Screenshot refreshed
[[17:56:14]] [INFO] Refreshing screenshot...
[[17:56:14]] [INFO] NOnuFzXy63=pass
[[17:56:10]] [SUCCESS] Screenshot refreshed successfully
[[17:56:10]] [SUCCESS] Screenshot refreshed successfully
[[17:56:09]] [INFO] NOnuFzXy63=running
[[17:56:09]] [INFO] Executing action 47/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:56:09]] [SUCCESS] Screenshot refreshed
[[17:56:09]] [INFO] Refreshing screenshot...
[[17:56:09]] [INFO] kz9lnCdwoH=pass
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:04]] [INFO] kz9lnCdwoH=running
[[17:56:04]] [INFO] Executing action 46/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:56:04]] [SUCCESS] Screenshot refreshed
[[17:56:04]] [INFO] Refreshing screenshot...
[[17:56:04]] [INFO] kz9lnCdwoH=pass
[[17:56:00]] [SUCCESS] Screenshot refreshed successfully
[[17:56:00]] [SUCCESS] Screenshot refreshed successfully
[[17:56:00]] [INFO] kz9lnCdwoH=running
[[17:56:00]] [INFO] Executing action 45/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:55:59]] [SUCCESS] Screenshot refreshed
[[17:55:59]] [INFO] Refreshing screenshot...
[[17:55:59]] [INFO] qIF9CVPc56=pass
[[17:55:54]] [SUCCESS] Screenshot refreshed successfully
[[17:55:54]] [SUCCESS] Screenshot refreshed successfully
[[17:55:54]] [INFO] qIF9CVPc56=running
[[17:55:54]] [INFO] Executing action 44/590: iOS Function: text - Text: "mat"
[[17:55:54]] [SUCCESS] Screenshot refreshed
[[17:55:54]] [INFO] Refreshing screenshot...
[[17:55:54]] [INFO] yEga5MkcRe=pass
[[17:55:49]] [SUCCESS] Screenshot refreshed successfully
[[17:55:49]] [SUCCESS] Screenshot refreshed successfully
[[17:55:49]] [INFO] yEga5MkcRe=running
[[17:55:49]] [INFO] Executing action 43/590: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:55:48]] [SUCCESS] Screenshot refreshed
[[17:55:48]] [INFO] Refreshing screenshot...
[[17:55:48]] [INFO] F4NGh9HrLw=pass
[[17:55:45]] [SUCCESS] Screenshot refreshed successfully
[[17:55:45]] [SUCCESS] Screenshot refreshed successfully
[[17:55:44]] [INFO] F4NGh9HrLw=running
[[17:55:44]] [INFO] Executing action 42/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:55:44]] [SUCCESS] Screenshot refreshed
[[17:55:44]] [INFO] Refreshing screenshot...
[[17:55:44]] [INFO] kz9lnCdwoH=pass
[[17:55:39]] [SUCCESS] Screenshot refreshed successfully
[[17:55:39]] [SUCCESS] Screenshot refreshed successfully
[[17:55:39]] [INFO] kz9lnCdwoH=running
[[17:55:39]] [INFO] Executing action 41/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:55:39]] [SUCCESS] Screenshot refreshed
[[17:55:39]] [INFO] Refreshing screenshot...
[[17:55:39]] [INFO] kz9lnCdwoH=pass
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:35]] [INFO] kz9lnCdwoH=running
[[17:55:35]] [INFO] Executing action 40/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:55:34]] [SUCCESS] Screenshot refreshed
[[17:55:34]] [INFO] Refreshing screenshot...
[[17:55:34]] [INFO] JRheDTvpJf=pass
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [INFO] JRheDTvpJf=running
[[17:55:30]] [INFO] Executing action 39/590: iOS Function: text - Text: "Kid toy"
[[17:55:29]] [SUCCESS] Screenshot refreshed
[[17:55:29]] [INFO] Refreshing screenshot...
[[17:55:29]] [INFO] yEga5MkcRe=pass
[[17:55:25]] [SUCCESS] Screenshot refreshed successfully
[[17:55:25]] [SUCCESS] Screenshot refreshed successfully
[[17:55:25]] [INFO] yEga5MkcRe=running
[[17:55:25]] [INFO] Executing action 38/590: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:55:24]] [SUCCESS] Screenshot refreshed
[[17:55:24]] [INFO] Refreshing screenshot...
[[17:55:24]] [INFO] F4NGh9HrLw=pass
[[17:55:20]] [SUCCESS] Screenshot refreshed successfully
[[17:55:20]] [SUCCESS] Screenshot refreshed successfully
[[17:55:20]] [INFO] F4NGh9HrLw=running
[[17:55:20]] [INFO] Executing action 37/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:55:19]] [SUCCESS] Screenshot refreshed
[[17:55:19]] [INFO] Refreshing screenshot...
[[17:55:19]] [INFO] XPEr3w6Zof=pass
[[17:55:14]] [SUCCESS] Screenshot refreshed successfully
[[17:55:14]] [SUCCESS] Screenshot refreshed successfully
[[17:55:14]] [INFO] XPEr3w6Zof=running
[[17:55:14]] [INFO] Executing action 36/590: Restart app: env[appid]
[[17:55:13]] [SUCCESS] Screenshot refreshed
[[17:55:13]] [INFO] Refreshing screenshot...
[[17:55:13]] [INFO] PiQRBWBe3E=pass
[[17:55:09]] [SUCCESS] Screenshot refreshed successfully
[[17:55:09]] [SUCCESS] Screenshot refreshed successfully
[[17:55:09]] [INFO] PiQRBWBe3E=running
[[17:55:09]] [INFO] Executing action 35/590: Tap on image: env[device-back-img]
[[17:55:09]] [SUCCESS] Screenshot refreshed
[[17:55:09]] [INFO] Refreshing screenshot...
[[17:55:09]] [INFO] GWoppouz1l=pass
[[17:55:06]] [SUCCESS] Screenshot refreshed successfully
[[17:55:06]] [SUCCESS] Screenshot refreshed successfully
[[17:55:06]] [INFO] GWoppouz1l=running
[[17:55:06]] [INFO] Executing action 34/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[17:55:05]] [SUCCESS] Screenshot refreshed
[[17:55:05]] [INFO] Refreshing screenshot...
[[17:55:05]] [INFO] B6GDXWAmWp=pass
[[17:54:45]] [SUCCESS] Screenshot refreshed successfully
[[17:54:45]] [SUCCESS] Screenshot refreshed successfully
[[17:54:45]] [INFO] B6GDXWAmWp=running
[[17:54:45]] [INFO] Executing action 33/590: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[17:54:44]] [SUCCESS] Screenshot refreshed
[[17:54:44]] [INFO] Refreshing screenshot...
[[17:54:44]] [INFO] mtYqeDttRc=pass
[[17:54:40]] [SUCCESS] Screenshot refreshed successfully
[[17:54:40]] [SUCCESS] Screenshot refreshed successfully
[[17:54:40]] [INFO] mtYqeDttRc=running
[[17:54:40]] [INFO] Executing action 32/590: Tap on image: env[paypal-close-img]
[[17:54:39]] [SUCCESS] Screenshot refreshed
[[17:54:39]] [INFO] Refreshing screenshot...
[[17:54:39]] [INFO] q6cKxgMAIn=pass
[[17:54:32]] [SUCCESS] Screenshot refreshed successfully
[[17:54:32]] [SUCCESS] Screenshot refreshed successfully
[[17:54:32]] [INFO] q6cKxgMAIn=running
[[17:54:32]] [INFO] Executing action 31/590: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[17:54:31]] [SUCCESS] Screenshot refreshed
[[17:54:31]] [INFO] Refreshing screenshot...
[[17:54:31]] [INFO] KRQDBv2D3A=pass
[[17:54:27]] [SUCCESS] Screenshot refreshed successfully
[[17:54:27]] [SUCCESS] Screenshot refreshed successfully
[[17:54:27]] [INFO] KRQDBv2D3A=running
[[17:54:27]] [INFO] Executing action 30/590: Tap on image: env[device-back-img]
[[17:54:26]] [SUCCESS] Screenshot refreshed
[[17:54:26]] [INFO] Refreshing screenshot...
[[17:54:26]] [INFO] P4b2BITpCf=pass
[[17:54:23]] [SUCCESS] Screenshot refreshed successfully
[[17:54:23]] [SUCCESS] Screenshot refreshed successfully
[[17:54:23]] [INFO] P4b2BITpCf=running
[[17:54:23]] [INFO] Executing action 29/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[17:54:23]] [SUCCESS] Screenshot refreshed
[[17:54:23]] [INFO] Refreshing screenshot...
[[17:54:23]] [INFO] inrxgdWzXr=pass
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:15]] [INFO] inrxgdWzXr=running
[[17:54:15]] [INFO] Executing action 28/590: Tap on element with accessibility_id: Learn more about Zip
[[17:54:15]] [SUCCESS] Screenshot refreshed
[[17:54:15]] [INFO] Refreshing screenshot...
[[17:54:15]] [INFO] Et3kvnFdxh=pass
[[17:54:11]] [SUCCESS] Screenshot refreshed successfully
[[17:54:11]] [SUCCESS] Screenshot refreshed successfully
[[17:54:11]] [INFO] Et3kvnFdxh=running
[[17:54:11]] [INFO] Executing action 27/590: Tap on image: env[device-back-img]
[[17:54:11]] [INFO] Skipping disabled action 26/590: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[17:54:10]] [SUCCESS] Screenshot refreshed
[[17:54:10]] [INFO] Refreshing screenshot...
[[17:54:10]] [INFO] pk2DLZFBmx=pass
[[17:54:03]] [SUCCESS] Screenshot refreshed successfully
[[17:54:03]] [SUCCESS] Screenshot refreshed successfully
[[17:54:03]] [INFO] pk2DLZFBmx=running
[[17:54:03]] [INFO] Executing action 25/590: Tap on element with accessibility_id: Learn more about AfterPay
[[17:54:02]] [SUCCESS] Screenshot refreshed
[[17:54:02]] [INFO] Refreshing screenshot...
[[17:54:02]] [INFO] ShJSdXvmVL=pass
[[17:53:54]] [SUCCESS] Screenshot refreshed successfully
[[17:53:54]] [SUCCESS] Screenshot refreshed successfully
[[17:53:54]] [INFO] ShJSdXvmVL=running
[[17:53:54]] [INFO] Executing action 24/590: Swipe up till element accessibilityid: "Learn more about AfterPay" is visible
[[17:53:53]] [SUCCESS] Screenshot refreshed
[[17:53:53]] [INFO] Refreshing screenshot...
[[17:53:53]] [INFO] sHQtYzpI4s=pass
[[17:53:49]] [SUCCESS] Screenshot refreshed successfully
[[17:53:49]] [SUCCESS] Screenshot refreshed successfully
[[17:53:48]] [INFO] sHQtYzpI4s=running
[[17:53:48]] [INFO] Executing action 23/590: Tap on image: env[closebtnimage]
[[17:53:48]] [SUCCESS] Screenshot refreshed
[[17:53:48]] [INFO] Refreshing screenshot...
[[17:53:48]] [INFO] 83tV9A4NOn=pass
[[17:53:44]] [SUCCESS] Screenshot refreshed successfully
[[17:53:44]] [SUCCESS] Screenshot refreshed successfully
[[17:53:44]] [INFO] 83tV9A4NOn=running
[[17:53:44]] [INFO] Executing action 22/590: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[17:53:43]] [SUCCESS] Screenshot refreshed
[[17:53:43]] [INFO] Refreshing screenshot...
[[17:53:43]] [INFO] dCqKBG3e7u=pass
[[17:53:39]] [SUCCESS] Screenshot refreshed successfully
[[17:53:39]] [SUCCESS] Screenshot refreshed successfully
[[17:53:38]] [INFO] dCqKBG3e7u=running
[[17:53:38]] [INFO] Executing action 21/590: Tap on image: env[product-share-img]
[[17:53:38]] [SUCCESS] Screenshot refreshed
[[17:53:38]] [INFO] Refreshing screenshot...
[[17:53:38]] [INFO] kAQ1yIIw3h=pass
[[17:53:34]] [SUCCESS] Screenshot refreshed successfully
[[17:53:34]] [SUCCESS] Screenshot refreshed successfully
[[17:53:34]] [INFO] kAQ1yIIw3h=running
[[17:53:34]] [INFO] Executing action 20/590: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[17:53:33]] [SUCCESS] Screenshot refreshed
[[17:53:33]] [INFO] Refreshing screenshot...
[[17:53:33]] [INFO] OmKfD9iBjD=pass
[[17:53:30]] [SUCCESS] Screenshot refreshed successfully
[[17:53:30]] [SUCCESS] Screenshot refreshed successfully
[[17:53:29]] [INFO] OmKfD9iBjD=running
[[17:53:29]] [INFO] Executing action 19/590: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:53:29]] [SUCCESS] Screenshot refreshed
[[17:53:29]] [INFO] Refreshing screenshot...
[[17:53:29]] [INFO] dMl1PH9Dlc=pass
[[17:53:17]] [SUCCESS] Screenshot refreshed successfully
[[17:53:17]] [SUCCESS] Screenshot refreshed successfully
[[17:53:17]] [INFO] dMl1PH9Dlc=running
[[17:53:17]] [INFO] Executing action 18/590: Wait for 10 ms
[[17:53:16]] [SUCCESS] Screenshot refreshed
[[17:53:16]] [INFO] Refreshing screenshot...
[[17:53:16]] [INFO] eHLWiRoqqS=pass
[[17:53:11]] [SUCCESS] Screenshot refreshed successfully
[[17:53:11]] [SUCCESS] Screenshot refreshed successfully
[[17:53:11]] [INFO] eHLWiRoqqS=running
[[17:53:11]] [INFO] Executing action 17/590: Swipe from (50%, 70%) to (50%, 30%)
[[17:53:11]] [SUCCESS] Screenshot refreshed
[[17:53:11]] [INFO] Refreshing screenshot...
[[17:53:11]] [INFO] huUnpMMjVR=pass
[[17:53:07]] [SUCCESS] Screenshot refreshed successfully
[[17:53:07]] [SUCCESS] Screenshot refreshed successfully
[[17:53:06]] [INFO] huUnpMMjVR=running
[[17:53:06]] [INFO] Executing action 16/590: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[17:53:06]] [SUCCESS] Screenshot refreshed
[[17:53:06]] [INFO] Refreshing screenshot...
[[17:53:06]] [INFO] XmAxcBtFI0=pass
[[17:53:03]] [SUCCESS] Screenshot refreshed successfully
[[17:53:03]] [SUCCESS] Screenshot refreshed successfully
[[17:53:02]] [INFO] XmAxcBtFI0=running
[[17:53:02]] [INFO] Executing action 15/590: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[17:53:02]] [SUCCESS] Screenshot refreshed
[[17:53:02]] [INFO] Refreshing screenshot...
[[17:53:02]] [INFO] ktAufkDJnF=pass
[[17:52:58]] [SUCCESS] Screenshot refreshed successfully
[[17:52:58]] [SUCCESS] Screenshot refreshed successfully
[[17:52:58]] [INFO] ktAufkDJnF=running
[[17:52:58]] [INFO] Executing action 14/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[17:52:57]] [SUCCESS] Screenshot refreshed
[[17:52:57]] [INFO] Refreshing screenshot...
[[17:52:57]] [INFO] dMl1PH9Dlc=pass
[[17:52:51]] [SUCCESS] Screenshot refreshed successfully
[[17:52:51]] [SUCCESS] Screenshot refreshed successfully
[[17:52:50]] [INFO] dMl1PH9Dlc=running
[[17:52:50]] [INFO] Executing action 13/590: Wait for 5 ms
[[17:52:50]] [SUCCESS] Screenshot refreshed
[[17:52:50]] [INFO] Refreshing screenshot...
[[17:52:50]] [INFO] a50JhCx0ir=pass
[[17:52:46]] [SUCCESS] Screenshot refreshed successfully
[[17:52:46]] [SUCCESS] Screenshot refreshed successfully
[[17:52:46]] [INFO] a50JhCx0ir=running
[[17:52:46]] [INFO] Executing action 12/590: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[17:52:45]] [SUCCESS] Screenshot refreshed
[[17:52:45]] [INFO] Refreshing screenshot...
[[17:52:45]] [INFO] Y1O1clhMSJ=pass
[[17:52:41]] [SUCCESS] Screenshot refreshed successfully
[[17:52:41]] [SUCCESS] Screenshot refreshed successfully
[[17:52:41]] [INFO] Y1O1clhMSJ=running
[[17:52:41]] [INFO] Executing action 11/590: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[17:52:41]] [SUCCESS] Screenshot refreshed
[[17:52:41]] [INFO] Refreshing screenshot...
[[17:52:41]] [INFO] lYPskZt0Ya=pass
[[17:52:37]] [SUCCESS] Screenshot refreshed successfully
[[17:52:37]] [SUCCESS] Screenshot refreshed successfully
[[17:52:37]] [INFO] lYPskZt0Ya=running
[[17:52:37]] [INFO] Executing action 10/590: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:52:36]] [SUCCESS] Screenshot refreshed
[[17:52:36]] [INFO] Refreshing screenshot...
[[17:52:36]] [INFO] xUbWFa8Ok2=pass
[[17:52:32]] [SUCCESS] Screenshot refreshed successfully
[[17:52:32]] [SUCCESS] Screenshot refreshed successfully
[[17:52:32]] [INFO] xUbWFa8Ok2=running
[[17:52:32]] [INFO] Executing action 9/590: Tap on Text: "Latest"
[[17:52:31]] [SUCCESS] Screenshot refreshed
[[17:52:31]] [INFO] Refreshing screenshot...
[[17:52:31]] [INFO] RbNtEW6N9T=pass
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [INFO] RbNtEW6N9T=running
[[17:52:27]] [INFO] Executing action 8/590: Tap on Text: "Toys"
[[17:52:26]] [SUCCESS] Screenshot refreshed
[[17:52:26]] [INFO] Refreshing screenshot...
[[17:52:26]] [INFO] ltDXyWvtEz=pass
[[17:52:22]] [SUCCESS] Screenshot refreshed successfully
[[17:52:22]] [SUCCESS] Screenshot refreshed successfully
[[17:52:22]] [INFO] ltDXyWvtEz=running
[[17:52:22]] [INFO] Executing action 7/590: Tap on image: env[device-back-img]
[[17:52:22]] [SUCCESS] Screenshot refreshed
[[17:52:22]] [INFO] Refreshing screenshot...
[[17:52:22]] [INFO] QPKR6jUF9O=pass
[[17:52:18]] [SUCCESS] Screenshot refreshed successfully
[[17:52:18]] [SUCCESS] Screenshot refreshed successfully
[[17:52:18]] [INFO] QPKR6jUF9O=running
[[17:52:18]] [INFO] Executing action 6/590: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[17:52:17]] [SUCCESS] Screenshot refreshed
[[17:52:17]] [INFO] Refreshing screenshot...
[[17:52:17]] [INFO] vfwUVEyq6X=pass
[[17:52:14]] [SUCCESS] Screenshot refreshed successfully
[[17:52:14]] [SUCCESS] Screenshot refreshed successfully
[[17:52:14]] [INFO] vfwUVEyq6X=running
[[17:52:14]] [INFO] Executing action 5/590: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[17:52:13]] [SUCCESS] Screenshot refreshed
[[17:52:13]] [INFO] Refreshing screenshot...
[[17:52:13]] [INFO] Xr6F8gdd8q=pass
[[17:52:09]] [SUCCESS] Screenshot refreshed successfully
[[17:52:09]] [SUCCESS] Screenshot refreshed successfully
[[17:52:09]] [INFO] Xr6F8gdd8q=running
[[17:52:09]] [INFO] Executing action 4/590: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:52:09]] [SUCCESS] Screenshot refreshed
[[17:52:09]] [INFO] Refreshing screenshot...
[[17:52:09]] [INFO] Xr6F8gdd8q=pass
[[17:52:06]] [SUCCESS] Screenshot refreshed successfully
[[17:52:06]] [SUCCESS] Screenshot refreshed successfully
[[17:52:06]] [INFO] Xr6F8gdd8q=running
[[17:52:06]] [INFO] Executing action 3/590: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:52:05]] [SUCCESS] Screenshot refreshed
[[17:52:05]] [INFO] Refreshing screenshot...
[[17:52:05]] [INFO] F4NGh9HrLw=pass
[[17:52:02]] [SUCCESS] Screenshot refreshed successfully
[[17:52:02]] [SUCCESS] Screenshot refreshed successfully
[[17:52:01]] [INFO] F4NGh9HrLw=running
[[17:52:01]] [INFO] Executing action 2/590: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:52:01]] [SUCCESS] Screenshot refreshed
[[17:52:01]] [INFO] Refreshing screenshot...
[[17:52:01]] [INFO] H9fy9qcFbZ=pass
[[17:51:57]] [INFO] Collapsed all test cases
[[17:51:55]] [INFO] H9fy9qcFbZ=running
[[17:51:55]] [INFO] Executing action 1/590: Restart app: env[appid]
[[17:51:55]] [INFO] ExecutionManager: Starting execution of 590 actions...
[[17:51:55]] [SUCCESS] Cleared 1 screenshots from database
[[17:51:55]] [INFO] Clearing screenshots from database before execution...
[[17:51:55]] [SUCCESS] All screenshots deleted successfully
[[17:51:55]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:51:55]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250718_175155/screenshots
[[17:51:55]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250718_175155
[[17:51:55]] [SUCCESS] Report directory initialized successfully
[[17:51:55]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:51:52]] [SUCCESS] All screenshots deleted successfully
[[17:51:52]] [INFO] All actions cleared
[[17:51:52]] [INFO] Cleaning up screenshots...
[[17:51:30]] [SUCCESS] All screenshots deleted successfully
[[17:51:30]] [SUCCESS] Loaded test case "Postcode Flow" with 52 actions
[[17:51:30]] [SUCCESS] Added action: cleanupSteps
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: swipe
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: exists
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: swipe
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: tapAndType
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: tapIfLocatorExists
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: exists
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: exists
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: textClear
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: exists
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: iosFunctions
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: tapIfLocatorExists
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: tapOnText
[[17:51:30]] [SUCCESS] Added action: textClear
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: multiStep
[[17:51:30]] [SUCCESS] Added action: waitTill
[[17:51:30]] [SUCCESS] Added action: iosFunctions
[[17:51:30]] [SUCCESS] Added action: tap
[[17:51:30]] [SUCCESS] Added action: restartApp
[[17:51:30]] [INFO] All actions cleared
[[17:51:30]] [INFO] Cleaning up screenshots...
[[17:51:22]] [SUCCESS] Screenshot refreshed successfully
[[17:51:21]] [SUCCESS] Screenshot refreshed
[[17:51:21]] [INFO] Refreshing screenshot...
[[17:51:20]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:51:20]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:50:58]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:50:57]] [SUCCESS] Found 1 device(s)
[[17:50:56]] [INFO] Refreshing device list...
