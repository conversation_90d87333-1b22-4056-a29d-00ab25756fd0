#!/usr/bin/env python3
"""
Simple script to help download the analysis report
"""

import os
import shutil
from pathlib import Path

def main():
    """Copy report files to a downloads directory"""
    
    # Create downloads directory
    downloads_dir = Path("downloads")
    downloads_dir.mkdir(exist_ok=True)
    
    # Files to copy
    files_to_copy = [
        "Mobile_App_Automation_Session_Management_Analysis.pdf",
        "Mobile_App_Automation_Session_Management_Analysis.md",
        "generate_pdf_report.py"
    ]
    
    print("📄 Mobile App Automation Session Management Analysis Report")
    print("=" * 60)
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            dest_path = downloads_dir / file_name
            shutil.copy2(file_name, dest_path)
            
            # Get file size
            size = os.path.getsize(file_name)
            if size > 1024 * 1024:
                size_str = f"{size / 1024 / 1024:.1f} MB"
            elif size > 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size} bytes"
            
            print(f"✅ {file_name} ({size_str}) -> downloads/{file_name}")
        else:
            print(f"❌ {file_name} not found")
    
    print("\n📁 Files copied to 'downloads' directory")
    print("\n📋 Report Contents:")
    print("   • Comprehensive session management architecture analysis")
    print("   • Appium session initialization patterns")
    print("   • AirTest integration assessment")
    print("   • OpenCV integration analysis")
    print("   • Session management complexity evaluation")
    print("   • Detailed simplification recommendations")
    print("   • Implementation roadmap and risk assessment")
    
    print(f"\n📊 Report Statistics:")
    if os.path.exists("Mobile_App_Automation_Session_Management_Analysis.md"):
        with open("Mobile_App_Automation_Session_Management_Analysis.md", 'r') as f:
            content = f.read()
            lines = len(content.split('\n'))
            words = len(content.split())
            chars = len(content)
        print(f"   • {lines:,} lines")
        print(f"   • {words:,} words") 
        print(f"   • {chars:,} characters")
    
    print(f"\n🎯 Key Findings:")
    print("   • Android platform has significantly more robust session stability")
    print("   • Multiple session access patterns create architectural complexity")
    print("   • Integration challenges between Appium, AirTest, and OpenCV")
    print("   • Opportunities for 30% code reduction through unification")
    
    print(f"\n💡 Main Recommendations:")
    print("   • Implement unified session management strategy")
    print("   • Extend Android stability improvements to iOS")
    print("   • Consolidate multiple session storage patterns")
    print("   • Create platform adapter pattern for code reuse")

if __name__ == "__main__":
    main()