"""
Integration script for session stability improvements

This script applies the session stability enhancements to the existing
AppiumDeviceController implementation for Android platform only.
"""

import os
import sys
import logging
import importlib
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("session_stability_integration")

def apply_session_stability_fixes():
    """
    Apply session stability fixes to Android components only
    
    Returns:
        bool: True if successful, False otherwise
    """
    success = True
    
    try:
        # Step 1: Import and apply session router enhancement
        logger.info("Applying session router enhancements...")
        from session_router_enhanced import apply_session_router_enhancement
        enhanced_router = apply_session_router_enhancement()
        logger.info("Session router enhancement applied successfully")
        
        # Step 2: Import and apply controller enhancements for Android only
        logger.info("Applying Android controller enhancements...")
        try:
            # Import Android controller
            from app_android.utils.appium_device_controller import AppiumDevice<PERSON><PERSON>roll<PERSON> as AndroidController
            
            # Import session stability fix
            from session_stability_fix import apply_session_management
            
            # Apply session management to Android controller
            apply_session_management(AndroidController)
            logger.info("Android controller enhancement applied successfully")
        except Exception as e:
            logger.error(f"Failed to apply Android controller enhancements: {e}")
            logger.error(traceback.format_exc())
            success = False
        
        # Step 3: Update documentation
        logger.info("Updating documentation...")
        update_documentation()
        logger.info("Documentation updated successfully")
        
        return success
    
    except Exception as e:
        logger.error(f"Failed to apply session stability fixes: {e}")
        logger.error(traceback.format_exc())
        return False

def update_documentation():
    """Update documentation with information about the Android session stability fixes"""
    docs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'docs', 'guides')
    
    if not os.path.exists(docs_dir):
        os.makedirs(docs_dir, exist_ok=True)
    
    doc_path = os.path.join(docs_dir, 'SESSION_STABILITY_IMPLEMENTATION.md')
    
    with open(doc_path, 'w') as f:
        f.write("""# Android Session Stability Implementation

## Overview

This document describes the implementation of session stability improvements for the Android automation component of the Mobile Automation Tool. These improvements address persistent session termination issues with Android devices and provide robust recovery mechanisms.

## Components

### 1. Android Session Manager

The `SessionManager` class provides robust session management capabilities specifically for Android:

- **Health Monitoring**: Regular checks to ensure Android Appium session is alive and responsive
- **Automatic Recovery**: Seamless recovery when Android sessions terminate unexpectedly
- **Error Handling**: Comprehensive error categorization and handling for UiAutomator2 sessions
- **Metrics Collection**: Tracking of session health and recovery statistics

### 2. Enhanced Session Router

The `EnhancedSessionRouter` class extends the original session router with:

- **Session Tracking**: Maintains information about active Android sessions
- **Health Monitoring**: Background thread for monitoring Android session health
- **Recovery Coordination**: Coordinates recovery across multiple Android devices
- **Statistics Collection**: Provides insights into Android session health and performance

### 3. Android Controller Integration

Session stability features are integrated with the Android controller:

- **Method Wrapping**: Critical Android controller methods are wrapped with session error handling
- **Transparent Recovery**: Operations automatically retry after Android session recovery
- **Backward Compatibility**: Maintains the same API for existing Android automation code

## Implementation Details

### Android-Specific Session Health Checks

Health checks use lightweight operations to verify Android session health:

```python
def _check_session_health(self):
    # Basic check: session ID exists
    current_session_id = getattr(self.controller.driver, 'session_id', None)
    if not current_session_id:
        return False
    
    # Test 1: Get window size (very lightweight)
    size = self.controller.driver.get_window_size()
    if not size or 'width' not in size or 'height' not in size:
        return False
    
    # Test 2: Check current activity (Android-specific)
    try:
        current_activity = self.controller.driver.current_activity
        if not current_activity:
            logger.debug("Health check warning: No current activity")
    except Exception:
        # Don't fail just on this
        pass
    
    # Test 3: Check UiAutomator2 server status
    if self.uiautomator2_server_check and hasattr(self.controller, 'uiautomator2_helper'):
        try:
            if self.controller.uiautomator2_helper and not self._check_uiautomator2_server():
                logger.warning("Health check warning: UiAutomator2 server not responsive")
        except Exception:
            # Don't fail just on this
            pass
    
    return True
```

### Android Session Recovery

When an Android session fails, recovery follows these steps:

1. Clean up existing driver resources
2. Check and reset UiAutomator2 server if needed
3. Create a new session with enhanced Android-specific capabilities
4. Verify the new session is healthy

```python
def _attempt_recovery(self):
    # Strategy 1: Clean up existing driver
    if self.controller.driver:
        try:
            self.controller.driver.quit()
        except Exception:
            pass
        self.controller.driver = None
    
    # Strategy 2: Check UiAutomator2 server status and reset if needed
    if self._should_reset_uiautomator2():
        self._reset_uiautomator2_server()
        time.sleep(3)  # Wait for server to fully stop
    
    # Strategy 3: Attempt reconnection with enhanced capabilities
    enhanced_options = self.connection_options.copy() if self.connection_options else {}
    
    # Add Android-specific stability enhancements
    android_enhancements = {
        'uiautomator2ServerLaunchTimeout': 120000,
        'uiautomator2ServerInstallTimeout': 120000,
        'adbExecTimeout': 120000,
        'newCommandTimeout': 900,
        # ... other Android-specific options ...
    }
    
    # Merge with existing options
    for key, value in android_enhancements.items():
        if key not in enhanced_options:
            enhanced_options[key] = value
    
    # Attempt reconnection with enhanced options
    return self.controller.connect_to_device(
        self.device_id, 
        enhanced_options,
        'Android'
    )
```

### UiAutomator2 Server Reset

A key component of the recovery process is the ability to reset the UiAutomator2 server:

```python
def _reset_uiautomator2_server(self):
    try:
        # Stop UiAutomator2 server
        self.controller.uiautomator2_helper.shell('am force-stop io.appium.uiautomator2.server')
        self.controller.uiautomator2_helper.shell('am force-stop io.appium.uiautomator2.server.test')
        
        # Clear app data
        self.controller.uiautomator2_helper.shell('pm clear io.appium.uiautomator2.server')
        self.controller.uiautomator2_helper.shell('pm clear io.appium.uiautomator2.server.test')
        
        # Kill any zombie UiAutomator processes
        self.controller.uiautomator2_helper.shell("ps | grep uiautomator | awk '{print $2}' | xargs kill -9")
        
        return True
    except Exception as e:
        logger.error(f"Error resetting UiAutomator2 server: {e}")
        return False
```

## Benefits

- **Improved Android Stability**: Sessions remain stable even during long test runs
- **Reduced Failures**: Automatic recovery prevents test failures due to UiAutomator2 issues
- **Better Diagnostics**: Comprehensive metrics help identify recurring Android-specific issues
- **Simplified Maintenance**: Centralized session management reduces code complexity

## Usage

The Android session stability improvements are automatically applied when the application starts. No changes to existing code are required.

For advanced usage, the session manager provides additional APIs:

```python
# Get session statistics
stats = session_router.get_session_stats()

# Check health of a specific Android session
health = session_router.check_session_health(device_id)

# Force Android session recovery
session_router._recover_session(device_id)
```

## Monitoring

Android session health metrics are available through the session router:

```python
stats = session_router.get_session_stats()
print(f"Active Android sessions: {stats['by_platform']['Android']}")

# Per-session details
for device_id, session in stats['sessions'].items():
    if session['platform'] == 'Android':
        print(f"Android device {device_id}:")
        print(f"  Duration: {session['duration']}")
        print(f"  Health checks: {session['health_checks']}")
        print(f"  Recovery attempts: {session['recovery_attempts']}")
```

## Root Causes Addressed

1. **UiAutomator2 Server Crashes**: Implemented automatic detection and reset of UiAutomator2 server
2. **ADB Connection Issues**: Added robust error handling for ADB connection failures
3. **Inadequate Session Health Monitoring**: Implemented Android-specific health checks
4. **Limited Session Recovery**: Added comprehensive recovery mechanisms for Android sessions
5. **Missing Session Keep-Alive**: Implemented background monitoring to prevent timeout-based terminations

## Conclusion

These Android session stability improvements address the root causes of session termination issues and provide a robust, self-healing session management system. The solution ensures stable test execution while maintaining full backward compatibility with existing Android automation code.""")

def main():
    """Main entry point"""
    logger.info("Starting Android session stability integration...")
    
    if apply_session_stability_fixes():
        logger.info("Android session stability integration completed successfully")
        return 0
    else:
        logger.error("Android session stability integration failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
"""