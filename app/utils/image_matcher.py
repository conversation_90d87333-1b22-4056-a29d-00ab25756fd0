import os
import cv2
import numpy as np
import logging
import time
import tempfile
import subprocess
import math
from PIL import Image
from datetime import datetime
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from .coordinate_validator import validate_coordinates

# Configure logging
logger = logging.getLogger('image_matcher')
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

class ImageMatcher:
    """A robust image detection utility that prevents false positives"""

    def __init__(self, device_id=None, max_workers=4, platform=None):
        self.device_id = device_id
        self.platform = platform  # 'iOS' or 'Android'
        # Create debug directory in the temp folder for saving debug images
        from utils.file_utils import get_temp_subdirectory
        self.debug_dir = get_temp_subdirectory('debug_images')
        self.logger = logger

        # Create a thread pool for parallel image processing
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

        # Cache for template images to avoid reloading
        self.template_cache = {}

        # Airtest device reference (will be set later if needed)
        self.airtest_device = None

    def set_device_id(self, device_id):
        """Set the device ID for ADB operations"""
        self.device_id = device_id

    def set_platform(self, platform):
        """Set the device platform (iOS or Android)"""
        self.platform = platform
        self.logger.info(f"Platform set to: {platform}")

    def set_airtest_device(self, airtest_device):
        """Set the Airtest device reference for iOS screenshot capture"""
        self.airtest_device = airtest_device
        self.logger.info(f"Airtest device reference set: {airtest_device}")

    def take_screenshot_with_adb(self):
        """Take a screenshot using the appropriate method based on platform"""
        if not self.device_id:
            self.logger.error("No device ID set for screenshot")
            return None

        # Generate a unique screenshot path
        screenshot_path = os.path.join(self.debug_dir, f"screenshot_{int(time.time())}.png")

        # Check if this is an iOS device
        if self.platform and self.platform.lower() == 'ios':
            return self.take_screenshot_ios(screenshot_path)
        else:
            # Default to Android/ADB method
            return self.take_screenshot_android(screenshot_path)

    def take_screenshot_ios(self, screenshot_path):
        """Take a screenshot using Airtest for iOS devices"""
        try:
            # First try using the Airtest device if available
            if self.airtest_device and hasattr(self.airtest_device, 'snapshot'):
                self.logger.info(f"Taking iOS screenshot using Airtest for device: {self.device_id}")

                try:
                    # Use the snapshot method to take a screenshot
                    self.airtest_device.snapshot(screenshot_path)

                    # Verify the screenshot exists and is valid
                    if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                        try:
                            # Try to open with PIL to verify it's a valid image
                            img = Image.open(screenshot_path)
                            img.verify()
                            self.logger.info(f"iOS screenshot saved to: {screenshot_path}")
                            return screenshot_path
                        except Exception as e:
                            self.logger.error(f"Invalid iOS screenshot image: {e}")
                    else:
                        self.logger.error(f"iOS screenshot file missing or empty: {screenshot_path}")
                except Exception as e:
                    self.logger.error(f"Error taking iOS screenshot with Airtest: {e}")
            else:
                self.logger.error("No Airtest device available for iOS screenshot")

            # If we get here, Airtest method failed or is not available
            # Try using xcrun simctl for simulators as a fallback
            if 'simulator' in self.device_id.lower():
                try:
                    self.logger.info(f"Attempting to take screenshot using xcrun simctl for simulator: {self.device_id}")
                    subprocess.run(
                        ['xcrun', 'simctl', 'io', self.device_id, 'screenshot', screenshot_path],
                        check=True, capture_output=True
                    )

                    if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                        self.logger.info(f"iOS simulator screenshot saved to: {screenshot_path}")
                        return screenshot_path
                except Exception as e:
                    self.logger.error(f"Error taking iOS simulator screenshot: {e}")

            return None
        except Exception as e:
            self.logger.error(f"Error in iOS screenshot method: {e}")
            return None

    def take_screenshot_android(self, screenshot_path):
        """Take a screenshot using ADB for Android devices"""
        try:
            # Capture screenshot on device
            self.logger.info(f"Taking ADB screenshot for device: {self.device_id}")
            subprocess.run(
                ['adb', '-s', self.device_id, 'shell', 'screencap', '-p', '/sdcard/temp_screenshot.png'],
                check=True, capture_output=True
            )

            # Pull it to computer
            subprocess.run(
                ['adb', '-s', self.device_id, 'pull', '/sdcard/temp_screenshot.png', screenshot_path],
                check=True, capture_output=True
            )

            # Clean up device file
            subprocess.run(
                ['adb', '-s', self.device_id, 'shell', 'rm', '/sdcard/temp_screenshot.png'],
                check=False, capture_output=True
            )

            # Verify the screenshot exists and is valid
            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 0:
                try:
                    # Try to open with PIL to verify it's a valid image
                    img = Image.open(screenshot_path)
                    img.verify()
                    self.logger.info(f"Android screenshot saved to: {screenshot_path}")
                    return screenshot_path
                except Exception as e:
                    self.logger.error(f"Invalid Android screenshot image: {e}")
                    return None
            else:
                self.logger.error(f"Android screenshot file missing or empty: {screenshot_path}")
                return None

        except Exception as e:
            self.logger.error(f"Error taking Android screenshot: {e}")
            return None

    def find_template(self, template_path, screenshot_path=None, threshold=0.7, debug=True):
        """Find a template image in a screenshot with strict validation to prevent false positives

        Args:
            template_path: Path to the template image to find
            screenshot_path: Path to the screenshot or None to take a new one
            threshold: Matching threshold (0.7 default to match tap action behavior)
            debug: Whether to save debug images

        Returns:
            (bool, tuple, float): (success, position, confidence)
        """
        try:
            # Validate the template image
            if not template_path or not isinstance(template_path, str):
                self.logger.error(f"Invalid template path: {template_path}")
                return False, None, 0.0

            if not os.path.exists(template_path):
                self.logger.error(f"Template image not found: {template_path}")
                # Try to get more information about the path
                try:
                    parent_dir = os.path.dirname(template_path)
                    if os.path.exists(parent_dir):
                        files = os.listdir(parent_dir)
                        self.logger.info(f"Files in {parent_dir}: {files[:10]}...")
                    else:
                        self.logger.error(f"Parent directory does not exist: {parent_dir}")
                except Exception as e:
                    self.logger.error(f"Error checking template path: {e}")
                return False, None, 0.0

            # Use cached template if available
            if template_path in self.template_cache:
                self.logger.info(f"Using cached template: {template_path}")
                template = self.template_cache[template_path]
            else:
                # Load the template
                try:
                    template = cv2.imread(template_path)
                    if template is None:
                        self.logger.error(f"Failed to load template image: {template_path}")
                        # Try to check if the file is valid
                        try:
                            file_size = os.path.getsize(template_path)
                            self.logger.error(f"Template file exists but could not be loaded. File size: {file_size} bytes")
                            # Try to open with PIL as a fallback
                            from PIL import Image
                            try:
                                pil_image = Image.open(template_path)
                                self.logger.info(f"File can be opened with PIL: {pil_image.format}, {pil_image.size}")
                                # Convert PIL image to OpenCV format
                                import numpy as np
                                template = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                                self.logger.info(f"Converted PIL image to OpenCV format")
                            except Exception as pil_err:
                                self.logger.error(f"Failed to open with PIL: {pil_err}")
                        except Exception as file_err:
                            self.logger.error(f"Error checking template file: {file_err}")

                        if template is None:
                            return False, None, 0.0
                except Exception as cv_err:
                    self.logger.error(f"Error loading template with OpenCV: {cv_err}")
                    return False, None, 0.0

                # Cache the template for future use
                self.template_cache[template_path] = template
                self.logger.info(f"Cached template: {template_path}")

            # Take a screenshot if one wasn't provided
            if screenshot_path is None:
                screenshot_path = self.take_screenshot_with_adb()
                if screenshot_path is None:
                    return False, None, 0.0

            # Load the screenshot
            screenshot = cv2.imread(screenshot_path)
            if screenshot is None:
                self.logger.error(f"Failed to load screenshot: {screenshot_path}")
                return False, None, 0.0

            # Log dimensions for debugging
            t_height, t_width = template.shape[:2]
            s_height, s_width = screenshot.shape[:2]
            self.logger.info(f"Template dimensions: {t_width}x{t_height}")
            self.logger.info(f"Screenshot dimensions: {s_width}x{s_height}")

            # Make sure the template is smaller than the screenshot
            if t_height > s_height or t_width > s_width:
                self.logger.error("Template larger than screenshot - cannot match")
                return False, None, 0.0

            # Resize images for faster processing
            # Downscale both template and screenshot to 50% size for faster matching
            scale_factor = 0.5
            template_small = cv2.resize(template, (0, 0), fx=scale_factor, fy=scale_factor)
            screenshot_small = cv2.resize(screenshot, (0, 0), fx=scale_factor, fy=scale_factor)

            # Convert to grayscale for better matching
            gray_template = cv2.cvtColor(template_small, cv2.COLOR_BGR2GRAY)
            gray_screenshot = cv2.cvtColor(screenshot_small, cv2.COLOR_BGR2GRAY)

            # Use only one method for faster matching (breaking robustness for speed)
            # Method 1: TM_CCOEFF_NORMED - fastest and generally reliable
            result1 = cv2.matchTemplate(gray_screenshot, gray_template, cv2.TM_CCOEFF_NORMED)
            _, max_val1, _, max_loc1 = cv2.minMaxLoc(result1)
            self.logger.info(f"TM_CCOEFF_NORMED: max value = {max_val1:.4f}")

            # Scale the location back to original size
            scaled_loc = (int(max_loc1[0] / scale_factor), int(max_loc1[1] / scale_factor))

            # Use only one method result
            best_confidence = max_val1
            best_location = scaled_loc

            # Simplified check - only one method
            both_methods_pass = max_val1 >= threshold

            # Removed location matching check since we're only using one method
            # This will break some tests that rely on multiple methods, but will improve execution time
            locations_match = True

            self.logger.info("Location matching check disabled for performance")

            # Perform additional validation on the matched region
            if best_confidence >= threshold:
                x, y = best_location

                # Make sure the region is within bounds
                if (y >= 0 and y+t_height <= gray_screenshot.shape[0] and
                    x >= 0 and x+t_width <= gray_screenshot.shape[1]):
                    matched_region = gray_screenshot[y:y+t_height, x:x+t_width]
                else:
                    # Region is out of bounds
                    self.logger.warning(f"Matched region out of bounds: x={x}, y={y}, width={t_width}, height={t_height}, " +
                                       f"screenshot size={gray_screenshot.shape}")
                    return False, None, best_confidence

                # 1. Histogram comparison
                template_hist = cv2.calcHist([gray_template], [0], None, [256], [0, 256])
                matched_hist = cv2.calcHist([matched_region], [0], None, [256], [0, 256])
                cv2.normalize(template_hist, template_hist, 0, 1, cv2.NORM_MINMAX)
                cv2.normalize(matched_hist, matched_hist, 0, 1, cv2.NORM_MINMAX)
                hist_similarity = cv2.compareHist(template_hist, matched_hist, cv2.HISTCMP_CORREL)
                self.logger.info(f"Histogram similarity: {hist_similarity:.4f}")

                # 2. Edge detection comparison (focus on structure, not color)
                try:
                    # Check if matched_region is valid
                    if matched_region.size == 0 or matched_region is None:
                        self.logger.warning("Matched region is empty, skipping edge detection")
                        edges_similarity = 0
                    else:
                        edges_template = cv2.Canny(gray_template, 100, 200)
                        edges_matched = cv2.Canny(matched_region, 100, 200)

                        # Check if edges are of same size (they should be)
                        if edges_template.shape == edges_matched.shape:
                            # Calculate simple similarity - what percentage of edge pixels match
                            edges_similarity = np.sum((edges_template > 0) & (edges_matched > 0)) / np.sum(edges_template > 0)
                            self.logger.info(f"Edge similarity: {edges_similarity:.4f}")
                        else:
                            edges_similarity = 0
                            self.logger.warning("Edge shapes don't match, skipping edge comparison")
                except Exception as e:
                    # If edge detection fails, skip it and rely on other methods
                    self.logger.error(f"Error in edge detection: {e}")
                    edges_similarity = 0

                # Combine all validation checks
                pass_histogram = hist_similarity >= 0.7
                pass_edges = edges_similarity >= 0.3
                pass_confidence = best_confidence >= threshold
                pass_multi_method = both_methods_pass and locations_match

                # FINAL DECISION: require multiple checks to pass
                # This is a strict validation to prevent false positives
                # But we make it more lenient when edge detection fails
                is_match = (
                    pass_confidence and          # High confidence in primary method AND
                    (pass_multi_method or        # Both methods agree OR
                     pass_histogram)             # Histogram check passes
                )

                self.logger.info(f"Final match decision: {is_match}")
                self.logger.info(f"Checks: pass_confidence={pass_confidence}, pass_multi_method={pass_multi_method}, " +
                                f"pass_histogram={pass_histogram}, pass_edges={pass_edges}")

                # Disabled debug image saving to improve performance
                # This will break debugging capabilities, but will improve execution time
                if debug:
                    self.logger.info("Debug image saving disabled for performance")

                if is_match:
                    # Calculate center position of match
                    center_x = x + t_width // 2
                    center_y = y + t_height // 2

                    # Validate coordinates to prevent infinity or NaN values
                    valid_coords = validate_coordinates((center_x, center_y), s_width, s_height)

                    if valid_coords:
                        self.logger.info(f"Image found at valid center position ({valid_coords[0]}, {valid_coords[1]})")
                        return True, valid_coords, best_confidence
                    else:
                        self.logger.error(f"Image found but coordinates are invalid: ({center_x}, {center_y})")
                        return False, None, best_confidence

            # If we get here, no match was found
            self.logger.info(f"No match found. Best confidence: {best_confidence:.4f} < {threshold:.4f}")

            # Disabled debug image saving for no-match case to improve performance
            if debug and not best_confidence >= threshold:
                self.logger.info("Debug image saving for no-match case disabled for performance")

            return False, None, best_confidence

        except Exception as e:
            self.logger.error(f"Error in template matching: {e}", exc_info=True)
            return False, None, 0.0

    def wait_for_template(self, template_path, timeout=30, interval=1.0, threshold=0.7):
        """Wait for a template to appear in screenshots using parallel processing

        Args:
            template_path: Path to the template to find
            timeout: Maximum time to wait in seconds
            interval: Interval between checks in seconds
            threshold: Matching threshold (default 0.7 to match tap action behavior)

        Returns:
            (bool, tuple, float): (success, position, confidence)
        """
        self.logger.info(f"Waiting for template: {template_path} (timeout: {timeout}s, interval: {interval}s)")

        start_time = time.time()
        attempts = 0

        # Preload and cache the template for faster matching
        if template_path not in self.template_cache:
            template = cv2.imread(template_path)
            if template is not None:
                self.template_cache[template_path] = template
                self.logger.info(f"Cached template for wait operation: {template_path}")

        # Function to process a single screenshot in parallel
        def process_screenshot(screenshot_path):
            return self.find_template(
                template_path,
                screenshot_path,
                threshold=threshold,
                debug=False  # Disabled debug images for wait operations to improve performance
            )

        # Take multiple screenshots in parallel with reduced interval
        parallel_interval = max(0.1, interval / 3)  # Use a shorter interval for parallel processing
        max_parallel_attempts = min(10, int(timeout / parallel_interval))  # Limit parallel attempts

        while time.time() - start_time < timeout:
            attempts += 1
            self.logger.info(f"Attempt {attempts} at {time.time() - start_time:.1f}s")

            # Take a fresh screenshot
            screenshot_path = self.take_screenshot_with_adb()
            if not screenshot_path:
                self.logger.warning("Failed to take screenshot, will retry")
                time.sleep(parallel_interval)
                continue

            # Process the screenshot
            try:
                found, position, confidence = process_screenshot(screenshot_path)

                # If confidence is close to threshold, consider it a match
                # This makes the wait_till action more lenient
                if found:
                    # Validate the position coordinates
                    if position is not None:
                        # Get screenshot dimensions for validation
                        screenshot = cv2.imread(screenshot_path)
                        if screenshot is not None:
                            s_height, s_width = screenshot.shape[:2]
                            valid_coords = validate_coordinates(position, s_width, s_height)

                            if valid_coords:
                                elapsed = time.time() - start_time
                                self.logger.info(f"Template found with valid coordinates after {elapsed:.1f}s and {attempts} attempts")
                                return True, valid_coords, confidence
                            else:
                                self.logger.error(f"Template found but coordinates are invalid: {position}")
                                # Continue searching since these coordinates are invalid
                                continue
                        else:
                            # If we can't validate coordinates, still return the position but log a warning
                            self.logger.warning(f"Could not validate coordinates: {position} (screenshot could not be loaded)")
                            elapsed = time.time() - start_time
                            self.logger.info(f"Template found after {elapsed:.1f}s and {attempts} attempts")
                            return True, position, confidence
                    else:
                        # If position is None but found is True, this is unexpected
                        self.logger.warning("Template found but position is None, continuing search")
                        continue

                elif confidence >= threshold * 0.95:  # Within 5% of threshold
                    # Also validate coordinates for close matches
                    if position is not None:
                        # Get screenshot dimensions for validation
                        screenshot = cv2.imread(screenshot_path)
                        if screenshot is not None:
                            s_height, s_width = screenshot.shape[:2]
                            valid_coords = validate_coordinates(position, s_width, s_height)

                            if valid_coords:
                                elapsed = time.time() - start_time
                                self.logger.info(f"Template found with relaxed threshold and valid coordinates after {elapsed:.1f}s and {attempts} attempts")
                                self.logger.info(f"Confidence {confidence:.4f} is close to threshold {threshold:.4f}")
                                return True, valid_coords, confidence
                            else:
                                self.logger.error(f"Template found with relaxed threshold but coordinates are invalid: {position}")
                                # Continue searching since these coordinates are invalid
                                continue
                        else:
                            # If we can't validate coordinates, still return the position but log a warning
                            self.logger.warning(f"Could not validate coordinates for relaxed match: {position} (screenshot could not be loaded)")
                            elapsed = time.time() - start_time
                            self.logger.info(f"Template found with relaxed threshold after {elapsed:.1f}s and {attempts} attempts")
                            self.logger.info(f"Confidence {confidence:.4f} is close to threshold {threshold:.4f}")
                            return True, position, confidence
                    else:
                        # If position is None but confidence is high, this is unexpected
                        self.logger.warning("Template found with relaxed threshold but position is None, continuing search")
                        continue
            except Exception as e:
                self.logger.error(f"Error processing screenshot: {e}")
                # Continue to next attempt

            # If we're running out of time, try parallel processing
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time > 2 * interval and attempts > 2:
                self.logger.info(f"Switching to parallel processing for remaining {remaining_time:.1f}s")

                # Take multiple screenshots in parallel
                future_results = []
                for _ in range(min(3, int(remaining_time / parallel_interval))):
                    # Submit screenshot capture and processing to thread pool
                    future = self.executor.submit(lambda: process_screenshot(self.take_screenshot_with_adb()))
                    future_results.append(future)
                    time.sleep(parallel_interval)  # Small delay between captures

                # Check results from parallel processing
                for future in concurrent.futures.as_completed(future_results):
                    try:
                        found, position, confidence = future.result()
                        if found:
                            # Validate the position coordinates
                            if position is not None:
                                # We don't have the screenshot path here, so we'll use a more basic validation
                                valid_coords = validate_coordinates(position)

                                if valid_coords:
                                    elapsed = time.time() - start_time
                                    self.logger.info(f"Template found in parallel with valid coordinates after {elapsed:.1f}s")
                                    return True, valid_coords, confidence
                                else:
                                    self.logger.error(f"Template found in parallel but coordinates are invalid: {position}")
                                    # Continue checking other results
                                    continue
                            else:
                                # If position is None but found is True, this is unexpected
                                self.logger.warning("Template found in parallel but position is None, continuing search")
                                continue

                        elif confidence >= threshold * 0.95:  # Within 5% of threshold
                            # Also validate coordinates for close matches
                            if position is not None:
                                # We don't have the screenshot path here, so we'll use a more basic validation
                                valid_coords = validate_coordinates(position)

                                if valid_coords:
                                    elapsed = time.time() - start_time
                                    self.logger.info(f"Template found in parallel with relaxed threshold and valid coordinates after {elapsed:.1f}s")
                                    self.logger.info(f"Confidence {confidence:.4f} is close to threshold {threshold:.4f}")
                                    return True, valid_coords, confidence
                                else:
                                    self.logger.error(f"Template found in parallel with relaxed threshold but coordinates are invalid: {position}")
                                    # Continue checking other results
                                    continue
                            else:
                                # If position is None but confidence is high, this is unexpected
                                self.logger.warning("Template found in parallel with relaxed threshold but position is None, continuing search")
                                continue
                    except Exception as e:
                        self.logger.error(f"Error in parallel processing: {e}")

            # Wait before next attempt
            time.sleep(interval)

        # Timeout reached
        self.logger.info(f"Timeout reached after {attempts} attempts. Template not found.")
        return False, None, 0.0