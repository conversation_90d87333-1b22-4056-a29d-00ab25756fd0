"""
Session Integration Module for Mobile App Automation Tool

This module provides integration utilities to help migrate the existing
app.py session management to use the new unified session management system
while maintaining backward compatibility.

Author: Augment Agent
Date: January 2025
"""

import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps

from .unified_session_manager import unified_session_manager
from .session_registry import session_registry
from .session_errors import SessionError, log_session_error

logger = logging.getLogger(__name__)


class SessionIntegrationHelper:
    """
    Helper class to integrate unified session management with existing code.
    
    This class provides compatibility methods and utilities to ease the
    transition from the old session management to the new unified system.
    """
    
    def __init__(self):
        self.legacy_device_controllers = {}  # For backward compatibility
        self.legacy_players = {}
        self.legacy_action_factories = {}
        
    def migrate_existing_session(self, device_id: str, platform: str, 
                                controller: Any, client_session_id: str = None) -> str:
        """
        Migrate an existing session to the unified session manager.
        
        Args:
            device_id: Device identifier
            platform: Platform name
            controller: Existing controller instance
            client_session_id: Client session identifier
            
        Returns:
            str: Session key in the unified system
        """
        try:
            # Register the existing controller in the unified system
            session_key = session_registry.register(
                device_id=device_id,
                platform=platform,
                controller=controller,
                client_session_id=client_session_id,
                metadata={'migrated': True}
            )
            
            # Store in legacy mappings for backward compatibility
            self.legacy_device_controllers[session_key] = controller
            
            logger.info(f"Migrated existing session to unified system: {session_key}")
            return session_key
            
        except Exception as e:
            logger.error(f"Failed to migrate session for device {device_id}: {e}")
            raise SessionError(f"Session migration failed: {str(e)}")
    
    def get_legacy_controller(self, session_key: str) -> Optional[Any]:
        """Get controller using legacy lookup patterns"""
        # Try unified system first
        controller = unified_session_manager.get_controller(session_key)
        if controller:
            return controller
        
        # Fallback to legacy storage
        return self.legacy_device_controllers.get(session_key)
    
    def create_session_key(self, device_id: str, client_session_id: str) -> str:
        """Create a standardized session key"""
        return f"{device_id}_{client_session_id}"
    
    def parse_session_key(self, session_key: str) -> tuple:
        """Parse a session key into device_id and client_session_id"""
        parts = session_key.split('_', 1)
        if len(parts) == 2:
            return parts[0], parts[1]
        else:
            return parts[0], ""
    
    def ensure_session_exists(self, device_id: str, platform: str, 
                             client_session_id: str = None, 
                             options: Dict[str, Any] = None) -> str:
        """
        Ensure a session exists, creating one if necessary.
        
        Args:
            device_id: Device identifier
            platform: Platform name
            client_session_id: Client session identifier
            options: Session options
            
        Returns:
            str: Session key
        """
        # Check if session already exists
        existing_session = session_registry.get_by_device(device_id)
        if existing_session:
            session_key = f"{device_id}_{existing_session.client_session_id}"
            logger.info(f"Using existing session: {session_key}")
            return session_key
        
        # Create new session
        session_key = unified_session_manager.create_session(
            device_id=device_id,
            platform=platform,
            client_session_id=client_session_id,
            options=options
        )
        
        logger.info(f"Created new session: {session_key}")
        return session_key
    
    def cleanup_legacy_session(self, session_key: str):
        """Clean up legacy session data"""
        if session_key in self.legacy_device_controllers:
            del self.legacy_device_controllers[session_key]
        if session_key in self.legacy_players:
            del self.legacy_players[session_key]
        if session_key in self.legacy_action_factories:
            del self.legacy_action_factories[session_key]
        
        logger.debug(f"Cleaned up legacy session data for: {session_key}")


def session_required(platform_required: str = None):
    """
    Decorator to ensure a valid session exists before executing a function.
    
    Args:
        platform_required: Required platform ('iOS' or 'Android')
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Extract session information from request context
                # This would need to be adapted based on how the Flask app
                # passes session information to the decorated functions
                
                # For now, we'll assume the function has access to session info
                # In a real implementation, this would extract from Flask request
                
                return func(*args, **kwargs)
                
            except SessionError as e:
                log_session_error(e)
                # Return appropriate error response based on the function type
                if hasattr(func, '__name__') and 'api' in func.__name__:
                    # Assume this is an API endpoint
                    from flask import jsonify
                    return jsonify({
                        'status': 'error',
                        'error': str(e),
                        'error_type': e.__class__.__name__
                    }), 500
                else:
                    raise
            
        return wrapper
    return decorator


def get_session_from_request():
    """
    Extract session information from Flask request context.
    
    This function should be called within Flask request context to
    extract device_id and client_session_id from the request.
    
    Returns:
        tuple: (device_id, client_session_id, session_key)
    """
    try:
        from flask import request, session
        
        # Try to get device_id from various sources
        device_id = None
        if request.is_json and request.json:
            device_id = request.json.get('device_id')
        
        if not device_id:
            device_id = request.headers.get('X-Device-ID')
        
        if not device_id:
            device_id = request.args.get('deviceId')
        
        # Get client session ID
        client_session_id = request.headers.get('X-Client-Session-ID')
        if not client_session_id and request.is_json and request.json:
            client_session_id = request.json.get('client_session_id')
        
        if not client_session_id:
            if 'client_session_id' not in session:
                import uuid
                session['client_session_id'] = f"server_{str(uuid.uuid4())[:8]}"
            client_session_id = session['client_session_id']
        
        # Create session key
        session_key = None
        if device_id and client_session_id:
            session_key = f"{device_id}_{client_session_id}"
        
        return device_id, client_session_id, session_key
        
    except Exception as e:
        logger.error(f"Failed to extract session from request: {e}")
        return None, None, None


def get_controller_from_request(platform_hint: str = None) -> Optional[Any]:
    """
    Get device controller from Flask request context.
    
    Args:
        platform_hint: Platform hint for session lookup
        
    Returns:
        Device controller instance or None
    """
    device_id, client_session_id, session_key = get_session_from_request()
    
    if not session_key:
        logger.warning("No session key available from request")
        return None
    
    # Try to get controller from unified session manager
    controller = unified_session_manager.get_controller(session_key)
    if controller:
        return controller
    
    # If no session exists and we have device info, try to create one
    if device_id and platform_hint:
        try:
            session_key = unified_session_manager.create_session(
                device_id=device_id,
                platform=platform_hint,
                client_session_id=client_session_id
            )
            return unified_session_manager.get_controller(session_key)
        except Exception as e:
            logger.error(f"Failed to create session for request: {e}")
    
    return None


def update_global_variables(session_key: str, controller: Any, 
                           player: Any = None, action_factory: Any = None):
    """
    Update global variables for backward compatibility.
    
    This function helps maintain compatibility with existing code that
    relies on global variables for session management.
    
    Args:
        session_key: Session key
        controller: Device controller
        player: Player instance
        action_factory: Action factory instance
    """
    try:
        # This would update the global variables in app.py
        # The actual implementation would depend on how the globals are structured
        
        # For now, we'll just log the update
        logger.debug(f"Updated global variables for session: {session_key}")
        
        # In a real implementation, this might look like:
        # import app
        # app.device_controller = controller
        # app.player = player
        # app.action_factory = action_factory
        
    except Exception as e:
        logger.error(f"Failed to update global variables: {e}")


def create_compatibility_wrapper():
    """
    Create a compatibility wrapper for the existing device_controllers dictionary.
    
    This function returns a dictionary-like object that provides access to
    controllers through the unified session manager while maintaining the
    same interface as the original device_controllers dictionary.
    
    Returns:
        Dictionary-like object for backward compatibility
    """
    class CompatibilityWrapper:
        def __init__(self):
            self.integration_helper = SessionIntegrationHelper()
        
        def __getitem__(self, key):
            return self.integration_helper.get_legacy_controller(key)
        
        def __setitem__(self, key, value):
            # When setting a controller, migrate it to the unified system
            device_id, client_session_id = self.integration_helper.parse_session_key(key)
            if device_id:
                # Determine platform from controller
                platform = 'iOS'  # Default
                if hasattr(value, 'platform_name'):
                    platform = value.platform_name
                
                try:
                    self.integration_helper.migrate_existing_session(
                        device_id, platform, value, client_session_id
                    )
                except Exception as e:
                    logger.error(f"Failed to migrate session {key}: {e}")
                    # Fallback to legacy storage
                    self.integration_helper.legacy_device_controllers[key] = value
        
        def __delitem__(self, key):
            # Clean up both unified and legacy storage
            unified_session_manager.terminate_session(key)
            self.integration_helper.cleanup_legacy_session(key)
        
        def __contains__(self, key):
            controller = self.integration_helper.get_legacy_controller(key)
            return controller is not None
        
        def get(self, key, default=None):
            controller = self.integration_helper.get_legacy_controller(key)
            return controller if controller is not None else default
        
        def keys(self):
            # Return keys from both unified and legacy systems
            unified_keys = [
                f"{s.device_id}_{s.client_session_id}" 
                for s in session_registry.get_all_sessions()
            ]
            legacy_keys = list(self.integration_helper.legacy_device_controllers.keys())
            return list(set(unified_keys + legacy_keys))
        
        def clear(self):
            # Clear both unified and legacy systems
            unified_session_manager.shutdown()
            self.integration_helper.legacy_device_controllers.clear()
            self.integration_helper.legacy_players.clear()
            self.integration_helper.legacy_action_factories.clear()
    
    return CompatibilityWrapper()


# Global integration helper instance
session_integration_helper = SessionIntegrationHelper()
