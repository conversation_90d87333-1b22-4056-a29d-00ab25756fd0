"""
Enhanced Screenshot Management System for Mobile App Automation Tool

This module provides improved screenshot handling with proper action_id parameter
passing, consistent naming conventions, and efficient storage to report folders
without duplication.

Author: Augment Agent
Date: January 2025
"""

import os
import base64
import logging
import threading
import time
from datetime import datetime
from typing import Optional, Dict, Any, Union
from pathlib import Path
import uuid

from .session_registry import session_registry
from .session_errors import SessionError, handle_session_error

logger = logging.getLogger(__name__)


class ScreenshotNamingStrategy:
    """Strategy for screenshot naming conventions"""
    
    @staticmethod
    def action_id_based(action_id: str) -> str:
        """Generate filename based on action_id"""
        if not action_id:
            raise ValueError("action_id is required for action_id_based naming")
        return f"{action_id}.png"
    
    @staticmethod
    def timestamped(prefix: str = "screenshot") -> str:
        """Generate timestamped filename"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        return f"{prefix}_{timestamp}.png"
    
    @staticmethod
    def custom_name(name: str, action_id: str = None) -> str:
        """Generate filename with custom name and optional action_id"""
        # Sanitize the custom name
        safe_name = "".join(c for c in name if c.isalnum() or c in ('-', '_', '.')).rstrip()
        if not safe_name:
            safe_name = "screenshot"
        
        if action_id:
            return f"app-screenshot_{safe_name}_{action_id}.png"
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"app-screenshot_{safe_name}_{timestamp}.png"


class ScreenshotStorage:
    """Handles screenshot storage operations"""
    
    def __init__(self):
        self._lock = threading.RLock()
        self.default_screenshots_dir = None
        self.current_report_dir = None
        self.current_screenshots_dir = None
    
    def set_report_directory(self, report_dir: str, screenshots_dir: str = None):
        """Set the current report directory for screenshot storage"""
        with self._lock:
            self.current_report_dir = report_dir
            if screenshots_dir:
                self.current_screenshots_dir = screenshots_dir
            else:
                self.current_screenshots_dir = os.path.join(report_dir, 'screenshots')
            
            # Ensure screenshots directory exists
            os.makedirs(self.current_screenshots_dir, exist_ok=True)
            logger.info(f"Set screenshot storage directory: {self.current_screenshots_dir}")
    
    def clear_report_directory(self):
        """Clear the current report directory"""
        with self._lock:
            self.current_report_dir = None
            self.current_screenshots_dir = None
            logger.info("Cleared screenshot storage directory")
    
    def get_storage_path(self, filename: str) -> str:
        """Get the full storage path for a screenshot"""
        with self._lock:
            if self.current_screenshots_dir:
                return os.path.join(self.current_screenshots_dir, filename)
            elif self.default_screenshots_dir:
                return os.path.join(self.default_screenshots_dir, filename)
            else:
                # Fallback to a default location
                fallback_dir = os.path.join(os.getcwd(), 'screenshots')
                os.makedirs(fallback_dir, exist_ok=True)
                return os.path.join(fallback_dir, filename)
    
    def save_screenshot_data(self, screenshot_data: Union[bytes, str], filepath: str) -> bool:
        """Save screenshot data to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # Handle base64 encoded data
            if isinstance(screenshot_data, str):
                if screenshot_data.startswith('data:image/png;base64,'):
                    screenshot_data = base64.b64decode(screenshot_data.split(',')[1])
                else:
                    screenshot_data = base64.b64decode(screenshot_data)
            
            # Write to file
            with open(filepath, 'wb') as f:
                f.write(screenshot_data)
            
            logger.debug(f"Saved screenshot data to: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save screenshot data to {filepath}: {e}")
            return False


class EnhancedScreenshotManager:
    """
    Enhanced screenshot manager with improved action_id handling,
    consistent naming, and efficient storage management.
    """
    
    def __init__(self):
        self.storage = ScreenshotStorage()
        self.naming = ScreenshotNamingStrategy()
        self._lock = threading.RLock()
        self.screenshot_history = {}  # Track screenshot history per session
        
        # Initialize default directories
        self._initialize_default_directories()
    
    def _initialize_default_directories(self):
        """Initialize default screenshot directories"""
        try:
            # Try to get from app configuration
            from app import app
            if hasattr(app, 'root_path'):
                default_dir = os.path.join(app.root_path, 'static', 'screenshots')
            else:
                default_dir = os.path.join(os.getcwd(), 'app', 'static', 'screenshots')
            
            os.makedirs(default_dir, exist_ok=True)
            self.storage.default_screenshots_dir = default_dir
            logger.info(f"Initialized default screenshots directory: {default_dir}")
            
        except Exception as e:
            logger.warning(f"Could not initialize default screenshots directory: {e}")
    
    def set_report_context(self, report_dir: str, screenshots_dir: str = None):
        """Set the current report context for screenshot storage"""
        self.storage.set_report_directory(report_dir, screenshots_dir)
    
    def clear_report_context(self):
        """Clear the current report context"""
        self.storage.clear_report_directory()
    
    @handle_session_error(max_retries=2, retry_delay=1.0)
    def take_screenshot(self, controller, action_id: str = None, 
                       custom_name: str = None, session_key: str = None) -> Dict[str, Any]:
        """
        Take a screenshot with enhanced management.
        
        Args:
            controller: Device controller instance
            action_id: Unique action identifier (preferred)
            custom_name: Custom name for the screenshot
            session_key: Session key for tracking
            
        Returns:
            Dict with status, message, and file paths
        """
        try:
            # Validate inputs
            if not controller:
                raise SessionError("No device controller provided")
            
            # Generate action_id if not provided
            if not action_id:
                action_id = self._generate_action_id()
                logger.warning(f"No action_id provided, generated: {action_id}")
            
            # Determine filename strategy
            if custom_name:
                filename = self.naming.custom_name(custom_name, action_id)
            else:
                filename = self.naming.action_id_based(action_id)
            
            # Get storage path
            filepath = self.storage.get_storage_path(filename)
            
            # Take screenshot using controller
            screenshot_result = self._take_controller_screenshot(controller, filepath, action_id)
            
            if screenshot_result.get('status') == 'success':
                # Track screenshot in history
                self._track_screenshot(session_key, action_id, filepath, custom_name)
                
                return {
                    'status': 'success',
                    'message': f'Screenshot saved successfully',
                    'filepath': filepath,
                    'filename': filename,
                    'action_id': action_id,
                    'custom_name': custom_name
                }
            else:
                raise SessionError(f"Controller screenshot failed: {screenshot_result.get('message', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Enhanced screenshot failed: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'action_id': action_id
            }
    
    def _take_controller_screenshot(self, controller, filepath: str, action_id: str) -> Dict[str, Any]:
        """Take screenshot using the device controller"""
        try:
            # Call controller's take_screenshot method with proper parameters
            if hasattr(controller, 'take_screenshot'):
                return controller.take_screenshot(
                    filename=filepath,
                    action_id=action_id
                )
            else:
                raise SessionError("Controller does not support take_screenshot method")
                
        except Exception as e:
            logger.error(f"Controller screenshot failed: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def save_screenshot_data(self, screenshot_data: Union[bytes, str], 
                           action_id: str = None, custom_name: str = None) -> Dict[str, Any]:
        """
        Save screenshot data directly without taking a new screenshot.
        
        Args:
            screenshot_data: Screenshot data (bytes or base64 string)
            action_id: Unique action identifier
            custom_name: Custom name for the screenshot
            
        Returns:
            Dict with status, message, and file paths
        """
        try:
            # Generate action_id if not provided
            if not action_id:
                action_id = self._generate_action_id()
                logger.warning(f"No action_id provided for screenshot data, generated: {action_id}")
            
            # Determine filename
            if custom_name:
                filename = self.naming.custom_name(custom_name, action_id)
            else:
                filename = self.naming.action_id_based(action_id)
            
            # Get storage path
            filepath = self.storage.get_storage_path(filename)
            
            # Save the data
            if self.storage.save_screenshot_data(screenshot_data, filepath):
                return {
                    'status': 'success',
                    'message': 'Screenshot data saved successfully',
                    'filepath': filepath,
                    'filename': filename,
                    'action_id': action_id
                }
            else:
                return {
                    'status': 'error',
                    'message': 'Failed to save screenshot data',
                    'action_id': action_id
                }
                
        except Exception as e:
            logger.error(f"Failed to save screenshot data: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'action_id': action_id
            }
    
    def _generate_action_id(self) -> str:
        """Generate a unique action ID"""
        return str(uuid.uuid4()).replace('-', '')[:10]
    
    def _track_screenshot(self, session_key: str, action_id: str, 
                         filepath: str, custom_name: str = None):
        """Track screenshot in history"""
        if not session_key:
            return
        
        with self._lock:
            if session_key not in self.screenshot_history:
                self.screenshot_history[session_key] = []
            
            self.screenshot_history[session_key].append({
                'action_id': action_id,
                'filepath': filepath,
                'custom_name': custom_name,
                'timestamp': datetime.now(),
                'exists': os.path.exists(filepath)
            })
            
            # Keep only last 50 screenshots per session
            if len(self.screenshot_history[session_key]) > 50:
                self.screenshot_history[session_key] = self.screenshot_history[session_key][-50:]
    
    def get_screenshot_history(self, session_key: str) -> list:
        """Get screenshot history for a session"""
        with self._lock:
            return self.screenshot_history.get(session_key, [])
    
    def cleanup_session_screenshots(self, session_key: str):
        """Clean up screenshot history for a session"""
        with self._lock:
            if session_key in self.screenshot_history:
                del self.screenshot_history[session_key]
                logger.info(f"Cleaned up screenshot history for session: {session_key}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get screenshot manager statistics"""
        with self._lock:
            total_screenshots = sum(len(history) for history in self.screenshot_history.values())
            active_sessions = len(self.screenshot_history)
            
            return {
                'total_screenshots': total_screenshots,
                'active_sessions': active_sessions,
                'current_report_dir': self.storage.current_report_dir,
                'current_screenshots_dir': self.storage.current_screenshots_dir,
                'default_screenshots_dir': self.storage.default_screenshots_dir
            }


# Global enhanced screenshot manager instance
enhanced_screenshot_manager = EnhancedScreenshotManager()
