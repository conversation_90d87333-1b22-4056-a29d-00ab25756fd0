"""
Unified Health Monitoring System for Mobile App Automation Tool

This module provides comprehensive health monitoring for both iOS and Android
sessions, extending the robust Android monitoring to iOS platforms.

Author: Augment Agent
Date: January 2025
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, Callable
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta

from .session_registry import session_registry, SessionInfo
from .session_errors import (
    SessionError, HealthCheckError, SessionTerminatedError,
    log_session_error, ErrorSeverity
)

logger = logging.getLogger(__name__)


@dataclass
class HealthCheckResult:
    """Result of a health check operation"""
    is_healthy: bool
    check_type: str
    message: str
    details: Dict[str, Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class PlatformHealthChecker(ABC):
    """Abstract base class for platform-specific health checkers"""
    
    @abstractmethod
    def check_session_health(self, controller: Any) -> HealthCheckResult:
        """Perform platform-specific health check"""
        pass
    
    @abstractmethod
    def check_driver_health(self, controller: Any) -> HealthCheckResult:
        """Check WebDriver health"""
        pass
    
    @abstractmethod
    def check_server_health(self, controller: Any) -> HealthCheckResult:
        """Check automation server health"""
        pass


class IOSHealthChecker(PlatformHealthChecker):
    """Health checker for iOS sessions"""
    
    def check_session_health(self, controller: Any) -> HealthCheckResult:
        """Comprehensive iOS session health check"""
        try:
            # Test 1: Driver existence and session ID
            if not controller.driver:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message="No active driver instance"
                )
            
            session_id = getattr(controller.driver, 'session_id', None)
            if not session_id:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message="No session ID available"
                )
            
            # Test 2: Basic driver communication
            try:
                window_size = controller.driver.get_window_size()
                if not window_size or 'width' not in window_size or 'height' not in window_size:
                    return HealthCheckResult(
                        is_healthy=False,
                        check_type="session",
                        message="Invalid window size response"
                    )
            except Exception as e:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message=f"Driver communication failed: {str(e)}"
                )
            
            # Test 3: iOS-specific capabilities check
            try:
                capabilities = controller.driver.capabilities
                platform_name = capabilities.get('platformName', '').lower()
                if platform_name != 'ios':
                    return HealthCheckResult(
                        is_healthy=False,
                        check_type="session",
                        message=f"Unexpected platform: {platform_name}"
                    )
            except Exception as e:
                logger.warning(f"Capabilities check failed (non-critical): {e}")
            
            return HealthCheckResult(
                is_healthy=True,
                check_type="session",
                message="iOS session is healthy",
                details={
                    'session_id': session_id,
                    'window_size': window_size,
                    'platform': 'iOS'
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="session",
                message=f"Health check failed: {str(e)}"
            )
    
    def check_driver_health(self, controller: Any) -> HealthCheckResult:
        """Check iOS WebDriver health"""
        try:
            if not controller.driver:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="driver",
                    message="No driver instance"
                )
            
            # Test driver responsiveness with a lightweight operation
            try:
                current_url = controller.driver.current_url
                return HealthCheckResult(
                    is_healthy=True,
                    check_type="driver",
                    message="iOS driver is responsive",
                    details={'current_url': current_url}
                )
            except Exception as e:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="driver",
                    message=f"Driver not responsive: {str(e)}"
                )
                
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="driver",
                message=f"Driver health check failed: {str(e)}"
            )
    
    def check_server_health(self, controller: Any) -> HealthCheckResult:
        """Check WebDriverAgent server health"""
        try:
            # Check if WebDriverAgent is responsive
            if hasattr(controller, 'get_session_info'):
                try:
                    session_info = controller.get_session_info()
                    if session_info and 'wda_status' in session_info:
                        wda_status = session_info['wda_status']
                        return HealthCheckResult(
                            is_healthy=True,
                            check_type="server",
                            message="WebDriverAgent is healthy",
                            details={'wda_status': wda_status}
                        )
                except Exception as e:
                    return HealthCheckResult(
                        is_healthy=False,
                        check_type="server",
                        message=f"WebDriverAgent check failed: {str(e)}"
                    )
            
            # Fallback: basic server check
            return HealthCheckResult(
                is_healthy=True,
                check_type="server",
                message="Server check completed (basic)",
                details={'method': 'fallback'}
            )
            
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="server",
                message=f"Server health check failed: {str(e)}"
            )


class AndroidHealthChecker(PlatformHealthChecker):
    """Health checker for Android sessions (enhanced from existing implementation)"""
    
    def check_session_health(self, controller: Any) -> HealthCheckResult:
        """Comprehensive Android session health check"""
        try:
            # Test 1: Driver and session ID validation
            if not controller.driver:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message="No active driver instance"
                )
            
            session_id = getattr(controller.driver, 'session_id', None)
            if not session_id:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message="No session ID available"
                )
            
            # Test 2: Window size check
            try:
                window_size = controller.driver.get_window_size()
                if not window_size or 'width' not in window_size or 'height' not in window_size:
                    return HealthCheckResult(
                        is_healthy=False,
                        check_type="session",
                        message="Invalid window size response"
                    )
            except Exception as e:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="session",
                    message=f"Window size check failed: {str(e)}"
                )
            
            # Test 3: Android-specific current activity check
            try:
                activity = controller.driver.current_activity
                if not activity:
                    logger.debug("No current activity - warning but not failure")
            except Exception as e:
                logger.debug(f"Activity check failed - warning but not failure: {e}")
            
            return HealthCheckResult(
                is_healthy=True,
                check_type="session",
                message="Android session is healthy",
                details={
                    'session_id': session_id,
                    'window_size': window_size,
                    'platform': 'Android'
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="session",
                message=f"Health check failed: {str(e)}"
            )
    
    def check_driver_health(self, controller: Any) -> HealthCheckResult:
        """Check Android WebDriver health"""
        try:
            if not controller.driver:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="driver",
                    message="No driver instance"
                )
            
            # Test driver with current package
            try:
                current_package = controller.driver.current_package
                return HealthCheckResult(
                    is_healthy=True,
                    check_type="driver",
                    message="Android driver is responsive",
                    details={'current_package': current_package}
                )
            except Exception as e:
                return HealthCheckResult(
                    is_healthy=False,
                    check_type="driver",
                    message=f"Driver not responsive: {str(e)}"
                )
                
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="driver",
                message=f"Driver health check failed: {str(e)}"
            )
    
    def check_server_health(self, controller: Any) -> HealthCheckResult:
        """Check UiAutomator2 server health"""
        try:
            # Check UiAutomator2 server if available
            if hasattr(controller, '_check_uiautomator2_server'):
                try:
                    server_healthy = controller._check_uiautomator2_server()
                    return HealthCheckResult(
                        is_healthy=server_healthy,
                        check_type="server",
                        message="UiAutomator2 server is healthy" if server_healthy else "UiAutomator2 server not responsive",
                        details={'server_type': 'UiAutomator2'}
                    )
                except Exception as e:
                    return HealthCheckResult(
                        is_healthy=False,
                        check_type="server",
                        message=f"UiAutomator2 server check failed: {str(e)}"
                    )
            
            # Fallback: basic server check
            return HealthCheckResult(
                is_healthy=True,
                check_type="server",
                message="Server check completed (basic)",
                details={'method': 'fallback'}
            )
            
        except Exception as e:
            return HealthCheckResult(
                is_healthy=False,
                check_type="server",
                message=f"Server health check failed: {str(e)}"
            )


class UnifiedHealthMonitor:
    """
    Unified health monitoring service for all platforms.
    
    This class provides comprehensive health monitoring that works across
    both iOS and Android platforms with platform-specific optimizations.
    """
    
    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval  # seconds
        self.monitoring_active = False
        self.monitor_thread = None
        self._lock = threading.RLock()
        
        # Platform-specific health checkers
        self.health_checkers = {
            'ios': IOSHealthChecker(),
            'android': AndroidHealthChecker()
        }
        
        # Health check history
        self.health_history: Dict[str, list] = {}
        
        # Recovery callbacks
        self.recovery_callbacks: Dict[str, Callable] = {}
    
    def start_monitoring(self):
        """Start the health monitoring service"""
        with self._lock:
            if self.monitoring_active:
                logger.warning("Health monitoring is already active")
                return
            
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                name="UnifiedHealthMonitor",
                daemon=True
            )
            self.monitor_thread.start()
            logger.info("Unified health monitoring started")
    
    def stop_monitoring(self):
        """Stop the health monitoring service"""
        with self._lock:
            if not self.monitoring_active:
                return
            
            self.monitoring_active = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            logger.info("Unified health monitoring stopped")
    
    def _monitor_worker(self):
        """Background worker for health monitoring"""
        logger.info("Health monitor worker started")
        
        while self.monitoring_active:
            try:
                # Get all active sessions
                sessions = session_registry.get_all_sessions()
                
                for session_info in sessions:
                    if not self.monitoring_active:
                        break
                    
                    try:
                        self._check_session_health(session_info)
                    except Exception as e:
                        logger.error(f"Error checking health for session {session_info.session_id}: {e}")
                
                # Sleep for the check interval
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in health monitor worker: {e}")
                time.sleep(5)  # Short sleep on error
        
        logger.info("Health monitor worker stopped")
    
    def _check_session_health(self, session_info: SessionInfo):
        """Check health of a specific session"""
        session_key = f"{session_info.device_id}_{session_info.client_session_id}"
        platform = session_info.platform.lower()
        
        # Get platform-specific health checker
        health_checker = self.health_checkers.get(platform)
        if not health_checker:
            logger.warning(f"No health checker available for platform: {platform}")
            return
        
        try:
            # Perform comprehensive health check
            session_result = health_checker.check_session_health(session_info.controller)
            driver_result = health_checker.check_driver_health(session_info.controller)
            server_result = health_checker.check_server_health(session_info.controller)
            
            # Determine overall health
            overall_healthy = all([
                session_result.is_healthy,
                driver_result.is_healthy,
                server_result.is_healthy
            ])
            
            # Update session registry
            session_registry.update_health_status(session_key, overall_healthy)
            session_registry.update_activity(session_key)
            
            # Store health check results
            self._store_health_result(session_key, {
                'session': session_result,
                'driver': driver_result,
                'server': server_result,
                'overall_healthy': overall_healthy
            })
            
            # If unhealthy, attempt recovery
            if not overall_healthy:
                logger.warning(f"Session {session_key} is unhealthy")
                self._attempt_recovery(session_info, session_key)
            
        except Exception as e:
            logger.error(f"Health check failed for session {session_key}: {e}")
            session_registry.update_health_status(session_key, False)
    
    def _store_health_result(self, session_key: str, results: Dict[str, Any]):
        """Store health check results in history"""
        if session_key not in self.health_history:
            self.health_history[session_key] = []
        
        # Keep only last 10 results
        history = self.health_history[session_key]
        history.append({
            'timestamp': datetime.now(),
            'results': results
        })
        
        if len(history) > 10:
            history.pop(0)
    
    def _attempt_recovery(self, session_info: SessionInfo, session_key: str):
        """Attempt to recover an unhealthy session"""
        platform = session_info.platform.lower()
        recovery_callback = self.recovery_callbacks.get(platform)
        
        if recovery_callback:
            try:
                logger.info(f"Attempting recovery for session {session_key}")
                recovery_success = recovery_callback(session_info.controller, session_info)
                
                if recovery_success:
                    logger.info(f"Recovery successful for session {session_key}")
                    session_registry.update_health_status(session_key, True)
                else:
                    logger.warning(f"Recovery failed for session {session_key}")
                    session_registry.increment_recovery_attempts(session_key)
                    
            except Exception as e:
                logger.error(f"Recovery attempt failed for session {session_key}: {e}")
                session_registry.increment_recovery_attempts(session_key)
        else:
            logger.warning(f"No recovery callback available for platform: {platform}")
    
    def register_recovery_callback(self, platform: str, callback: Callable):
        """Register a recovery callback for a platform"""
        self.recovery_callbacks[platform.lower()] = callback
        logger.info(f"Registered recovery callback for platform: {platform}")
    
    def get_health_status(self, session_key: str) -> Optional[Dict[str, Any]]:
        """Get the latest health status for a session"""
        return self.health_history.get(session_key, [])[-1] if session_key in self.health_history else None
    
    def get_all_health_status(self) -> Dict[str, Any]:
        """Get health status for all sessions"""
        return {
            session_key: (history[-1] if history else None)
            for session_key, history in self.health_history.items()
        }


# Global health monitor instance
unified_health_monitor = UnifiedHealthMonitor()
