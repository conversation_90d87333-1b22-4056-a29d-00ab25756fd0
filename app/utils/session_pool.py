"""
Session Pooling and Resource Management for Mobile App Automation Tool

This module provides advanced session pooling capabilities and automatic
resource management to improve performance and reduce session creation overhead.

Author: Augment Agent
Date: January 2025
"""

import threading
import time
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import weakref

from .session_registry import session_registry, SessionInfo
from .unified_session_manager import unified_session_manager
from .session_errors import SessionError, SessionTimeoutError

logger = logging.getLogger(__name__)


@dataclass
class PooledSession:
    """Information about a pooled session"""
    session_info: SessionInfo
    created_at: datetime
    last_used: datetime
    use_count: int = 0
    max_idle_time: int = 300  # 5 minutes default
    max_lifetime: int = 3600  # 1 hour default
    is_available: bool = True
    
    def is_expired(self) -> bool:
        """Check if the session has expired"""
        now = datetime.now()
        idle_expired = (now - self.last_used).total_seconds() > self.max_idle_time
        lifetime_expired = (now - self.created_at).total_seconds() > self.max_lifetime
        return idle_expired or lifetime_expired
    
    def mark_used(self):
        """Mark the session as used"""
        self.last_used = datetime.now()
        self.use_count += 1
        self.is_available = False
    
    def mark_available(self):
        """Mark the session as available"""
        self.is_available = True


class SessionPool:
    """
    Session pool for reusing sessions across multiple operations.
    
    This class manages a pool of sessions to reduce the overhead of
    creating and destroying sessions for each operation.
    """
    
    def __init__(self, max_pool_size: int = 10, cleanup_interval: int = 60):
        self.max_pool_size = max_pool_size
        self.cleanup_interval = cleanup_interval
        self._pool: Dict[str, List[PooledSession]] = defaultdict(list)  # platform -> sessions
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._cleanup_active = False
        
        # Statistics
        self.stats = {
            'sessions_created': 0,
            'sessions_reused': 0,
            'sessions_expired': 0,
            'pool_hits': 0,
            'pool_misses': 0
        }
        
        self.start_cleanup_worker()
    
    def start_cleanup_worker(self):
        """Start the background cleanup worker"""
        with self._lock:
            if self._cleanup_active:
                return
            
            self._cleanup_active = True
            self._cleanup_thread = threading.Thread(
                target=self._cleanup_worker,
                name="SessionPoolCleanup",
                daemon=True
            )
            self._cleanup_thread.start()
            logger.info("Session pool cleanup worker started")
    
    def stop_cleanup_worker(self):
        """Stop the background cleanup worker"""
        with self._lock:
            if not self._cleanup_active:
                return
            
            self._cleanup_active = False
            if self._cleanup_thread and self._cleanup_thread.is_alive():
                self._cleanup_thread.join(timeout=5)
            logger.info("Session pool cleanup worker stopped")
    
    def _cleanup_worker(self):
        """Background worker for cleaning up expired sessions"""
        while self._cleanup_active:
            try:
                self._cleanup_expired_sessions()
                time.sleep(self.cleanup_interval)
            except Exception as e:
                logger.error(f"Error in session pool cleanup: {e}")
                time.sleep(5)
    
    def _cleanup_expired_sessions(self):
        """Clean up expired sessions from the pool"""
        with self._lock:
            total_cleaned = 0
            
            for platform, sessions in self._pool.items():
                expired_sessions = []
                
                for pooled_session in sessions[:]:  # Create a copy to iterate
                    if pooled_session.is_expired():
                        expired_sessions.append(pooled_session)
                        sessions.remove(pooled_session)
                        total_cleaned += 1
                
                # Terminate expired sessions
                for pooled_session in expired_sessions:
                    try:
                        session_key = f"{pooled_session.session_info.device_id}_{pooled_session.session_info.client_session_id}"
                        unified_session_manager.terminate_session(session_key)
                        logger.debug(f"Cleaned up expired session: {session_key}")
                    except Exception as e:
                        logger.error(f"Error terminating expired session: {e}")
            
            if total_cleaned > 0:
                self.stats['sessions_expired'] += total_cleaned
                logger.info(f"Cleaned up {total_cleaned} expired sessions from pool")
    
    def get_session(self, device_id: str, platform: str, 
                   client_session_id: str = None, options: Dict[str, Any] = None) -> str:
        """
        Get a session from the pool or create a new one.
        
        Args:
            device_id: Device identifier
            platform: Platform name
            client_session_id: Client session identifier
            options: Session options
            
        Returns:
            str: Session key
        """
        with self._lock:
            platform_lower = platform.lower()
            
            # Try to find an available session in the pool
            available_session = self._find_available_session(platform_lower, device_id)
            
            if available_session:
                # Reuse existing session
                available_session.mark_used()
                session_key = f"{available_session.session_info.device_id}_{available_session.session_info.client_session_id}"
                
                self.stats['sessions_reused'] += 1
                self.stats['pool_hits'] += 1
                
                logger.info(f"Reused pooled session: {session_key}")
                return session_key
            
            # No available session found, create a new one
            self.stats['pool_misses'] += 1
            
            # Check if we can add to pool
            if len(self._pool[platform_lower]) >= self.max_pool_size:
                # Pool is full, create session without pooling
                session_key = unified_session_manager.create_session(
                    device_id=device_id,
                    platform=platform,
                    client_session_id=client_session_id,
                    options=options
                )
                logger.info(f"Created non-pooled session (pool full): {session_key}")
                return session_key
            
            # Create new session and add to pool
            session_key = unified_session_manager.create_session(
                device_id=device_id,
                platform=platform,
                client_session_id=client_session_id,
                options=options
            )
            
            # Add to pool
            session_info = session_registry.get(session_key)
            if session_info:
                pooled_session = PooledSession(
                    session_info=session_info,
                    created_at=datetime.now(),
                    last_used=datetime.now()
                )
                pooled_session.mark_used()
                
                self._pool[platform_lower].append(pooled_session)
                self.stats['sessions_created'] += 1
                
                logger.info(f"Created and pooled new session: {session_key}")
            
            return session_key
    
    def _find_available_session(self, platform: str, device_id: str = None) -> Optional[PooledSession]:
        """Find an available session in the pool"""
        sessions = self._pool.get(platform, [])
        
        for pooled_session in sessions:
            if (pooled_session.is_available and 
                not pooled_session.is_expired() and
                (device_id is None or pooled_session.session_info.device_id == device_id)):
                return pooled_session
        
        return None
    
    def return_session(self, session_key: str):
        """Return a session to the pool for reuse"""
        with self._lock:
            # Find the pooled session
            for platform, sessions in self._pool.items():
                for pooled_session in sessions:
                    session_info_key = f"{pooled_session.session_info.device_id}_{pooled_session.session_info.client_session_id}"
                    if session_info_key == session_key:
                        pooled_session.mark_available()
                        logger.debug(f"Returned session to pool: {session_key}")
                        return
            
            logger.debug(f"Session not found in pool for return: {session_key}")
    
    def remove_session(self, session_key: str):
        """Remove a session from the pool"""
        with self._lock:
            for platform, sessions in self._pool.items():
                for pooled_session in sessions[:]:  # Create copy to iterate
                    session_info_key = f"{pooled_session.session_info.device_id}_{pooled_session.session_info.client_session_id}"
                    if session_info_key == session_key:
                        sessions.remove(pooled_session)
                        logger.debug(f"Removed session from pool: {session_key}")
                        return
    
    def clear_pool(self, platform: str = None):
        """Clear the session pool"""
        with self._lock:
            if platform:
                platform_lower = platform.lower()
                if platform_lower in self._pool:
                    # Terminate all sessions for the platform
                    for pooled_session in self._pool[platform_lower]:
                        try:
                            session_key = f"{pooled_session.session_info.device_id}_{pooled_session.session_info.client_session_id}"
                            unified_session_manager.terminate_session(session_key)
                        except Exception as e:
                            logger.error(f"Error terminating pooled session: {e}")
                    
                    del self._pool[platform_lower]
                    logger.info(f"Cleared session pool for platform: {platform}")
            else:
                # Clear all platforms
                for platform, sessions in self._pool.items():
                    for pooled_session in sessions:
                        try:
                            session_key = f"{pooled_session.session_info.device_id}_{pooled_session.session_info.client_session_id}"
                            unified_session_manager.terminate_session(session_key)
                        except Exception as e:
                            logger.error(f"Error terminating pooled session: {e}")
                
                self._pool.clear()
                logger.info("Cleared all session pools")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get session pool statistics"""
        with self._lock:
            pool_info = {}
            total_sessions = 0
            
            for platform, sessions in self._pool.items():
                available_count = sum(1 for s in sessions if s.is_available)
                in_use_count = len(sessions) - available_count
                
                pool_info[platform] = {
                    'total': len(sessions),
                    'available': available_count,
                    'in_use': in_use_count
                }
                total_sessions += len(sessions)
            
            return {
                'pool_info': pool_info,
                'total_pooled_sessions': total_sessions,
                'max_pool_size': self.max_pool_size,
                'cleanup_active': self._cleanup_active,
                'stats': self.stats.copy()
            }


class ResourceManager:
    """
    Automatic resource management for sessions and related resources.
    
    This class provides automatic cleanup of resources and monitoring
    of resource usage to prevent memory leaks and resource exhaustion.
    """
    
    def __init__(self, check_interval: int = 120):  # 2 minutes
        self.check_interval = check_interval
        self._monitoring_active = False
        self._monitor_thread = None
        self._resource_refs = weakref.WeakSet()
        
        # Resource limits
        self.max_memory_usage = 500 * 1024 * 1024  # 500MB
        self.max_open_files = 100
        
        self.start_monitoring()
    
    def start_monitoring(self):
        """Start resource monitoring"""
        with threading.Lock():
            if self._monitoring_active:
                return
            
            self._monitoring_active = True
            self._monitor_thread = threading.Thread(
                target=self._monitor_worker,
                name="ResourceMonitor",
                daemon=True
            )
            self._monitor_thread.start()
            logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """Stop resource monitoring"""
        with threading.Lock():
            if not self._monitoring_active:
                return
            
            self._monitoring_active = False
            if self._monitor_thread and self._monitor_thread.is_alive():
                self._monitor_thread.join(timeout=5)
            logger.info("Resource monitoring stopped")
    
    def _monitor_worker(self):
        """Background worker for resource monitoring"""
        while self._monitoring_active:
            try:
                self._check_resource_usage()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in resource monitoring: {e}")
                time.sleep(10)
    
    def _check_resource_usage(self):
        """Check current resource usage and cleanup if necessary"""
        try:
            import psutil
            import gc
            
            # Get current process
            process = psutil.Process()
            
            # Check memory usage
            memory_info = process.memory_info()
            if memory_info.rss > self.max_memory_usage:
                logger.warning(f"High memory usage detected: {memory_info.rss / 1024 / 1024:.1f}MB")
                self._cleanup_resources()
            
            # Check open files
            try:
                open_files = len(process.open_files())
                if open_files > self.max_open_files:
                    logger.warning(f"High open file count detected: {open_files}")
                    self._cleanup_resources()
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            # Force garbage collection periodically
            gc.collect()
            
        except ImportError:
            # psutil not available, skip resource monitoring
            pass
        except Exception as e:
            logger.error(f"Error checking resource usage: {e}")
    
    def _cleanup_resources(self):
        """Cleanup resources to free memory"""
        try:
            import gc
            
            # Force garbage collection
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
            
            # Clean up expired sessions from pool
            if hasattr(session_pool, '_cleanup_expired_sessions'):
                session_pool._cleanup_expired_sessions()
            
            # Clean up screenshot history
            from .enhanced_screenshot_manager import enhanced_screenshot_manager
            # Limit screenshot history
            for session_key in list(enhanced_screenshot_manager.screenshot_history.keys()):
                history = enhanced_screenshot_manager.screenshot_history[session_key]
                if len(history) > 20:
                    enhanced_screenshot_manager.screenshot_history[session_key] = history[-20:]
            
        except Exception as e:
            logger.error(f"Error during resource cleanup: {e}")
    
    def register_resource(self, resource):
        """Register a resource for monitoring"""
        self._resource_refs.add(resource)
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """Get resource usage statistics"""
        try:
            import psutil
            
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'memory_usage_mb': memory_info.rss / 1024 / 1024,
                'memory_percent': process.memory_percent(),
                'cpu_percent': process.cpu_percent(),
                'open_files': len(process.open_files()) if hasattr(process, 'open_files') else 0,
                'threads': process.num_threads(),
                'monitoring_active': self._monitoring_active
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception as e:
            return {'error': str(e)}


# Global instances
session_pool = SessionPool()
resource_manager = ResourceManager()
