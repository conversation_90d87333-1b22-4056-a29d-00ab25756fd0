"""
Unified Session Manager for Mobile App Automation Tool

This module provides a centralized session management system that integrates
all session-related components including registry, health monitoring, error
handling, and screenshot management.

Author: Augment Agent
Date: January 2025
"""

import logging
import threading
import time
from typing import Dict, Any, Optional, Callable, Union
from abc import ABC, abstractmethod

from .session_registry import session_registry, SessionInfo
from .unified_health_monitor import unified_health_monitor
from .enhanced_screenshot_manager import enhanced_screenshot_manager
from .session_errors import (
    SessionError, SessionTerminatedError, SessionConnectionError,
    RecoveryFailedError, log_session_error, handle_session_error
)

logger = logging.getLogger(__name__)


class PlatformAdapter(ABC):
    """Abstract base class for platform-specific adapters"""
    
    @abstractmethod
    def create_session(self, device_id: str, options: Dict[str, Any] = None) -> Any:
        """Create a new session for the platform"""
        pass
    
    @abstractmethod
    def get_default_capabilities(self, device_id: str) -> Dict[str, Any]:
        """Get default capabilities for the platform"""
        pass
    
    @abstractmethod
    def validate_connection(self, controller: Any) -> bool:
        """Validate that the connection is working"""
        pass
    
    @abstractmethod
    def attempt_recovery(self, controller: Any, session_info: SessionInfo) -> bool:
        """Attempt to recover a failed session"""
        pass


class IOSPlatformAdapter(PlatformAdapter):
    """Platform adapter for iOS devices"""
    
    def create_session(self, device_id: str, options: Dict[str, Any] = None) -> Any:
        """Create a new iOS session"""
        try:
            # Import iOS controller
            from app.utils.appium_device_controller import AppiumDeviceController
            
            # Create controller instance
            controller = AppiumDeviceController()
            
            # Build capabilities
            capabilities = self.get_default_capabilities(device_id)
            if options:
                capabilities.update(options)
            
            # Connect to device
            success = controller.connect_to_device(device_id, capabilities, 'iOS')
            if not success:
                raise SessionConnectionError(f"Failed to connect to iOS device: {device_id}")
            
            return controller
            
        except Exception as e:
            logger.error(f"Failed to create iOS session for device {device_id}: {e}")
            raise SessionConnectionError(f"iOS session creation failed: {str(e)}", device_id=device_id)
    
    def get_default_capabilities(self, device_id: str) -> Dict[str, Any]:
        """Get default iOS capabilities"""
        return {
            'platformName': 'iOS',
            'automationName': 'XCUITest',
            'udid': device_id,
            'newCommandTimeout': 600,
            'wdaLaunchTimeout': 120000,
            'wdaConnectionTimeout': 120000,
            'useNewWDA': False,
            'shouldTerminateApp': False,
            'forceAppLaunch': False
        }
    
    def validate_connection(self, controller: Any) -> bool:
        """Validate iOS connection"""
        try:
            if not controller or not controller.driver:
                return False
            
            # Test basic driver operation
            window_size = controller.driver.get_window_size()
            return window_size is not None and 'width' in window_size
            
        except Exception as e:
            logger.debug(f"iOS connection validation failed: {e}")
            return False
    
    def attempt_recovery(self, controller: Any, session_info: SessionInfo) -> bool:
        """Attempt to recover iOS session"""
        try:
            logger.info(f"Attempting iOS session recovery for device: {session_info.device_id}")
            
            # Try reconnect if available
            if hasattr(controller, 'reconnect_device'):
                success = controller.reconnect_device()
                if success:
                    logger.info("iOS session recovery successful using reconnect_device")
                    return True
            
            # Try disconnect and reconnect
            if hasattr(controller, 'disconnect'):
                controller.disconnect()
                time.sleep(2)
            
            # Recreate connection
            capabilities = self.get_default_capabilities(session_info.device_id)
            success = controller.connect_to_device(session_info.device_id, capabilities, 'iOS')
            
            if success:
                logger.info("iOS session recovery successful using reconnect")
                return True
            else:
                logger.warning("iOS session recovery failed")
                return False
                
        except Exception as e:
            logger.error(f"iOS session recovery failed: {e}")
            return False


class AndroidPlatformAdapter(PlatformAdapter):
    """Platform adapter for Android devices"""
    
    def create_session(self, device_id: str, options: Dict[str, Any] = None) -> Any:
        """Create a new Android session"""
        try:
            # Import Android controller
            from app_android.utils.appium_device_controller import AppiumDeviceController
            
            # Create controller instance
            controller = AppiumDeviceController()
            
            # Build capabilities
            capabilities = self.get_default_capabilities(device_id)
            if options:
                capabilities.update(options)
            
            # Connect to device
            success = controller.connect_to_device(device_id, capabilities, 'Android')
            if not success:
                raise SessionConnectionError(f"Failed to connect to Android device: {device_id}")
            
            return controller
            
        except Exception as e:
            logger.error(f"Failed to create Android session for device {device_id}: {e}")
            raise SessionConnectionError(f"Android session creation failed: {str(e)}", device_id=device_id)
    
    def get_default_capabilities(self, device_id: str) -> Dict[str, Any]:
        """Get default Android capabilities"""
        return {
            'platformName': 'Android',
            'automationName': 'UiAutomator2',
            'udid': device_id,
            'newCommandTimeout': 900,
            'uiautomator2ServerLaunchTimeout': 120000,
            'uiautomator2ServerInstallTimeout': 120000,
            'adbExecTimeout': 120000,
            'ignoreHiddenApiPolicyError': True,
            'disableWindowAnimation': True,
            'autoGrantPermissions': True,
            'dontStopAppOnReset': True,
            'unicodeKeyboard': True,
            'resetKeyboard': True,
            'skipLogcatCapture': True,
            'enforceXPath1': True,
            'shouldTerminateApp': False,
            'forceAppLaunch': False
        }
    
    def validate_connection(self, controller: Any) -> bool:
        """Validate Android connection"""
        try:
            if not controller or not controller.driver:
                return False
            
            # Test basic driver operation
            window_size = controller.driver.get_window_size()
            return window_size is not None and 'width' in window_size
            
        except Exception as e:
            logger.debug(f"Android connection validation failed: {e}")
            return False
    
    def attempt_recovery(self, controller: Any, session_info: SessionInfo) -> bool:
        """Attempt to recover Android session"""
        try:
            logger.info(f"Attempting Android session recovery for device: {session_info.device_id}")
            
            # Try existing recovery mechanisms if available
            if hasattr(controller, '_attempt_recovery'):
                success = controller._attempt_recovery()
                if success:
                    logger.info("Android session recovery successful using _attempt_recovery")
                    return True
            
            # Try reconnect if available
            if hasattr(controller, 'reconnect_device'):
                success = controller.reconnect_device()
                if success:
                    logger.info("Android session recovery successful using reconnect_device")
                    return True
            
            # Try disconnect and reconnect
            if hasattr(controller, 'disconnect'):
                controller.disconnect()
                time.sleep(3)
            
            # Recreate connection
            capabilities = self.get_default_capabilities(session_info.device_id)
            success = controller.connect_to_device(session_info.device_id, capabilities, 'Android')
            
            if success:
                logger.info("Android session recovery successful using reconnect")
                return True
            else:
                logger.warning("Android session recovery failed")
                return False
                
        except Exception as e:
            logger.error(f"Android session recovery failed: {e}")
            return False


class UnifiedSessionManager:
    """
    Unified session manager that coordinates all session-related operations
    across both iOS and Android platforms.
    """
    
    def __init__(self):
        self._lock = threading.RLock()
        self.platform_adapters = {
            'ios': IOSPlatformAdapter(),
            'android': AndroidPlatformAdapter()
        }
        
        # Register recovery callbacks with health monitor
        unified_health_monitor.register_recovery_callback('ios', self._ios_recovery_callback)
        unified_health_monitor.register_recovery_callback('android', self._android_recovery_callback)
        
        # Start health monitoring
        unified_health_monitor.start_monitoring()
        
        logger.info("Unified session manager initialized")
    
    @handle_session_error(max_retries=2, retry_delay=3.0)
    def create_session(self, device_id: str, platform: str, 
                      client_session_id: str = None, options: Dict[str, Any] = None) -> str:
        """
        Create a new session for a device.
        
        Args:
            device_id: Device identifier
            platform: Platform name ('iOS' or 'Android')
            client_session_id: Client session identifier
            options: Additional session options
            
        Returns:
            str: Session key for accessing the session
        """
        with self._lock:
            platform_lower = platform.lower()
            
            # Get platform adapter
            adapter = self.platform_adapters.get(platform_lower)
            if not adapter:
                raise SessionError(f"Unsupported platform: {platform}")
            
            # Check if session already exists
            existing_session = session_registry.get_by_device(device_id)
            if existing_session:
                logger.info(f"Session already exists for device {device_id}, reusing")
                session_key = f"{device_id}_{existing_session.client_session_id}"
                return session_key
            
            # Create new session
            try:
                controller = adapter.create_session(device_id, options)
                
                # Validate connection
                if not adapter.validate_connection(controller):
                    raise SessionConnectionError(f"Connection validation failed for device: {device_id}")
                
                # Register session
                session_key = session_registry.register(
                    device_id=device_id,
                    platform=platform,
                    controller=controller,
                    client_session_id=client_session_id,
                    metadata={'adapter': platform_lower, 'options': options or {}}
                )
                
                logger.info(f"Created new session: {session_key} for device {device_id} ({platform})")
                return session_key
                
            except Exception as e:
                logger.error(f"Failed to create session for device {device_id}: {e}")
                raise
    
    def get_session(self, session_key: str) -> Optional[SessionInfo]:
        """Get session information by session key"""
        return session_registry.get(session_key)
    
    def get_controller(self, session_key: str) -> Optional[Any]:
        """Get controller for a session"""
        return session_registry.get_controller(session_key)
    
    def terminate_session(self, session_key: str) -> bool:
        """
        Terminate a session and clean up resources.
        
        Args:
            session_key: Session key to terminate
            
        Returns:
            bool: True if session was terminated successfully
        """
        with self._lock:
            session_info = session_registry.get(session_key)
            if not session_info:
                logger.warning(f"Session not found for termination: {session_key}")
                return False
            
            try:
                # Disconnect controller
                if session_info.controller and hasattr(session_info.controller, 'disconnect'):
                    session_info.controller.disconnect()
                
                # Clean up screenshot history
                enhanced_screenshot_manager.cleanup_session_screenshots(session_key)
                
                # Unregister from session registry
                session_registry.unregister(session_key)
                
                logger.info(f"Terminated session: {session_key}")
                return True
                
            except Exception as e:
                logger.error(f"Error terminating session {session_key}: {e}")
                return False
    
    def restart_session(self, session_key: str) -> bool:
        """
        Restart a session by terminating and recreating it.
        
        Args:
            session_key: Session key to restart
            
        Returns:
            bool: True if session was restarted successfully
        """
        session_info = session_registry.get(session_key)
        if not session_info:
            logger.error(f"Session not found for restart: {session_key}")
            return False
        
        try:
            # Store session details
            device_id = session_info.device_id
            platform = session_info.platform
            client_session_id = session_info.client_session_id
            options = session_info.metadata.get('options', {})
            
            # Terminate existing session
            self.terminate_session(session_key)
            
            # Create new session
            new_session_key = self.create_session(device_id, platform, client_session_id, options)
            
            logger.info(f"Restarted session: {session_key} -> {new_session_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restart session {session_key}: {e}")
            return False
    
    def get_all_sessions(self) -> list:
        """Get all active sessions"""
        return session_registry.get_all_sessions()
    
    def get_sessions_by_platform(self, platform: str) -> list:
        """Get all sessions for a specific platform"""
        return session_registry.get_sessions_by_platform(platform)
    
    def get_health_status(self, session_key: str) -> Optional[Dict[str, Any]]:
        """Get health status for a session"""
        return unified_health_monitor.get_health_status(session_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive session manager statistics"""
        registry_stats = session_registry.get_stats()
        health_stats = unified_health_monitor.get_all_health_status()
        screenshot_stats = enhanced_screenshot_manager.get_stats()
        
        return {
            'registry': registry_stats,
            'health_monitoring': {
                'active': unified_health_monitor.monitoring_active,
                'check_interval': unified_health_monitor.check_interval,
                'sessions_monitored': len(health_stats)
            },
            'screenshots': screenshot_stats,
            'platform_adapters': list(self.platform_adapters.keys())
        }
    
    def _ios_recovery_callback(self, controller: Any, session_info: SessionInfo) -> bool:
        """Recovery callback for iOS sessions"""
        adapter = self.platform_adapters['ios']
        return adapter.attempt_recovery(controller, session_info)
    
    def _android_recovery_callback(self, controller: Any, session_info: SessionInfo) -> bool:
        """Recovery callback for Android sessions"""
        adapter = self.platform_adapters['android']
        return adapter.attempt_recovery(controller, session_info)
    
    def set_report_context(self, report_dir: str, screenshots_dir: str = None):
        """Set report context for screenshot management"""
        enhanced_screenshot_manager.set_report_context(report_dir, screenshots_dir)

    def clear_report_context(self):
        """Clear report context for screenshot management"""
        enhanced_screenshot_manager.clear_report_context()

    def take_screenshot(self, session_key: str, action_id: str = None,
                       custom_name: str = None) -> Dict[str, Any]:
        """
        Take a screenshot using the enhanced screenshot manager.

        Args:
            session_key: Session key to identify the controller
            action_id: Unique action identifier
            custom_name: Custom name for the screenshot

        Returns:
            Dict with screenshot result
        """
        controller = self.get_controller(session_key)
        if not controller:
            return {
                'status': 'error',
                'message': f'No controller found for session: {session_key}'
            }

        return enhanced_screenshot_manager.take_screenshot(
            controller=controller,
            action_id=action_id,
            custom_name=custom_name,
            session_key=session_key
        )

    def shutdown(self):
        """Shutdown the session manager and clean up all resources"""
        logger.info("Shutting down unified session manager")

        # Stop health monitoring
        unified_health_monitor.stop_monitoring()

        # Terminate all sessions
        sessions = session_registry.get_all_sessions()
        for session_info in sessions:
            session_key = f"{session_info.device_id}_{session_info.client_session_id}"
            self.terminate_session(session_key)

        # Clear registry
        session_registry.clear_all()

        logger.info("Unified session manager shutdown complete")


# Global unified session manager instance
unified_session_manager = UnifiedSessionManager()
