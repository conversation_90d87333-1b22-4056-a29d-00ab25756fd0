"""
Standardized Error Types and Handling for Mobile App Automation Tool

This module defines a hierarchy of session-related errors and provides
decorators for consistent error handling across the application.

Author: Augment Agent
Date: January 2025
"""

import functools
import logging
import time
from typing import Callable, Any, Optional, Type, Union
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SessionError(Exception):
    """Base class for all session-related errors"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                 recoverable: bool = True, original_error: Exception = None):
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.recoverable = recoverable
        self.original_error = original_error
        self.timestamp = time.time()
    
    def __str__(self):
        return f"{self.__class__.__name__}: {self.message}"


class SessionTerminatedError(SessionError):
    """Session was terminated unexpectedly"""
    
    def __init__(self, message: str = "Session was terminated unexpectedly", 
                 session_id: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.HIGH, recoverable=True, **kwargs)
        self.session_id = session_id


class SessionTimeoutError(SessionError):
    """Session operation timed out"""
    
    def __init__(self, message: str = "Session operation timed out", 
                 timeout_duration: float = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.MEDIUM, recoverable=True, **kwargs)
        self.timeout_duration = timeout_duration


class SessionConnectionError(SessionError):
    """Failed to establish or maintain session connection"""
    
    def __init__(self, message: str = "Failed to establish session connection", 
                 device_id: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.HIGH, recoverable=True, **kwargs)
        self.device_id = device_id


class PlatformError(SessionError):
    """Platform-specific error"""
    
    def __init__(self, message: str, platform: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.MEDIUM, recoverable=True, **kwargs)
        self.platform = platform


class RecoveryFailedError(SessionError):
    """Session recovery failed after multiple attempts"""
    
    def __init__(self, message: str = "Session recovery failed", 
                 attempts: int = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.CRITICAL, recoverable=False, **kwargs)
        self.attempts = attempts


class DriverError(SessionError):
    """WebDriver-related error"""
    
    def __init__(self, message: str, driver_command: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.MEDIUM, recoverable=True, **kwargs)
        self.driver_command = driver_command


class HealthCheckError(SessionError):
    """Health check failed"""
    
    def __init__(self, message: str = "Session health check failed", 
                 check_type: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.MEDIUM, recoverable=True, **kwargs)
        self.check_type = check_type


class ConfigurationError(SessionError):
    """Configuration or capability error"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(message, severity=ErrorSeverity.HIGH, recoverable=False, **kwargs)
        self.config_key = config_key


def categorize_error(error: Exception) -> Type[SessionError]:
    """
    Categorize a generic exception into a specific SessionError type.
    
    Args:
        error: The original exception
        
    Returns:
        SessionError subclass that best represents the error
    """
    error_str = str(error).lower()
    error_type = type(error).__name__.lower()
    
    # Session termination patterns
    if any(pattern in error_str for pattern in [
        'session', 'terminated', 'closed', 'invalid session id', 'no such session'
    ]):
        return SessionTerminatedError
    
    # Timeout patterns
    if any(pattern in error_str for pattern in [
        'timeout', 'timed out', 'connection timeout', 'read timeout'
    ]):
        return SessionTimeoutError
    
    # Connection patterns
    if any(pattern in error_str for pattern in [
        'connection', 'refused', 'unreachable', 'network', 'socket'
    ]):
        return SessionConnectionError
    
    # Driver patterns
    if any(pattern in error_str for pattern in [
        'webdriver', 'driver', 'element not found', 'no such element'
    ]):
        return DriverError
    
    # Platform-specific patterns
    if any(pattern in error_str for pattern in [
        'ios', 'android', 'xcuitest', 'uiautomator', 'webdriveragent'
    ]):
        return PlatformError
    
    # Default to generic SessionError
    return SessionError


def handle_session_error(max_retries: int = 3, retry_delay: float = 2.0, 
                        recovery_callback: Callable = None):
    """
    Decorator for standardized session error handling with automatic recovery.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retry attempts in seconds
        recovery_callback: Optional callback function for session recovery
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            last_error = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(self, *args, **kwargs)
                    
                except Exception as e:
                    last_error = e
                    
                    # Categorize the error
                    error_class = categorize_error(e)
                    session_error = error_class(
                        message=str(e),
                        original_error=e
                    )
                    
                    logger.warning(f"Attempt {attempt + 1}/{max_retries + 1} failed: {session_error}")
                    
                    # If this is the last attempt or error is not recoverable, raise it
                    if attempt == max_retries or not session_error.recoverable:
                        if attempt == max_retries:
                            raise RecoveryFailedError(
                                f"Failed after {max_retries + 1} attempts: {str(e)}",
                                attempts=max_retries + 1,
                                original_error=e
                            )
                        else:
                            raise session_error
                    
                    # Attempt recovery if callback is provided
                    if recovery_callback and session_error.recoverable:
                        try:
                            logger.info(f"Attempting recovery for {session_error.__class__.__name__}")
                            recovery_success = recovery_callback(self, session_error)
                            if not recovery_success:
                                logger.warning("Recovery callback returned False")
                        except Exception as recovery_error:
                            logger.error(f"Recovery failed: {recovery_error}")
                    
                    # Wait before retry (except on last attempt)
                    if attempt < max_retries:
                        time.sleep(retry_delay)
            
            # This should never be reached, but just in case
            raise RecoveryFailedError(
                f"Unexpected error after {max_retries + 1} attempts",
                attempts=max_retries + 1,
                original_error=last_error
            )
        
        return wrapper
    return decorator


def log_session_error(error: SessionError, context: dict = None):
    """
    Log a session error with appropriate level and context.
    
    Args:
        error: SessionError instance to log
        context: Additional context information
    """
    context = context or {}
    
    # Determine log level based on severity
    if error.severity == ErrorSeverity.CRITICAL:
        log_level = logging.CRITICAL
    elif error.severity == ErrorSeverity.HIGH:
        log_level = logging.ERROR
    elif error.severity == ErrorSeverity.MEDIUM:
        log_level = logging.WARNING
    else:
        log_level = logging.INFO
    
    # Build log message
    message = f"{error.__class__.__name__}: {error.message}"
    if context:
        message += f" | Context: {context}"
    if error.original_error:
        message += f" | Original: {error.original_error}"
    
    logger.log(log_level, message)


def create_session_error_handler(recovery_manager=None):
    """
    Create a session error handler with a specific recovery manager.
    
    Args:
        recovery_manager: Recovery manager instance
        
    Returns:
        Configured error handling decorator
    """
    def recovery_callback(controller_instance, session_error):
        if recovery_manager and hasattr(recovery_manager, 'attempt_recovery'):
            return recovery_manager.attempt_recovery(controller_instance, session_error)
        return False
    
    return handle_session_error(recovery_callback=recovery_callback)


# Convenience decorators for common scenarios
def handle_driver_errors(func: Callable) -> Callable:
    """Decorator specifically for WebDriver operations"""
    return handle_session_error(max_retries=2, retry_delay=1.0)(func)


def handle_connection_errors(func: Callable) -> Callable:
    """Decorator specifically for connection operations"""
    return handle_session_error(max_retries=3, retry_delay=3.0)(func)


def handle_health_check_errors(func: Callable) -> Callable:
    """Decorator specifically for health check operations"""
    return handle_session_error(max_retries=1, retry_delay=0.5)(func)
