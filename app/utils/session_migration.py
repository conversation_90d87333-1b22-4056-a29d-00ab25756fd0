"""
Session Migration Utilities for Mobile App Automation Tool

This module provides utilities to migrate the existing app.py session management
to use the new unified session management system with minimal disruption.

Author: Augment Agent
Date: January 2025
"""

import logging
import os
import sys
from typing import Dict, Any, Optional

# Add the app directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
app_dir = os.path.dirname(current_dir)
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

from .unified_session_manager import unified_session_manager
from .session_integration import session_integration_helper, create_compatibility_wrapper
from .enhanced_screenshot_manager import enhanced_screenshot_manager

logger = logging.getLogger(__name__)


class SessionMigrationManager:
    """
    Manages the migration from legacy session management to unified session management.
    
    This class provides methods to gradually migrate existing sessions and
    integrate the new system with minimal code changes.
    """
    
    def __init__(self):
        self.migration_active = False
        self.compatibility_wrapper = None
        self.original_device_controllers = None
        
    def start_migration(self, app_module=None):
        """
        Start the migration process by replacing the device_controllers dictionary
        with a compatibility wrapper.
        
        Args:
            app_module: The app module (usually imported app.py)
        """
        try:
            if self.migration_active:
                logger.warning("Migration already active")
                return
            
            # Create compatibility wrapper
            self.compatibility_wrapper = create_compatibility_wrapper()
            
            # If app module is provided, replace the device_controllers
            if app_module and hasattr(app_module, 'device_controllers'):
                self.original_device_controllers = app_module.device_controllers
                
                # Migrate existing sessions
                self._migrate_existing_sessions(self.original_device_controllers)
                
                # Replace with compatibility wrapper
                app_module.device_controllers = self.compatibility_wrapper
                
                logger.info("Replaced device_controllers with compatibility wrapper")
            
            self.migration_active = True
            logger.info("Session migration started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start session migration: {e}")
            raise
    
    def _migrate_existing_sessions(self, device_controllers: Dict[str, Any]):
        """Migrate existing sessions to the unified system"""
        migrated_count = 0
        
        for session_key, controller in device_controllers.items():
            try:
                # Parse session key
                device_id, client_session_id = session_integration_helper.parse_session_key(session_key)
                
                # Determine platform
                platform = self._detect_platform(controller)
                
                # Migrate session
                new_session_key = session_integration_helper.migrate_existing_session(
                    device_id=device_id,
                    platform=platform,
                    controller=controller,
                    client_session_id=client_session_id
                )
                
                migrated_count += 1
                logger.info(f"Migrated session: {session_key} -> {new_session_key}")
                
            except Exception as e:
                logger.error(f"Failed to migrate session {session_key}: {e}")
        
        logger.info(f"Migrated {migrated_count} existing sessions")
    
    def _detect_platform(self, controller: Any) -> str:
        """Detect platform from controller instance"""
        try:
            # Check for platform_name attribute
            if hasattr(controller, 'platform_name'):
                platform = controller.platform_name.lower()
                if platform in ['ios', 'android']:
                    return platform.title()
            
            # Check module path
            module_path = controller.__class__.__module__
            if 'app_android' in module_path:
                return 'Android'
            elif 'app' in module_path and 'android' not in module_path:
                return 'iOS'
            
            # Check capabilities if available
            if hasattr(controller, 'driver') and controller.driver:
                try:
                    capabilities = controller.driver.capabilities
                    platform_name = capabilities.get('platformName', '').lower()
                    if platform_name in ['ios', 'android']:
                        return platform_name.title()
                except Exception:
                    pass
            
            # Default to iOS for backward compatibility
            logger.warning(f"Could not detect platform for controller {controller}, defaulting to iOS")
            return 'iOS'
            
        except Exception as e:
            logger.error(f"Error detecting platform: {e}")
            return 'iOS'
    
    def stop_migration(self, app_module=None):
        """
        Stop the migration and restore original session management if needed.
        
        Args:
            app_module: The app module to restore
        """
        try:
            if not self.migration_active:
                logger.warning("Migration not active")
                return
            
            # Restore original device_controllers if available
            if app_module and self.original_device_controllers is not None:
                app_module.device_controllers = self.original_device_controllers
                logger.info("Restored original device_controllers")
            
            self.migration_active = False
            logger.info("Session migration stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop session migration: {e}")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get the current migration status"""
        return {
            'migration_active': self.migration_active,
            'unified_sessions': len(unified_session_manager.get_all_sessions()),
            'legacy_sessions': len(session_integration_helper.legacy_device_controllers),
            'health_monitoring_active': unified_session_manager.unified_health_monitor.monitoring_active,
            'screenshot_manager_stats': enhanced_screenshot_manager.get_stats()
        }


def apply_session_management_patches():
    """
    Apply patches to integrate session management with existing code.
    
    This function modifies key functions in the app to use the new
    session management system while maintaining backward compatibility.
    """
    try:
        # Import the app module
        import app
        
        # Create migration manager
        migration_manager = SessionMigrationManager()
        
        # Start migration
        migration_manager.start_migration(app)
        
        # Patch key functions
        _patch_connect_device_function(app)
        _patch_disconnect_device_function(app)
        _patch_screenshot_functions(app)
        
        logger.info("Applied session management patches successfully")
        return migration_manager
        
    except Exception as e:
        logger.error(f"Failed to apply session management patches: {e}")
        raise


def _patch_connect_device_function(app_module):
    """Patch the connect_device function to use unified session management"""
    try:
        # Store original function
        original_connect = getattr(app_module, 'connect_device', None)
        if not original_connect:
            logger.warning("connect_device function not found in app module")
            return
        
        def enhanced_connect_device():
            """Enhanced connect_device function using unified session management"""
            try:
                from flask import request, jsonify
                from .session_integration import get_session_from_request
                
                # Get session information from request
                device_id, client_session_id, session_key = get_session_from_request()
                
                if not device_id:
                    return jsonify({"status": "error", "error": "Missing device_id parameter"}), 400
                
                # Get platform from request
                data = request.get_json() or {}
                platform = data.get('platform', 'iOS')  # Default to iOS
                
                # Create or get existing session
                session_key = unified_session_manager.create_session(
                    device_id=device_id,
                    platform=platform,
                    client_session_id=client_session_id
                )
                
                # Get controller
                controller = unified_session_manager.get_controller(session_key)
                if not controller:
                    return jsonify({"status": "error", "error": "Failed to create session"}), 500
                
                # Update global variables for backward compatibility
                app_module.device_controller = controller
                app_module.current_device_id = device_id
                
                # Take initial screenshot
                screenshot_result = controller.take_screenshot()
                
                return jsonify({
                    "status": "connected",
                    "device_id": device_id,
                    "session_key": session_key,
                    "platform": platform,
                    "screenshot_available": screenshot_result.get('status') == 'success' if screenshot_result else False
                })
                
            except Exception as e:
                logger.error(f"Enhanced connect_device failed: {e}")
                # Fallback to original function
                return original_connect()
        
        # Replace the function
        setattr(app_module, 'connect_device', enhanced_connect_device)
        logger.info("Patched connect_device function")
        
    except Exception as e:
        logger.error(f"Failed to patch connect_device function: {e}")


def _patch_disconnect_device_function(app_module):
    """Patch the disconnect_device function to use unified session management"""
    try:
        # Store original function
        original_disconnect = getattr(app_module, 'disconnect_device', None)
        if not original_disconnect:
            logger.warning("disconnect_device function not found in app module")
            return
        
        def enhanced_disconnect_device():
            """Enhanced disconnect_device function using unified session management"""
            try:
                from flask import request, jsonify
                from .session_integration import get_session_from_request
                
                # Get session information from request
                device_id, client_session_id, session_key = get_session_from_request()
                
                if session_key:
                    # Terminate session using unified manager
                    success = unified_session_manager.terminate_session(session_key)
                    
                    if success:
                        # Clear global variables
                        app_module.device_controller = None
                        app_module.current_device_id = None
                        
                        return jsonify({"status": "disconnected"})
                    else:
                        return jsonify({"status": "error", "error": "Failed to terminate session"}), 500
                else:
                    return jsonify({"status": "disconnected", "message": "No active session found"})
                
            except Exception as e:
                logger.error(f"Enhanced disconnect_device failed: {e}")
                # Fallback to original function
                return original_disconnect()
        
        # Replace the function
        setattr(app_module, 'disconnect_device', enhanced_disconnect_device)
        logger.info("Patched disconnect_device function")
        
    except Exception as e:
        logger.error(f"Failed to patch disconnect_device function: {e}")


def _patch_screenshot_functions(app_module):
    """Patch screenshot-related functions to use enhanced screenshot management"""
    try:
        # Patch take_screenshot function if it exists
        original_take_screenshot = getattr(app_module, 'take_screenshot', None)
        if original_take_screenshot:
            
            def enhanced_take_screenshot():
                """Enhanced take_screenshot function"""
                try:
                    from flask import request, jsonify
                    from .session_integration import get_session_from_request
                    
                    # Get session information
                    device_id, client_session_id, session_key = get_session_from_request()
                    
                    if not session_key:
                        return jsonify({"status": "error", "error": "No active session"}), 400
                    
                    # Get action_id from request
                    data = request.get_json() or {}
                    action_id = data.get('action_id')
                    custom_name = data.get('custom_name')
                    
                    # Take screenshot using unified manager
                    result = unified_session_manager.take_screenshot(
                        session_key=session_key,
                        action_id=action_id,
                        custom_name=custom_name
                    )
                    
                    if result['status'] == 'success':
                        return jsonify({
                            "status": "success",
                            "path": result.get('filepath'),
                            "action_id": result.get('action_id')
                        })
                    else:
                        return jsonify({
                            "status": "error",
                            "error": result.get('message', 'Screenshot failed')
                        }), 500
                        
                except Exception as e:
                    logger.error(f"Enhanced take_screenshot failed: {e}")
                    # Fallback to original function
                    return original_take_screenshot()
            
            setattr(app_module, 'take_screenshot', enhanced_take_screenshot)
            logger.info("Patched take_screenshot function")
        
    except Exception as e:
        logger.error(f"Failed to patch screenshot functions: {e}")


# Global migration manager instance
migration_manager = None


def initialize_session_management():
    """
    Initialize the enhanced session management system.
    
    This function should be called during app startup to enable
    the new session management features.
    """
    global migration_manager
    
    try:
        # Apply patches and start migration
        migration_manager = apply_session_management_patches()
        
        logger.info("Enhanced session management initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize enhanced session management: {e}")
        return False


def get_migration_status() -> Dict[str, Any]:
    """Get the current migration status"""
    if migration_manager:
        return migration_manager.get_migration_status()
    else:
        return {'migration_active': False, 'error': 'Migration manager not initialized'}
