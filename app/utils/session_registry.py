"""
Unified Session Registry for Mobile App Automation Tool

This module provides a centralized, thread-safe registry for managing all active
Appium sessions across both iOS and Android platforms. It serves as the single
source of truth for session management.

Author: Augment Agent
Date: January 2025
"""

import threading
import time
import uuid
from typing import Dict, Optional, Any, List
from dataclasses import dataclass, field
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class SessionInfo:
    """Information about an active session"""
    session_id: str
    device_id: str
    platform: str  # 'iOS' or 'Android'
    client_session_id: str
    controller: Any  # AppiumDeviceController instance
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    health_checks: int = 0
    recovery_attempts: int = 0
    is_healthy: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class SessionRegistry:
    """
    Thread-safe registry for managing all active Appium sessions.
    
    This class provides a centralized way to store, retrieve, and manage
    sessions across multiple devices and platforms.
    """
    
    def __init__(self):
        self._sessions: Dict[str, SessionInfo] = {}
        self._lock = threading.RLock()
        self._device_to_session: Dict[str, str] = {}  # device_id -> session_key mapping
        
    def register(self, device_id: str, platform: str, controller: Any, 
                 client_session_id: str = None, metadata: Dict[str, Any] = None) -> str:
        """
        Register a new session in the registry.
        
        Args:
            device_id: Unique device identifier
            platform: Platform name ('iOS' or 'Android')
            controller: AppiumDeviceController instance
            client_session_id: Client session identifier (optional)
            metadata: Additional session metadata (optional)
            
        Returns:
            str: Session key for accessing the session
        """
        with self._lock:
            # Generate session key
            if client_session_id:
                session_key = f"{device_id}_{client_session_id}"
            else:
                session_key = f"{device_id}_{str(uuid.uuid4())[:8]}"
            
            # Create session info
            session_info = SessionInfo(
                session_id=str(uuid.uuid4()),
                device_id=device_id,
                platform=platform,
                client_session_id=client_session_id or "",
                controller=controller,
                metadata=metadata or {}
            )
            
            # Store session
            self._sessions[session_key] = session_info
            self._device_to_session[device_id] = session_key
            
            logger.info(f"Registered new session: {session_key} for device {device_id} ({platform})")
            return session_key
    
    def get(self, session_key: str) -> Optional[SessionInfo]:
        """
        Get session information by session key.
        
        Args:
            session_key: Session key to look up
            
        Returns:
            SessionInfo if found, None otherwise
        """
        with self._lock:
            return self._sessions.get(session_key)
    
    def get_by_device(self, device_id: str) -> Optional[SessionInfo]:
        """
        Get session information by device ID.
        
        Args:
            device_id: Device ID to look up
            
        Returns:
            SessionInfo if found, None otherwise
        """
        with self._lock:
            session_key = self._device_to_session.get(device_id)
            if session_key:
                return self._sessions.get(session_key)
            return None
    
    def get_controller(self, session_key: str) -> Optional[Any]:
        """
        Get the controller for a session.
        
        Args:
            session_key: Session key to look up
            
        Returns:
            Controller instance if found, None otherwise
        """
        session_info = self.get(session_key)
        return session_info.controller if session_info else None
    
    def unregister(self, session_key: str) -> bool:
        """
        Unregister a session from the registry.
        
        Args:
            session_key: Session key to remove
            
        Returns:
            bool: True if session was removed, False if not found
        """
        with self._lock:
            session_info = self._sessions.get(session_key)
            if session_info:
                # Remove from both mappings
                del self._sessions[session_key]
                if session_info.device_id in self._device_to_session:
                    del self._device_to_session[session_info.device_id]
                
                logger.info(f"Unregistered session: {session_key} for device {session_info.device_id}")
                return True
            return False
    
    def update_activity(self, session_key: str) -> bool:
        """
        Update the last activity timestamp for a session.
        
        Args:
            session_key: Session key to update
            
        Returns:
            bool: True if updated, False if session not found
        """
        with self._lock:
            session_info = self._sessions.get(session_key)
            if session_info:
                session_info.last_activity = datetime.now()
                return True
            return False
    
    def update_health_status(self, session_key: str, is_healthy: bool, 
                           increment_checks: bool = True) -> bool:
        """
        Update the health status of a session.
        
        Args:
            session_key: Session key to update
            is_healthy: Current health status
            increment_checks: Whether to increment health check counter
            
        Returns:
            bool: True if updated, False if session not found
        """
        with self._lock:
            session_info = self._sessions.get(session_key)
            if session_info:
                session_info.is_healthy = is_healthy
                if increment_checks:
                    session_info.health_checks += 1
                if is_healthy:
                    session_info.recovery_attempts = 0  # Reset on successful health check
                return True
            return False
    
    def increment_recovery_attempts(self, session_key: str) -> int:
        """
        Increment the recovery attempt counter for a session.
        
        Args:
            session_key: Session key to update
            
        Returns:
            int: New recovery attempt count, -1 if session not found
        """
        with self._lock:
            session_info = self._sessions.get(session_key)
            if session_info:
                session_info.recovery_attempts += 1
                return session_info.recovery_attempts
            return -1
    
    def get_all_sessions(self) -> List[SessionInfo]:
        """
        Get all active sessions.
        
        Returns:
            List of all SessionInfo objects
        """
        with self._lock:
            return list(self._sessions.values())
    
    def get_sessions_by_platform(self, platform: str) -> List[SessionInfo]:
        """
        Get all sessions for a specific platform.
        
        Args:
            platform: Platform name ('iOS' or 'Android')
            
        Returns:
            List of SessionInfo objects for the platform
        """
        with self._lock:
            return [session for session in self._sessions.values() 
                   if session.platform.lower() == platform.lower()]
    
    def clear_all(self) -> int:
        """
        Clear all sessions from the registry.
        
        Returns:
            int: Number of sessions that were cleared
        """
        with self._lock:
            count = len(self._sessions)
            self._sessions.clear()
            self._device_to_session.clear()
            logger.info(f"Cleared all {count} sessions from registry")
            return count
    
    def get_session_keys(self) -> List[str]:
        """
        Get all session keys.
        
        Returns:
            List of session keys
        """
        with self._lock:
            return list(self._sessions.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get registry statistics.
        
        Returns:
            Dictionary with registry statistics
        """
        with self._lock:
            total_sessions = len(self._sessions)
            healthy_sessions = sum(1 for s in self._sessions.values() if s.is_healthy)
            platforms = {}
            
            for session in self._sessions.values():
                platform = session.platform
                if platform not in platforms:
                    platforms[platform] = 0
                platforms[platform] += 1
            
            return {
                'total_sessions': total_sessions,
                'healthy_sessions': healthy_sessions,
                'unhealthy_sessions': total_sessions - healthy_sessions,
                'platforms': platforms,
                'session_keys': list(self._sessions.keys())
            }


# Global session registry instance
session_registry = SessionRegistry()
