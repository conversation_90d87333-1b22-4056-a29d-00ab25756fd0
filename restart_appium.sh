#!/bin/bash
# Script to restart Appium server with optimized settings for session stability

echo "Restarting Appium server with optimized settings for session stability..."

# Kill existing Appium processes
echo "Stopping existing Appium processes..."
pkill -f appium || true
sleep 2

# Check if node_modules/.bin/appium exists (local installation)
if [ -f "./node_modules/.bin/appium" ]; then
    APPIUM_CMD="./node_modules/.bin/appium"
    echo "Using local Appium installation"
else
    APPIUM_CMD="appium"
    echo "Using global Appium installation"
fi

# Create log directory if it doesn't exist
mkdir -p logs

# Start Appium with optimized settings
echo "Starting Appium server with optimized settings..."
$APPIUM_CMD \
    --base-path /wd/hub \
    --port 4723 \
    --relaxed-security \
    --use-drivers xcuitest,uiautomator2 \
    --use-plugins=images,execute-driver \
    --session-override \
    --allow-cors \
    --allow-insecure chromedriver_autodownload \
    --log-timestamp \
    --log-no-colors \
    --log ./logs/appium_$(date +%Y%m%d_%H%M%S).log \
    --debug-log-spacing \
    --no-perms-check \
    --default-capabilities '{"newCommandTimeout": 900, "sessionOverride": true, "clearSystemFiles": true}' \
    > ./logs/appium_stdout.log 2>&1 &

APPIUM_PID=$!
echo "Appium started with PID: $APPIUM_PID"

# Wait for Appium to start
echo "Waiting for Appium server to start..."
for i in {1..10}; do
    if curl -s http://localhost:4723/wd/hub/status > /dev/null; then
        echo "Appium server started successfully"
        exit 0
    fi
    sleep 1
done

echo "Failed to start Appium server"
exit 1