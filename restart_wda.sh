#!/bin/bash
# <PERSON>ript to restart UiAutomator2 server for Android devices
# This script helps recover from UiAutomator2 server crashes that cause Appium session instability

# Check if device ID is provided
if [ -z "$1" ]; then
    echo "Usage: $0 <device_id>"
    echo "Example: $0 PJTCI7EMSSONYPU8"
    exit 1
fi

DEVICE_ID=$1
echo "Restarting UiAutomator2 server for Android device $DEVICE_ID..."

# Check if adb is available
if ! command -v adb &> /dev/null; then
    echo "Error: adb not found. Please install Android SDK Platform Tools."
    exit 1
fi

# Check if device is connected
if ! adb -s $DEVICE_ID get-state &> /dev/null; then
    echo "Error: Device $DEVICE_ID not found or not connected."
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Log file for this operation
LOG_FILE="logs/uiautomator2_restart_$(date +%Y%m%d_%H%M%S).log"
echo "Logging to $LOG_FILE"

# Function to log messages
log() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") - $1" | tee -a "$LOG_FILE"
}

# Check for zombie UiAutomator processes
log "Checking for zombie UiAutomator processes..."
ZOMBIE_PROCESSES=$(adb -s $DEVICE_ID shell "ps | grep uiautomator" | tee -a "$LOG_FILE")
if [ -n "$ZOMBIE_PROCESSES" ]; then
    log "Found UiAutomator processes, killing them..."
    adb -s $DEVICE_ID shell "ps | grep uiautomator | awk '{print \$2}' | xargs kill -9" >> "$LOG_FILE" 2>&1
fi

# Stop UiAutomator2 server
log "Stopping UiAutomator2 server..."
adb -s $DEVICE_ID shell am force-stop io.appium.uiautomator2.server >> "$LOG_FILE" 2>&1
adb -s $DEVICE_ID shell am force-stop io.appium.uiautomator2.server.test >> "$LOG_FILE" 2>&1
sleep 2

# Clear app data
log "Clearing UiAutomator2 server data..."
adb -s $DEVICE_ID shell pm clear io.appium.uiautomator2.server >> "$LOG_FILE" 2>&1
adb -s $DEVICE_ID shell pm clear io.appium.uiautomator2.server.test >> "$LOG_FILE" 2>&1
sleep 1

# Check if UiAutomator2 server is installed
log "Checking UiAutomator2 server installation..."
if ! adb -s $DEVICE_ID shell pm list packages | grep -q io.appium.uiautomator2.server; then
    log "UiAutomator2 server not installed. It will be installed automatically when you connect with Appium."
else
    log "UiAutomator2 server is installed."
    
    # Get app version for diagnostics
    SERVER_VERSION=$(adb -s $DEVICE_ID shell dumpsys package io.appium.uiautomator2.server | grep versionName | head -1)
    log "UiAutomator2 server version: $SERVER_VERSION"
fi

# Check device status
log "Checking device status..."
DEVICE_INFO=$(adb -s $DEVICE_ID shell getprop | grep -E 'model|version.sdk|manufacturer|hardware' | tee -a "$LOG_FILE")
log "Device info: $DEVICE_INFO"

# Check for system issues
log "Checking for system issues..."
MEMORY_INFO=$(adb -s $DEVICE_ID shell "free -m" | tee -a "$LOG_FILE")
log "Memory info: $MEMORY_INFO"

# Restart ADB server only if needed
if ! adb devices | grep -q "$DEVICE_ID"; then
    log "Device not responding, restarting ADB server..."
    adb kill-server >> "$LOG_FILE" 2>&1
    sleep 2
    adb start-server >> "$LOG_FILE" 2>&1
    sleep 1
else
    log "ADB connection is stable, no need to restart ADB server."
fi

# Verify device is still connected
if adb -s $DEVICE_ID get-state | grep -q "device"; then
    log "Device $DEVICE_ID is connected and ready."
    log "UiAutomator2 server has been reset. Next Appium session will reinstall and start the server if needed."
    
    # Optionally try to start the UiAutomator2 server manually
    log "Attempting to start UiAutomator2 server manually..."
    adb -s $DEVICE_ID shell am start -n io.appium.uiautomator2.server.test/androidx.test.runner.AndroidJUnitRunner >> "$LOG_FILE" 2>&1
    
    log "Recovery completed successfully."
    exit 0
else
    log "Warning: Device $DEVICE_ID is not in 'device' state. Please check connection."
    exit 1
fi