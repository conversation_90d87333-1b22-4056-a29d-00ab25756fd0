{"name": "Others", "created": "2025-07-14 16:13:27", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3417ms", "package_id": "env[appid]", "timestamp": 1746597492636, "type": "restartApp"}, {"action_id": "Dzn2Q7JTe0", "executionTime": "2512ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "method": "locator", "timeout": 20, "timestamp": 1747038761695, "type": "tap"}, {"action_id": "RlDZFks4Lc", "executionTime": "463ms", "function_name": "alert_accept", "timestamp": 1747038830836, "type": "iosFunctions"}, {"action_id": "F4NGh9HrLw", "executionTime": "1454ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Barcode Scanner\"]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "exists"}, {"action_id": "RbNtEW6N9T", "executionTime": "1346ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgHelp\"]", "text_to_find": "Living", "timeout": 10, "timestamp": 1746830828429, "type": "exists"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "1346ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]", "text_to_find": "Shop", "timeout": 10, "timestamp": 1746830873534, "type": "exists"}, {"action_id": "74XW7x54ad", "executionTime": "3139ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749457134837, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "2519ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746833833911, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2398ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "method": "locator", "start_x": 50, "start_y": 70, "text_to_find": "Track", "timeout": 10, "timestamp": 1746835476969, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OmKfD9iBjD", "double_tap": false, "executionTime": "2379ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Order number\"]", "method": "locator", "text_to_find": "Order", "timeout": 10, "timestamp": 1746835134218, "type": "tap"}, {"action_id": "7YbjwQH1Jc", "executionTime": "1656ms", "text": "env[searchorder]", "timestamp": 1747039240064, "type": "text"}, {"action_id": "kAQ1yIIw3h", "executionTime": "2488ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email Address\"]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "XJv08Gkucs", "enter": false, "executionTime": "1851ms", "text": "<EMAIL>", "timestamp": 1747039307941, "type": "text"}, {"action_id": "aNN0yYFLEd", "executionTime": "2530ms", "function_name": "text", "image_filename": "prodcut-share-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Search for order\"]", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1746831868369, "type": "tap"}, {"action_id": "83tV9A4NOn", "executionTime": "1552ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"refunded\"]", "timeout": 20, "timestamp": *************, "type": "exists"}, {"action_id": "VJJ3EXXotU", "executionTime": "2194ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"action_id": "gekNSY5O2E", "double_tap": false, "executionTime": "2433ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "text_to_find": "Sign", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "s0WyiD1w0B", "executionTime": "1190ms", "function_name": "alert_accept", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "iosFunctions"}, {"action_id": "u928vFzSni", "executionTime": "2781ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "pe9W6tZdXT", "enter": true, "executionTime": "3306ms", "function_name": "text", "text": "env[uname-op]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "d6vTfR4Y0D", "executionTime": "2759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "A57bC3QuEM", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "ShJSdXvmVL", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "3815ms", "interval": 1, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "start_x": 50, "start_y": 70, "timeout": 20, "timestamp": *************, "type": "exists", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "2261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "DhWa2PCBXE", "executionTime": "2593ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]", "method": "locator", "timeout": 20, "timestamp": *************, "type": "exists"}, {"action_id": "GEMv6goQtW", "executionTime": "2231ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"action_id": "inrxgdWzXr", "double_tap": false, "executionTime": "3020ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "text_to_find": "receipts", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "inrxgdWzXr", "double_tap": false, "executionTime": "3163ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "text_to_find": "Store", "timeout": 30, "timestamp": 1747039844368, "type": "tapOnText"}, {"action_id": "P4b2BITpCf", "executionTime": "1511ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 20, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "zdh8hKYC1a", "executionTime": "2429ms", "image_filename": "deviceback-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746832601515, "type": "tap"}, {"action_id": "q6cKxgMAIn", "executionTime": "1753ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"<PERSON><PERSON><PERSON> Receipt\"]/XCUIElementTypeOther[2]", "method": "locator", "timeout": 20, "timestamp": 1746832657816, "type": "exists"}, {"action_id": "XjclKOaCTh", "executionTime": "2829ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746832702071, "type": "tap"}, {"action_id": "U48qCNydwd", "executionTime": "3278ms", "package_id": "env[appid]", "timestamp": 1747042054721, "type": "restartApp"}, {"action_id": "UoH0wdtcLk", "double_tap": false, "executionTime": "3989ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1747042435305, "type": "tapOnText"}, {"action_id": "jmKjclMUWT", "executionTime": "3022ms", "text_to_find": "current", "timeout": 30, "timestamp": 1747042463732, "type": "tapOnText"}, {"type": "tapIfLocatorExists", "timestamp": 1752473542612, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnUpdate\"]", "timeout": 10, "action_id": "ysJIY9A3gq"}, {"action_id": "tWq2Qzn22D", "executionTime": "2246ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749457147347, "type": "tap"}, {"action_id": "Jtn2FK4THX", "double_tap": false, "executionTime": "4031ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1747042224985, "type": "tapOnText"}, {"action_id": "qG4RkNac30", "enter": true, "executionTime": "2565ms", "function_name": "text", "text": "P_42691341", "timestamp": 1749110826538, "type": "iosFunctions"}, {"action_id": "8XWyF2kgwW", "executionTime": "1865ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 40, "timestamp": 1748149052897, "type": "waitTill"}, {"action_id": "CcFsA41sKp", "executionTime": "2381ms", "image_filename": "quickadd-to-bag-btn.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748088918053, "type": "tap"}, {"action_id": "ALWzI9hXIc", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 50, "timestamp": 1751703306376, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5]}, {"action_id": "hlB6ARmBVC", "image_filename": "add-to-bag-ip14.png", "threshold": 0.7, "timeout": 5, "timestamp": 1751703126675, "type": "tapIfImageExists"}, {"action_id": "XOaZPEqzKU", "locator_type": "accessibility_id", "locator_value": "Add to bag", "timeout": 10, "timestamp": 1751703273268, "type": "tapIfLocatorExists"}, {"type": "tapIfLocatorExists", "timestamp": 1752473582790, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Save my location\"]", "timeout": 10, "action_id": "7mnBGa2GCk"}, {"action_id": "F4NGh9HrLw", "executionTime": "2346ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1747042715213, "type": "tap"}, {"action_id": "ZlrZ0BjA1R", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780996818, "type": "tapIfLocatorExists"}, {"action_id": "ZxObWodIp8", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Delivery_Buy_Steps_20250512194232.json", "test_case_name": "Delivery Buy Steps", "test_case_steps": [{"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 40, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Q5A0cNaJ24", "executionTime": "3031ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "method": "locator", "timeout": 10, "timestamp": 1745486308596, "type": "tap"}, {"action_id": "h9trcMrvxt", "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486361281, "type": "tap"}, {"action_id": "CLMmkV1OIM", "delay": 500, "executionTime": "3392ms", "function_name": "text", "text": "First Name", "timestamp": 1745486374043, "type": "textClear"}, {"action_id": "p8rfQL9ara", "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Last Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "QvuueoTR8W", "delay": 500, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "timestamp": 1745486416273, "type": "textClear"}, {"action_id": "9B5MQGTmpP", "executionTime": "3080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745486441044, "type": "tap"}, {"action_id": "lWJtKSqlPS", "delay": 500, "executionTime": "3483ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745486452706, "type": "textClear"}, {"action_id": "yi5EsHEFvc", "executionTime": "3080ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Mobile number\"]", "method": "locator", "timeout": 10, "timestamp": 1745486486775, "type": "tap"}, {"action_id": "SFj4Aa7RHQ", "delay": 500, "executionTime": "3252ms", "function_name": "text", "text": "0400000000", "timestamp": 1745486504243, "type": "textClear"}, {"action_id": "kDpsm2D3xt", "enter": true, "executionTime": "2985ms", "function_name": "text", "text": " ", "timestamp": 1745570305956, "type": "iosFunctions"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "tapOnText"}, {"action_id": "sdqCYvk2Du", "method": "coordinates", "text": "env[deliver-address]", "timeout": 60, "timestamp": 1749460676763, "type": "tapAndType", "x": "env[delivery-addr-x]", "y": "env[delivery-addr-y]"}, {"action_id": "NcU6aex76k", "executionTime": "1807ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746616991897, "type": "tap"}, {"action_id": "mMnRNh3NEd", "image_filename": "env[delivery-address-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749457829278, "type": "tap"}, {"action_id": "TTpwkHEyuE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044105004, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "1Lirmyxkft", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "method": "locator", "timeout": 10, "timestamp": 1747044123748, "type": "tap"}, {"action_id": "6LQ5cq0f6N", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044256988, "type": "tap"}, {"action_id": "CBBib3pFkq", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044349502, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "ftA0OJvd0W", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "method": "locator", "timeout": 10, "timestamp": 1747044370730, "type": "tap"}, {"action_id": "mfOWujfRpL", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]", "timeout": 10, "timestamp": 1747044439860, "type": "exists"}, {"action_id": "XLpUP3Wr93", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"close\"]", "method": "locator", "timeout": 10, "timestamp": 1747044533430, "type": "tap"}, {"action_id": "dkSs61jGvX", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044584329, "type": "tap"}, {"action_id": "GN587Y6VBQ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"Pay in 4\"]", "method": "locator", "timeout": 10, "timestamp": 1747044637222, "type": "tap"}, {"action_id": "wSdfNe4Kww", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Pay in 4 with PayPal\"]", "timeout": 10, "timestamp": 1747044696695, "type": "exists"}, {"action_id": "TzPItWbvDR", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"close\"]", "method": "locator", "timeout": 10, "timestamp": 1747044707774, "type": "tap"}, {"action_id": "YBT2MVclAv", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044719683, "type": "tap"}, {"action_id": "9Pwdq32eUk", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "method": "locator", "timeout": 10, "timestamp": 1747044771806, "type": "tap"}, {"action_id": "lSG7un0qKK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeImage[@name=\"Afterpay\"]", "timeout": 10, "timestamp": 1747044824024, "type": "exists"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1745982487511, "type": "tap"}, {"action_id": "YqmO7h7VP0", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UgjXUTZy7Z", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "TAKgcEDqvz", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Sign in with your Zip account\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 40, "timestamp": *************, "type": "waitTill"}, {"action_id": "2bcxKJ2cPg", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "start_x": 50, "start_y": 70, "timestamp": 1748156595054, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "test_case_steps_count": 41, "timestamp": 1749458010692, "type": "multiStep"}, {"action_id": "F4NGh9HrLw", "executionTime": "2515ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1747041764396, "type": "tap"}, {"action_id": "qHdMgerbTE", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2758ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748313766307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "2269ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "x4yLCZHaCR", "executionTime": "1045ms", "package_id": "env[appid]", "timestamp": 1746834909467, "type": "terminateApp"}, {"action_id": "OMgc2gHHyq", "display_depth": 0, "executionTime": "23226ms", "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751004578210, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-14 16:13:27"}